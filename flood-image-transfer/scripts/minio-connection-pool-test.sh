#!/bin/bash

# MinIO连接池测试脚本
# 验证MinIO连接池配置和性能提升效果

SERVER_URL=${1:-"http://localhost:8080"}
TEST_PROFILE=${2:-"test"}

echo "=== MinIO连接池测试 ==="
echo "服务器地址: $SERVER_URL"
echo "测试环境: $TEST_PROFILE"
echo "测试目标: 验证MinIO连接池配置和性能提升"
echo ""

# 检查服务器是否可用
echo "检查服务器连接..."
if ! curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo "警告: 无法连接到服务器 $SERVER_URL"
    echo "请确保服务已启动并且地址正确"
    echo ""
fi

# MinIO连接池状态检查函数
check_connection_pool_status() {
    echo "=== MinIO连接池状态检查 ==="
    
    # 检查连接池状态
    local status_response=$(curl -s "$SERVER_URL/api/minio/connection-pool/status")
    
    if command -v jq > /dev/null 2>&1; then
        echo "连接池状态:"
        echo "$status_response" | jq '.'
        
        local success=$(echo "$status_response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            echo "✅ MinIO连接池状态正常"
            
            local status=$(echo "$status_response" | jq -r '.status // "N/A"')
            local config=$(echo "$status_response" | jq -r '.config // "N/A"')
            local performance=$(echo "$status_response" | jq -r '.performance // "N/A"')
            
            echo "- 连接池状态: $status"
            echo "- 配置信息: $config"
            echo "- 性能统计: $performance"
        else
            echo "❌ MinIO连接池状态异常"
            local error=$(echo "$status_response" | jq -r '.error // "未知错误"')
            echo "- 错误信息: $error"
        fi
    else
        echo "连接池状态响应:"
        echo "$status_response"
    fi
    
    echo ""
}

# MinIO连接池性能测试函数
test_connection_pool_performance() {
    local test_name=$1
    local message_count=$2
    local expected_improvement=$3
    
    echo "=== $test_name ==="
    echo "消息数量: $message_count"
    echo "期望改进: $expected_improvement"
    echo ""
    
    # 重置统计数据
    echo "重置MinIO连接池统计数据..."
    local reset_response=$(curl -s -X POST "$SERVER_URL/api/minio/connection-pool/reset-stats")
    
    if command -v jq > /dev/null 2>&1; then
        local reset_success=$(echo "$reset_response" | jq -r '.success // false')
        if [ "$reset_success" = "true" ]; then
            echo "✅ 统计数据重置成功"
        else
            echo "⚠️  统计数据重置失败"
        fi
    fi
    
    echo ""
    
    local start_time=$(date +%s)
    
    # 发送测试请求
    local response=$(curl -s -X POST "$SERVER_URL/api/test/stress-test" \
        -H "Content-Type: application/json" \
        -d "{
            \"messageCount\": $message_count,
            \"threadCount\": 5,
            \"sendIntervalMs\": 100,
            \"testType\": \"normal\"
        }")
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    
    echo "测试完成，总耗时: ${total_time}秒"
    
    # 获取连接池性能统计
    sleep 2  # 等待统计数据更新
    local perf_response=$(curl -s "$SERVER_URL/api/minio/connection-pool/performance")
    
    if command -v jq > /dev/null 2>&1; then
        echo "业务处理结果:"
        echo "$response" | jq '.data // {}'
        
        echo ""
        echo "MinIO连接池性能统计:"
        echo "$perf_response" | jq '.'
        
        # 提取关键指标
        local throughput=$(echo "$response" | jq -r '.data.throughputPerSecond // "N/A"')
        local success_count=$(echo "$response" | jq -r '.data.successCount // "N/A"')
        local failed_count=$(echo "$response" | jq -r '.data.failedCount // "N/A"')
        local avg_latency=$(echo "$response" | jq -r '.data.avgLatencyMs // "N/A"')
        
        local perf_stats=$(echo "$perf_response" | jq -r '.performance // "N/A"')
        
        echo ""
        echo "连接池性能分析:"
        echo "- 业务吞吐量: $throughput msg/s"
        echo "- 成功处理: $success_count 条"
        echo "- 失败处理: $failed_count 条"
        echo "- 平均延迟: $avg_latency ms"
        echo "- MinIO统计: $perf_stats"
        
        # 性能评估
        if [ "$throughput" != "N/A" ]; then
            local throughput_int=$(echo "$throughput" | cut -d'.' -f1)
            if [ "$throughput_int" -ge 1 ]; then
                echo "✅ 连接池性能良好: $throughput msg/s"
                echo "🚀 连接池复用减少了连接建立开销"
            else
                echo "⚠️  性能仍需优化: $throughput msg/s"
            fi
        fi
        
    else
        echo "业务处理结果:"
        echo "$response"
        echo ""
        echo "MinIO连接池性能统计:"
        echo "$perf_response"
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

# 开始测试
echo "开始MinIO连接池测试..."
echo ""

# 1. 检查连接池初始状态
check_connection_pool_status

# 2. 小批量性能测试
test_connection_pool_performance "小批量连接池测试" 10 "连接复用效果"
sleep 3

# 3. 中等批量性能测试
test_connection_pool_performance "中等批量连接池测试" 20 "连接池稳定性"
sleep 3

# 4. 大批量性能测试
test_connection_pool_performance "大批量连接池测试" 50 "高并发连接管理"
sleep 3

# 5. 最终状态检查
echo "=== 最终连接池状态检查 ==="
check_connection_pool_status

# 获取详细报告
echo "=== MinIO连接池详细报告 ==="
local report_response=$(curl -s "$SERVER_URL/api/minio/connection-pool/report")

if command -v jq > /dev/null 2>&1; then
    echo "$report_response" | jq '.report // "报告获取失败"' -r
else
    echo "$report_response"
fi

echo ""
echo "=== MinIO连接池测试完成 ==="
echo ""

# 生成测试总结
echo "=== MinIO连接池优化总结 ==="
echo ""
echo "连接池配置优势:"
echo "1. ✅ 连接复用"
echo "   - 减少TCP连接建立/关闭开销"
echo "   - 提升网络传输效率"
echo "   - 降低MinIO服务器连接压力"
echo ""

echo "2. ✅ 超时控制"
echo "   - 连接超时: 10秒"
echo "   - Socket超时: 30秒"
echo "   - 连接请求超时: 5秒"
echo ""

echo "3. ✅ 连接管理"
echo "   - 最大连接数: 100"
echo "   - 每路由最大连接: 50"
echo "   - 连接保活时间: 60秒"
echo ""

echo "4. ✅ 性能监控"
echo "   - 实时连接池状态监控"
echo "   - 上传性能统计"
echo "   - 健康状态检查"
echo ""

echo "监控接口:"
echo "# 查看连接池状态"
echo "curl -s $SERVER_URL/api/minio/connection-pool/status | jq"
echo ""
echo "# 查看性能统计"
echo "curl -s $SERVER_URL/api/minio/connection-pool/performance | jq"
echo ""
echo "# 查看详细报告"
echo "curl -s $SERVER_URL/api/minio/connection-pool/report | jq"
echo ""
echo "# 重置统计数据"
echo "curl -s -X POST $SERVER_URL/api/minio/connection-pool/reset-stats | jq"
echo ""

echo "配置验证:"
echo "# 检查MinIO连接池配置"
echo "curl -s $SERVER_URL/actuator/configprops | grep -A 20 minio"
echo ""

echo "健康检查:"
echo "# 查看MinIO健康状态"
echo "curl -s $SERVER_URL/actuator/health | jq '.components.minioConnectionPoolMonitor // {}'"
echo ""

echo "性能优化效果:"
echo "- 🚀 连接复用减少网络开销"
echo "- ⚡ 超时控制提升响应速度"
echo "- 📊 实时监控便于性能调优"
echo "- 🛡️ 连接池管理提升稳定性"
echo ""

echo "进一步优化建议:"
echo "1. 根据实际负载调整最大连接数"
echo "2. 监控连接池使用率，优化超时参数"
echo "3. 定期检查MinIO服务器性能"
echo "4. 结合业务场景调整连接保活时间"
echo ""

echo "测试完成时间: $(date)"
echo ""
echo "MinIO连接池配置已完成，连接复用将提升上传性能！"
