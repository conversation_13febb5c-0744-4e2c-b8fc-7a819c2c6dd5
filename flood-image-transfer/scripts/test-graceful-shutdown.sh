#!/bin/bash

# 优雅停止功能测试脚本
# 用于验证应用的优雅停止功能是否正常工作

set -e

# 配置参数
APP_NAME="flood-image-transfer"
BASE_URL="http://localhost:26058"
API_BASE="${BASE_URL}/api/graceful-shutdown"
TEST_LOG="graceful-shutdown-test.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$TEST_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$TEST_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$TEST_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$TEST_LOG"
}

# 检查应用是否运行
check_app_running() {
    log "检查应用是否运行..."
    if curl -s "${BASE_URL}/actuator/health" > /dev/null 2>&1; then
        log_success "应用正在运行"
        return 0
    else
        log_error "应用未运行，请先启动应用"
        return 1
    fi
}

# 获取优雅停止状态
get_shutdown_status() {
    log "获取优雅停止状态..."
    response=$(curl -s "${API_BASE}/status" || echo "")
    if [ -n "$response" ]; then
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "无法获取状态"
    fi
}

# 获取活跃任务信息
get_active_tasks() {
    log "获取活跃任务信息..."
    response=$(curl -s "${API_BASE}/active-tasks" || echo "")
    if [ -n "$response" ]; then
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "无法获取活跃任务信息"
    fi
}

# 检查是否可以安全停止
check_can_shutdown() {
    log "检查是否可以安全停止..."
    response=$(curl -s "${API_BASE}/can-shutdown" || echo "")
    if [ -n "$response" ]; then
        can_shutdown=$(echo "$response" | jq -r '.data.canShutdown' 2>/dev/null || echo "unknown")
        active_count=$(echo "$response" | jq -r '.data.activeTaskCount' 2>/dev/null || echo "unknown")
        
        if [ "$can_shutdown" = "true" ]; then
            log_success "可以安全停止，活跃任务数: $active_count"
        else
            log_warning "不建议停止，活跃任务数: $active_count"
        fi
        
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "无法检查停止条件"
    fi
}

# 获取系统健康状态
get_health_status() {
    log "获取系统健康状态..."
    response=$(curl -s "${API_BASE}/health" || echo "")
    if [ -n "$response" ]; then
        healthy=$(echo "$response" | jq -r '.data.healthy' 2>/dev/null || echo "unknown")
        
        if [ "$healthy" = "true" ]; then
            log_success "系统健康状态正常"
        else
            log_warning "系统健康状态异常"
        fi
        
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "无法获取健康状态"
    fi
}

# 等待任务完成测试
test_wait_tasks_completion() {
    local timeout=${1:-10}
    log "测试等待任务完成功能（超时: ${timeout}秒）..."
    
    response=$(curl -s -X POST "${API_BASE}/wait-tasks-completion?timeoutSeconds=${timeout}" || echo "")
    if [ -n "$response" ]; then
        all_completed=$(echo "$response" | jq -r '.data.allCompleted' 2>/dev/null || echo "unknown")
        
        if [ "$all_completed" = "true" ]; then
            log_success "所有任务已完成"
        else
            log_warning "任务未在指定时间内完成"
        fi
        
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "等待任务完成测试失败"
    fi
}

# 模拟负载测试
simulate_load() {
    log "模拟负载以测试优雅停止..."
    
    # 这里可以添加模拟负载的逻辑
    # 例如：发送一些测试消息到Kafka
    # 或者调用一些会产生异步任务的API
    
    log "发送测试请求以产生异步任务..."
    
    # 示例：调用图片下载API（如果存在）
    # curl -s -X POST "${BASE_URL}/api/test/download" -d '{"url":"http://example.com/test.jpg"}' -H "Content-Type: application/json" > /dev/null || true
    
    log "负载模拟完成"
}

# 测试优雅停止流程
test_graceful_shutdown() {
    log "开始测试优雅停止流程..."
    
    # 1. 检查初始状态
    log "=== 1. 检查初始状态 ==="
    get_shutdown_status
    get_health_status
    
    # 2. 模拟负载
    log "=== 2. 模拟负载 ==="
    simulate_load
    
    # 3. 检查活跃任务
    log "=== 3. 检查活跃任务 ==="
    get_active_tasks
    
    # 4. 检查是否可以停止
    log "=== 4. 检查是否可以停止 ==="
    check_can_shutdown
    
    # 5. 测试等待任务完成
    log "=== 5. 测试等待任务完成 ==="
    test_wait_tasks_completion 5
    
    # 6. 最终状态检查
    log "=== 6. 最终状态检查 ==="
    get_shutdown_status
    check_can_shutdown
    
    log_success "优雅停止测试完成"
}

# 监控优雅停止过程
monitor_shutdown_process() {
    local pid=$1
    log "监控优雅停止过程，应用PID: $pid"
    
    # 发送SIGTERM信号
    log "发送SIGTERM信号..."
    kill -TERM "$pid" 2>/dev/null || {
        log_error "无法发送SIGTERM信号到PID: $pid"
        return 1
    }
    
    # 监控停止过程
    local start_time=$(date +%s)
    local timeout=30
    
    while kill -0 "$pid" 2>/dev/null; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [ $elapsed -gt $timeout ]; then
            log_error "优雅停止超时（${timeout}秒），进程仍在运行"
            return 1
        fi
        
        log "等待优雅停止完成... (${elapsed}s)"
        
        # 尝试获取状态（如果应用还在响应）
        if curl -s "${API_BASE}/status" > /dev/null 2>&1; then
            get_shutdown_status
        fi
        
        sleep 2
    done
    
    local total_time=$(($(date +%s) - start_time))
    log_success "优雅停止完成，耗时: ${total_time}秒"
}

# 完整的优雅停止测试
full_graceful_shutdown_test() {
    log "开始完整的优雅停止测试..."
    
    # 获取应用PID
    local app_pid=$(pgrep -f "$APP_NAME" | head -1)
    if [ -z "$app_pid" ]; then
        log_error "无法找到应用进程"
        return 1
    fi
    
    log "找到应用进程PID: $app_pid"
    
    # 执行测试流程
    test_graceful_shutdown
    
    # 监控停止过程
    monitor_shutdown_process "$app_pid"
}

# 重置统计信息
reset_statistics() {
    log "重置任务统计信息..."
    response=$(curl -s -X POST "${API_BASE}/reset-statistics" || echo "")
    if [ -n "$response" ]; then
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
        log_success "统计信息重置完成"
    else
        log_error "重置统计信息失败"
    fi
}

# 显示帮助信息
show_help() {
    echo "优雅停止功能测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  status          获取优雅停止状态"
    echo "  tasks           获取活跃任务信息"
    echo "  health          获取系统健康状态"
    echo "  can-shutdown    检查是否可以安全停止"
    echo "  wait-tasks      测试等待任务完成功能"
    echo "  test            执行完整的优雅停止测试"
    echo "  full-test       执行完整测试并监控停止过程"
    echo "  reset           重置任务统计信息"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status       # 获取当前状态"
    echo "  $0 test         # 执行测试"
    echo "  $0 full-test    # 执行完整测试"
}

# 主函数
main() {
    # 初始化日志文件
    echo "=== 优雅停止功能测试 - $(date) ===" > "$TEST_LOG"
    
    # 检查依赖
    if ! command -v curl > /dev/null 2>&1; then
        log_error "curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    if ! command -v jq > /dev/null 2>&1; then
        log_warning "jq 命令未找到，JSON输出可能不够美观"
    fi
    
    # 检查应用状态
    if ! check_app_running; then
        exit 1
    fi
    
    # 根据参数执行相应功能
    case "${1:-help}" in
        "status")
            get_shutdown_status
            ;;
        "tasks")
            get_active_tasks
            ;;
        "health")
            get_health_status
            ;;
        "can-shutdown")
            check_can_shutdown
            ;;
        "wait-tasks")
            test_wait_tasks_completion "${2:-10}"
            ;;
        "test")
            test_graceful_shutdown
            ;;
        "full-test")
            full_graceful_shutdown_test
            ;;
        "reset")
            reset_statistics
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
