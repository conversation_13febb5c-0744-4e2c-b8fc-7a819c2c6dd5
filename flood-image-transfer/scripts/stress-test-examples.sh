#!/bin/bash

# EventMessage 压力测试示例脚本
# 使用方法: ./stress-test-examples.sh [server_url]
# 默认服务器地址: http://localhost:8080

SERVER_URL=${1:-"http://localhost:8080"}
API_ENDPOINT="$SERVER_URL/api/test/stress-test"

echo "=== EventMessage 压力测试示例 ==="
echo "服务器地址: $SERVER_URL"
echo "API接口: $API_ENDPOINT"
echo ""

# 检查服务器是否可用
echo "检查服务器连接..."
if ! curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo "警告: 无法连接到服务器 $SERVER_URL"
    echo "请确保服务已启动并且地址正确"
    echo ""
fi

# 示例1: 基础压力测试
echo "=== 示例1: 基础压力测试 ==="
echo "发送1000条消息，使用10个线程，无发送间隔"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 1000,
    "threadCount": 10,
    "sendIntervalMs": 0,
    "testType": "normal"
  }' | jq '.'

echo ""
echo "按Enter键继续下一个示例..."
read

# 示例2: 小规模快速测试
echo "=== 示例2: 小规模快速测试 ==="
echo "发送100条消息，使用5个线程，测试基本功能"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 100,
    "threadCount": 5,
    "sendIntervalMs": 0,
    "testType": "normal"
  }' | jq '.'

echo ""
echo "按Enter键继续下一个示例..."
read

# 示例3: 限流测试
echo "=== 示例3: 限流测试 ==="
echo "发送500条消息，使用5个线程，每条消息间隔100ms"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 500,
    "threadCount": 5,
    "sendIntervalMs": 100,
    "testType": "normal"
  }' | jq '.'

echo ""
echo "按Enter键继续下一个示例..."
read

# 示例4: 异常场景测试 - 无图片URL
echo "=== 示例4: 异常场景测试 - 无图片URL ==="
echo "发送200条无图片URL的消息，测试异常处理"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 200,
    "threadCount": 5,
    "sendIntervalMs": 0,
    "testType": "no-image"
  }' | jq '.'

echo ""
echo "按Enter键继续下一个示例..."
read

# 示例5: 异常场景测试 - 无效URL
echo "=== 示例5: 异常场景测试 - 无效URL ==="
echo "发送200条无效图片URL的消息，测试错误处理"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 200,
    "threadCount": 5,
    "sendIntervalMs": 0,
    "testType": "invalid-url"
  }' | jq '.'

echo ""
echo "按Enter键继续下一个示例..."
read

# 示例6: 大数据测试
echo "=== 示例6: 大数据测试 ==="
echo "发送100条包含大量Base64数据的消息，测试大数据处理"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 100,
    "threadCount": 3,
    "sendIntervalMs": 50,
    "testType": "large-data"
  }' | jq '.'

echo ""
echo "按Enter键继续下一个示例..."
read

# 示例7: 高并发测试
echo "=== 示例7: 高并发测试 ==="
echo "发送5000条消息，使用50个线程，测试高并发性能"
echo "注意: 此测试可能需要较长时间，请确保系统资源充足"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 5000,
    "threadCount": 50,
    "sendIntervalMs": 0,
    "testType": "normal"
  }' | jq '.'

echo ""
echo "=== 压力测试示例完成 ==="
echo ""
echo "性能指标说明:"
echo "- throughputPerSecond: 每秒成功发送的消息数量"
echo "- avgLatencyMs: 单条消息发送的平均耗时(毫秒)"
echo "- p50LatencyMs: 50%的请求在该时间内完成"
echo "- p95LatencyMs: 95%的请求在该时间内完成"
echo "- p99LatencyMs: 99%的请求在该时间内完成"
echo ""
echo "建议:"
echo "1. 从小规模测试开始，逐步增加负载"
echo "2. 监控系统资源使用情况(CPU、内存、网络)"
echo "3. 观察Kafka集群的性能指标"
echo "4. 检查应用日志中的错误信息"
