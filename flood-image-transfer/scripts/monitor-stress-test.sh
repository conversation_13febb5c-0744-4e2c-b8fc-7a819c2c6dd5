#!/bin/bash

# 压力测试监控脚本
# 在压力测试期间监控系统资源使用情况

echo "=== 压力测试系统监控 ==="
echo "开始时间: $(date)"
echo ""

# 创建日志目录
LOG_DIR="flood-image-transfer/logs/stress-test"
mkdir -p "$LOG_DIR"

# 日志文件
MONITOR_LOG="$LOG_DIR/monitor-$(date +%Y%m%d_%H%M%S).log"
SYSTEM_LOG="$LOG_DIR/system-$(date +%Y%m%d_%H%M%S).log"

echo "监控日志: $MONITOR_LOG"
echo "系统日志: $SYSTEM_LOG"
echo ""

# 监控函数
monitor_system() {
    local duration=${1:-300}  # 默认监控5分钟
    local interval=${2:-5}    # 默认每5秒采集一次
    
    echo "开始监控系统资源，持续时间: ${duration}秒，采集间隔: ${interval}秒"
    echo "时间,CPU使用率(%),内存使用率(%),磁盘使用率(%),网络接收(KB/s),网络发送(KB/s)" > "$SYSTEM_LOG"
    
    local start_time=$(date +%s)
    local end_time=$((start_time + duration))
    
    while [ $(date +%s) -lt $end_time ]; do
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        
        # CPU使用率
        local cpu_usage=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
        
        # 内存使用率
        local memory_info=$(vm_stat | grep -E "(free|active|inactive|wired)" | awk '{print $3}' | sed 's/\.//')
        local free_pages=$(echo "$memory_info" | sed -n '1p')
        local active_pages=$(echo "$memory_info" | sed -n '2p')
        local inactive_pages=$(echo "$memory_info" | sed -n '3p')
        local wired_pages=$(echo "$memory_info" | sed -n '4p')
        
        local total_pages=$((free_pages + active_pages + inactive_pages + wired_pages))
        local used_pages=$((active_pages + inactive_pages + wired_pages))
        local memory_usage=$((used_pages * 100 / total_pages))
        
        # 磁盘使用率
        local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
        
        # 网络统计 (简化版本)
        local network_rx="0"
        local network_tx="0"
        
        echo "$timestamp,$cpu_usage,$memory_usage,$disk_usage,$network_rx,$network_tx" >> "$SYSTEM_LOG"
        
        # 显示实时信息
        printf "\r[%s] CPU: %s%% | 内存: %s%% | 磁盘: %s%%" "$timestamp" "$cpu_usage" "$memory_usage" "$disk_usage"
        
        sleep $interval
    done
    
    echo ""
    echo "监控完成"
}

# Java进程监控
monitor_java_process() {
    local duration=${1:-300}
    local interval=${2:-10}
    
    echo "监控Java进程资源使用情况"
    echo "时间,进程ID,CPU(%),内存(MB),线程数" > "$LOG_DIR/java-process-$(date +%Y%m%d_%H%M%S).log"
    
    local start_time=$(date +%s)
    local end_time=$((start_time + duration))
    
    while [ $(date +%s) -lt $end_time ]; do
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        
        # 查找Java进程
        local java_pids=$(pgrep -f "flood-image-transfer")
        
        for pid in $java_pids; do
            if [ -n "$pid" ]; then
                local cpu_percent=$(ps -p $pid -o %cpu= | tr -d ' ')
                local memory_mb=$(ps -p $pid -o rss= | awk '{print $1/1024}')
                local thread_count=$(ps -p $pid -o nlwp= | tr -d ' ')
                
                echo "$timestamp,$pid,$cpu_percent,$memory_mb,$thread_count" >> "$LOG_DIR/java-process-$(date +%Y%m%d_%H%M%S).log"
                
                echo "[$timestamp] Java进程 $pid: CPU: ${cpu_percent}%, 内存: ${memory_mb}MB, 线程: $thread_count"
            fi
        done
        
        sleep $interval
    done
}

# Kafka监控 (如果可用)
monitor_kafka() {
    echo "检查Kafka连接..."
    
    # 这里可以添加Kafka监控逻辑
    # 例如: 检查topic的消息积压、消费者lag等
    
    echo "Kafka监控功能需要根据具体环境配置"
}

# 应用日志监控
monitor_application_logs() {
    local log_file="flood-image-transfer/logs/application.log"
    
    if [ -f "$log_file" ]; then
        echo "监控应用日志: $log_file"
        tail -f "$log_file" | grep -E "(ERROR|WARN|压力测试)" &
        local tail_pid=$!
        
        # 返回tail进程ID，以便后续停止
        echo $tail_pid
    else
        echo "应用日志文件不存在: $log_file"
        return 1
    fi
}

# 生成监控报告
generate_report() {
    local report_file="$LOG_DIR/stress-test-report-$(date +%Y%m%d_%H%M%S).md"
    
    echo "# 压力测试监控报告" > "$report_file"
    echo "" >> "$report_file"
    echo "生成时间: $(date)" >> "$report_file"
    echo "" >> "$report_file"
    
    echo "## 系统资源使用情况" >> "$report_file"
    echo "" >> "$report_file"
    
    if [ -f "$SYSTEM_LOG" ]; then
        echo "### CPU使用率统计" >> "$report_file"
        local avg_cpu=$(awk -F',' 'NR>1 {sum+=$2; count++} END {if(count>0) print sum/count; else print 0}' "$SYSTEM_LOG")
        local max_cpu=$(awk -F',' 'NR>1 {if($2>max) max=$2} END {print max+0}' "$SYSTEM_LOG")
        echo "- 平均CPU使用率: ${avg_cpu}%" >> "$report_file"
        echo "- 最大CPU使用率: ${max_cpu}%" >> "$report_file"
        echo "" >> "$report_file"
        
        echo "### 内存使用率统计" >> "$report_file"
        local avg_memory=$(awk -F',' 'NR>1 {sum+=$3; count++} END {if(count>0) print sum/count; else print 0}' "$SYSTEM_LOG")
        local max_memory=$(awk -F',' 'NR>1 {if($3>max) max=$3} END {print max+0}' "$SYSTEM_LOG")
        echo "- 平均内存使用率: ${avg_memory}%" >> "$report_file"
        echo "- 最大内存使用率: ${max_memory}%" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    echo "## 建议" >> "$report_file"
    echo "" >> "$report_file"
    echo "1. 如果CPU使用率持续超过80%，建议减少并发线程数" >> "$report_file"
    echo "2. 如果内存使用率超过85%，建议增加JVM堆内存大小" >> "$report_file"
    echo "3. 监控Kafka集群的性能指标，确保消息处理及时" >> "$report_file"
    echo "4. 检查网络带宽是否成为瓶颈" >> "$report_file"
    
    echo "监控报告已生成: $report_file"
}

# 主函数
main() {
    local duration=${1:-300}  # 监控持续时间(秒)
    local mode=${2:-"system"} # 监控模式: system, java, all
    
    case $mode in
        "system")
            monitor_system $duration
            ;;
        "java")
            monitor_java_process $duration
            ;;
        "all")
            echo "启动全面监控..."
            monitor_system $duration &
            local system_pid=$!
            
            monitor_java_process $duration &
            local java_pid=$!
            
            # 等待监控完成
            wait $system_pid
            wait $java_pid
            ;;
        *)
            echo "未知监控模式: $mode"
            echo "支持的模式: system, java, all"
            exit 1
            ;;
    esac
    
    generate_report
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [持续时间(秒)] [监控模式]"
    echo ""
    echo "参数说明:"
    echo "  持续时间: 监控持续时间，默认300秒(5分钟)"
    echo "  监控模式: system(系统资源) | java(Java进程) | all(全部)"
    echo ""
    echo "示例:"
    echo "  $0 600 all          # 监控10分钟，包含系统和Java进程"
    echo "  $0 300 system       # 监控5分钟，仅系统资源"
    echo "  $0 180 java         # 监控3分钟，仅Java进程"
}

# 检查参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# 执行监控
main "$@"
