<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">

  <!-- 启用 ANSI 颜色支持（关键修改） -->
  <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
  <conversionRule conversionWord="wex"
                  converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
  <conversionRule conversionWord="wEx"
                  converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

  <!-- 控制台输出（支持颜色） -->
  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <!-- 颜色格式模板 -->
      <pattern>%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%thread]){blue} %clr(%-5level){green} %clr(%logger{36}){cyan} - %msg%n
      </pattern>
    </encoder>
  </appender>

  <!-- 文件滚动日志 -->
  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 当前日志文件路径 -->
    <file>${LOG_PATH:-logs}/app.log</file>

    <!-- 滚动策略 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <!-- 归档日志文件名模式 -->
      <fileNamePattern>logs/app.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>

      <!-- 单个日志文件最大限制 -->
      <maxFileSize>50MB</maxFileSize>

      <!-- 保留最近30天的日志 -->
      <maxHistory>30</maxHistory>

      <!-- 启动时清理过期日志 -->
      <cleanHistoryOnStart>true</cleanHistoryOnStart>
    </rollingPolicy>

    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>

  <!-- 新增：ERROR级别专属文件输出 -->
  <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_PATH:-logs}/error.log</file>
    <!-- 仅记录ERROR级别 -->
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>ERROR</level>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>logs/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
      <maxFileSize>10MB</maxFileSize>
      <maxHistory>90</maxHistory> <!-- ERROR日志保留更久 -->
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n%wEx</pattern>
    </encoder>
  </appender>

  <!-- ========== 关键修改：禁止Kafka连接信息日志 ========== -->
  <!-- 将Kafka客户端的INFO日志降级为WARN -->
  <logger name="org.apache.kafka" level="WARN" />
  <logger name="kafka" level="WARN" />
  <!-- 可选：关闭Kafka配置初始化日志 -->
  <logger name="org.apache.kafka.clients" level="WARN" />

  <!-- 日志级别设置 -->
  <root level="INFO">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FILE"/>
    <appender-ref ref="ERROR_FILE"/>
  </root>
</configuration>
