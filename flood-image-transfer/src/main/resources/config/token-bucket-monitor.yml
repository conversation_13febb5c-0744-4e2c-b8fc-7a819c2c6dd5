# 令牌桶监控配置
token-bucket:
  monitor:
    # 监控间隔（毫秒）
    interval: 20000
    
    # 告警配置
    alert:
      # 失败率阈值（0.0-1.0）
      failure-rate-threshold: 0.8
      # 负数重置次数阈值
      negative-reset-threshold: 10
      # 长等待时间阈值（毫秒）
      long-wait-threshold: 10000
    
    # 历史数据配置
    history:
      # 数据保留时间（小时）
      retention-hours: 24
      # 最大记录数（每个令牌桶）
      max-records-per-bucket: 1000
    
    # 性能配置
    performance:
      # 是否启用异步监控
      async-enabled: true
      # 监控线程池大小
      thread-pool-size: 2
      # 批量处理大小
      batch-size: 100

# JMX配置
management:
  endpoints:
    jmx:
      exposure:
        include: "*"
  endpoint:
    jmx:
      enabled: true

# 日志配置
logging:
  level:
    com.sdses.ai.imagetransfer.monitor: INFO
    com.sdses.ai.imagetransfer.component.RedisTokenBucket: DEBUG
