server:
  port: 26058
spring:
  application:
    name: imageTransfer
  banner:
    charset: UTF-8
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  profiles:
#    active: dev
#    active: prod
#    active: vpn
    active: test

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.sdses.*.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      id-type: input
      logic-delete-value: 1
      logic-not-delete-value: 0

# 优雅停止配置
graceful-shutdown:
  enabled: true                           # 启用优雅停止功能
  timeout-seconds: 10                     # 优雅停止超时时间（秒）
  force-shutdown-after-timeout: true      # 超时后是否强制停止

# 定时任务配置
scheduling:
  pool-size: 5                            # 定时任务线程池大小
  thread-name-prefix: "scheduled-task-"   # 线程名前缀
  wait-for-tasks-to-complete-on-shutdown: true  # 关闭时等待任务完成
  await-termination-seconds: 10           # 等待终止的最大时间（秒）
