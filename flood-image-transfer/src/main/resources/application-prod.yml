spring:
  kafka:
    listener:
      ack-mode: manual # 手动提交
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:192.168.6.78:39092,************:39092,192.168.6.52:39092,192.168.6.178:39092,192.168.6.76:39092,192.168.6.233:39092,192.168.6.126:39092,192.168.6.192:39092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: ${KAFKA_GROUP_ID:submerge-tracking-water-group} # 消费者组id
      auto-offset-reset: ${KAFKA_AUTO_OFFSET_RESET:latest} # 无偏移量时从最新一条开始消费
      enable-auto-commit: false # 关闭自动提交
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer

      concurrency: ${KAFKA_CONCURRENCY:4}
      retry-concurrency: ${RETRY_CONCURRENCY:2}
      max-poll-records: 500
      session-timeout-ms: 30000

      # JSON反序列化配置
      trusted-packages: com.sdses.ai.imagetransfer.entity
      use-type-info-headers: false

      heartbeat-interval-ms: 3000
      max-poll-interval-ms: 300000
      fetch-min-size: 10240
      fetch-max-wait: 500
      async-processing:
        enabled: true
        timeout-seconds: 120
      retry:
        async-processing:
          enabled: true
          timeout-seconds: 180
    topics:
      image-events: ${KAFKA_IMAGE_EVENT_TOPIC:common_event_alarm}
      retry-topic: ${KAFKA_RETRY_TOPIC:common_event_alarm_retry}
      resource-topic: ${KAFKA_RESOURCE_TOPIC:common_event_resource}

  # 动态数据源配置 - 使用 dynamic-datasource-spring-boot-starter
  datasource:
    dynamic:
      primary: pg1                     # 设置默认的数据源或者数据源组，默认值即为 master
      strict: false                    # 严格匹配数据源，默认false. true未匹配到指定数据源时抛异常，false使用默认数据源
      datasource:
        # 主数据源配置 (primary) - PostgreSQL
        pg1:
          driver-class-name: org.postgresql.Driver
          url: **********************************************************************************************************************************************************
          username: postgres
          password: Hh5xR1o!Xx
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 10               # 初始连接数
            min-idle: 10                   # 最小空闲连接数
            max-active: 100                # 最大活跃连接数
            max-wait: 60000                # 获取连接最大等待时间(ms)
            validation-query: SELECT 1     # 连接验证查询
            test-while-idle: true          # 空闲时验证连接
            test-on-borrow: false          # 获取连接时验证
            test-on-return: false          # 归还连接时验证
            time-between-eviction-runs-millis: 60000  # 检测空闲连接间隔时间
            min-evictable-idle-time-millis: 300000    # 连接最小空闲时间
            max-evictable-idle-time-millis: 900000    # 连接最大空闲时间

        # 第二数据源配置 (secondary) - Doris 数据库
        doris1:
          driver-class-name: com.mysql.cj.jdbc.Driver  # Doris 使用 MySQL JDBC 驱动
          url: *********************************************************************************************************************************************************************************
          username: datacube_doris_sh
          password: Rs3k_Nx8Tz
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 5                # 初始连接数 (Doris 建议较少连接)
            min-idle: 5                    # 最小空闲连接数
            max-active: 30                 # 最大活跃连接数 (Doris 适中连接数)
            max-wait: 60000                # 获取连接最大等待时间(ms)
            validation-query: SELECT 1     # 连接验证查询
            test-while-idle: true          # 空闲时验证连接
            test-on-borrow: true           # Doris 建议获取连接时验证
            test-on-return: false          # 归还连接时验证
            time-between-eviction-runs-millis: 60000   # 检测空闲连接间隔时间
            min-evictable-idle-time-millis: 300000     # 连接最小空闲时间
            max-evictable-idle-time-millis: 600000     # Doris 连接最大空闲时间(10分钟)
            # Doris 特定配置
            connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
            filters: stat,wall                         # 启用监控和防火墙
            max-pool-prepared-statement-per-connection-size: 20
            use-global-data-source-stat: true         # 启用全局数据源统计

  # redis配置
  data:
    redis:
      host: ${REDIS_HOST:************}  # 生产环境Redis地址
      port: ${REDIS_PORT:6399}          # 生产环境Redis端口
      password: ${REDIS_PASSWORD:Bigdata@2024}      # 生产环境Redis密码
      database: 15
      timeout: 3000  # 命令执行超时时间(ms)
      connect-timeout: 3000  # 连接超时时间(ms)
      pool:
        max-active: 30  # 生产环境连接池最大连接数
        max-idle: 15    # 连接池最大空闲连接数
        min-idle: 8     # 连接池最小空闲连接数
        max-wait: 3000  # 连接池最大阻塞等待时间(ms)

# 图片下载配置
image:
  download:
    # 基本超时配置 (优化后)
    connect-timeout: 3                # 生产环境连接超时时间(秒)
    read-timeout: 15                  # 生产环境读取超时时间(秒)
    write-timeout: 15                 # 生产环境写入超时时间(秒)
    call-timeout: 30            # 整个请求超时30秒
    trust-all-ssl: true               # 生产环境建议设为false
    monitor-interval: 10000           # 监控间隔(毫秒)

    # 连接池配置 (新增)
    connection-pool:
      max-idle-connections: 50        # 最大空闲连接数 (优化)
      keep-alive-duration: 300        # 连接保活时间(秒)
      max-requests: 200               # 最大并发请求数
      max-requests-per-host: 50       # 每个主机最大并发请求数

    # 重试配置 (新增)
    retry:
      retry-on-connection-failure: true  # 连接失败时重试
      max-retries: 3                     # 最大重试次数
      retry-interval: 1000               # 重试间隔(毫秒)

    # 性能配置 (新增)
    performance:
      enable-gzip: true                  # 启用Gzip压缩
      enable-http2: true                 # 启用HTTP/2
      max-concurrent-downloads: 20       # 最大并发下载数
      max-image-size: 52428800          # 最大图片大小(50MB)
      enable-metrics: true               # 启用性能指标收集

# 三调服务器地址
url-address:
  address-list:
    - address: https://************:18001
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: address-1
      rate: ${BUCKET_RATE:60}
    - address: https://************:18001
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: address-2
      rate: ${BUCKET_RATE:60}
    - address: https://*************:18001
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: address-3
      rate: ${BUCKET_RATE:60}
    - address: https://*************:18001
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: address-4
      rate: ${BUCKET_RATE:60}
    # 蒙版图服务器
    - address: http://*************:20112
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: mask-1
      rate: ${BUCKET_RATE:30}
    - address: http://*************:20112
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: mask-2
      rate: ${BUCKET_RATE:30}
    - address: http://*************:20112
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: mask-3
      rate: ${BUCKET_RATE:30}

minio:
  endpoint: ${MINIO_ENDPOINT:http://*************:9030}
  access-key: ${MINIO_ACCESS_KEY:t1q5wneMCiVK4eNDegNZ}
  secret-key: ${MINIO_SECRET_KEY:oDtUqHc9dZRR2rEVRmQzAcRMp5SPBj4EkhHnspVR}
  bucket-name: ${MINIO_BUCKET_NAME:common-event-video-data}
  # MinIO连接池配置 - 提升连接复用和性能
  connection-pool:
    enabled: true                     # 启用连接池
    max-connections: 100              # 最大连接数
    max-connections-per-route: 50     # 每个路由的最大连接数
    connection-timeout: 10000         # 连接超时时间(ms)
    socket-timeout: 30000             # Socket超时时间(ms)
    connection-request-timeout: 5000  # 连接请求超时时间(ms)
    keep-alive-time: 60000           # 连接保活时间(ms)
    max-idle-time: 300000            # 最大空闲时间(ms)
    validate-after-inactivity: true  # 空闲后验证连接
    retry-count: 3                   # 重试次数

async:
  virtual-thread:
    enabled: true
    name-prefix: "kafka-async-prod-"
  platform-thread:
    core-pool-size: 20
    max-pool-size: 100
  processing:
    timeout-seconds: 600
    batch-size: 200
    parallel-enabled: true

common:
  sync-upload-max-size: 10
  async-upload-max-size: 1000
  minio-public-url: ${MINIO_PUBLIC_URL:http://100.70.3.202:9030}  # MinIO公共访问URL，用于URL替换功能
