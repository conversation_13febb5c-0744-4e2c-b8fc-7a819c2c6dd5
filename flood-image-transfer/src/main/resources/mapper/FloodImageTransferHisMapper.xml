<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdses.ai.imagetransfer.mapper.FloodImageTransferHisMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sdses.ai.imagetransfer.entity.FloodImageTransferHis">
        <id column="id" property="id"/>
        <result column="kafka_msg" property="kafkaMsg"/>
        <result column="minio_image_url" property="minioImageUrl"/>
        <result column="dispose_type" property="disposeType"/>
        <result column="retry_count" property="retryCount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="event_time" property="eventTime"/>
    </resultMap>

    <insert id="saveOrUpdate">
        INSERT INTO flood_image_transfer_his (id,
                                              kafka_msg,
                                              minio_image_url,
                                              dispose_type,
                                              retry_count,
                                              event_time,
                                              task_id,
                                              data_source,
                                              create_time,
                                              update_time)
        VALUES (#{info.id},
                #{info.kafkaMsg},
                #{info.minioImageUrl},
                #{info.disposeType},
                #{info.retryCount},
                #{info.eventTime},
                #{info.taskId},
                #{info.dataSource},
                #{info.createTime},
                #{info.updateTime})
        ON CONFLICT (id) DO UPDATE
            SET kafka_msg       = EXCLUDED.kafka_msg,
                minio_image_url = EXCLUDED.minio_image_url,
                dispose_type= EXCLUDED.dispose_type,
                retry_count= EXCLUDED.retry_count,
                event_time= EXCLUDED.event_time,
                task_id         = EXCLUDED.task_id,
                data_source     = EXCLUDED.data_source,
                create_time     = EXCLUDED.create_time,
                update_time     = EXCLUDED.update_time;
    </insert>

    <select id="list48HoursData" resultMap="BaseResultMap">
        SELECT *
        FROM flood_image_transfer_his
        <where>
            <if test="minRetryCount != null">
                AND retry_count &gt; #{minRetryCount}
            </if>
            <if test="maxRetryCount != null">
                AND retry_count &lt; #{maxRetryCount}
            </if>
            <if test="time != null">
                AND event_time &gt;= (#{time}::timestamp - INTERVAL '48 hours')
            </if>
            <if test="disposeTypeList != null and disposeTypeList.size() > 0">
                AND dispose_type in
                <foreach collection="disposeTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY event_time
    </select>

    <insert id="saveOrUpdateRetry">
        INSERT INTO flood_image_transfer_his (id,
                                              kafka_msg,
                                              minio_image_url,
                                              dispose_type,
                                              retry_count,
                                              event_time,
                                              create_time,
                                              update_time)
        VALUES (#{info.id},
                #{info.kafkaMsg},
                #{info.minioImageUrl},
                #{info.disposeType},
                #{info.retryCount},
                #{info.eventTime},
                #{info.createTime},
                #{info.updateTime})
        ON CONFLICT (id) DO UPDATE
            SET kafka_msg       = EXCLUDED.kafka_msg,
                minio_image_url = EXCLUDED.minio_image_url,
                dispose_type= EXCLUDED.dispose_type,
                retry_count= EXCLUDED.retry_count,
                event_time= EXCLUDED.event_time,
                create_time     = EXCLUDED.create_time,
                update_time     =EXCLUDED.update_time;
    </insert>

    <delete id="deleteDataByHours">
        delete
        from flood_image_transfer_his
        where create_time &lt;= (#{time}::timestamp - make_interval(hours => #{hours}))
    </delete>

    <select id="listByParams" resultMap="BaseResultMap">
        SELECT *
        FROM flood_image_transfer_his
        <where>
            <if test="minRetryCount != null">
                AND retry_count &gt; #{minRetryCount}
            </if>
            <if test="maxRetryCount != null">
                AND retry_count &lt; #{maxRetryCount}
            </if>
            <if test="time != null and hours != null">
                AND event_time &gt;= (#{time}::timestamp - make_interval(hours => #{hours}))
            </if>
            <if test="disposeType != null and disposeType != ''">
                AND dispose_type = #{disposeType}
            </if>
        </where>
        ORDER BY event_time
    </select>

    <select id="list15MinData" resultMap="BaseResultMap">
        SELECT *
        FROM flood_image_transfer_his
        <where>
            <if test="minRetryCount != null">
                AND retry_count &gt;= #{minRetryCount}
            </if>
            <if test="maxRetryCount != null">
                AND retry_count &lt; #{maxRetryCount}
            </if>
            <if test="time != null">
                AND event_time &lt;= (#{time}::timestamp - INTERVAL '15 minutes')
            </if>
            <if test="disposeTypeList != null and disposeTypeList.size() > 0">
                AND dispose_type in
                <foreach collection="disposeTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY event_time
    </select>
</mapper>
