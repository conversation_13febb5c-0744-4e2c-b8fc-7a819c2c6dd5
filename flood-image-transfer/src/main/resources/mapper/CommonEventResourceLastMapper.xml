<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdses.ai.imagetransfer.mapper.CommonEventResourceLastMapper">
    <resultMap id="EventMessageMap" type="com.sdses.ai.imagetransfer.entity.EventMessage">
        <result property="eventId" column="event_id"/>

        <!-- 普通字段映射 -->
        <result property="cameraIndexCode" column="camera_index_code"/>
        <result property="cameraChannelCode" column="camera_channel_code"/>
        <result property="cameraForeignCode" column="camera_foreign_code"/>

        <!-- 时间字段（处理带时区的格式） -->
        <result property="eventTime" column="event_time"/>

        <result property="sourceSystem" column="source_system"/>
        <result property="sourceModule" column="source_module"/>
        <result property="info" column="info"/>
        <result property="eventType" column="event_type"/>

        <!-- 媒体资源字段 -->
        <result property="imageUrl" column="image_url"/>
        <result property="videoUrl" column="video_url"/>
        <result property="imageBase64" column="image_base64"/>

        <result property="onlyPicture" column="only_picture"/>
    </resultMap>


    <select id="listEventMessageByDB" resultMap="EventMessageMap">
        SELECT
        alarm.event_id,
        alarm.event_type,
        alarm.event_time AS event_time,
        alarm.snappedPicUrl as image_url,
        alarm.camera_foreign_code,
        alarm.camera_index_code,
        alarm.camera_channel_code,
        alarm.source_system,
        alarm.source_module,
        alarm.info
        FROM
        common_event_alarm alarm
        LEFT JOIN
        common_event_resource_last last
        ON
        alarm.event_id = last.event_id
        <where>
            <if test="startTime != null and startTime != ''">
                and alarm.event_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and alarm.event_time &lt;= #{endTime}
            </if>
            <if test="eventType != null and eventType != ''">
                AND alarm.event_type = #{eventType}
            </if>
        </where>
        ORDER BY
        alarm.event_time
    </select>
</mapper>
