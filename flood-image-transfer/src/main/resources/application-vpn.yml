spring:
  kafka:
    listener:
      ack-mode: manual
    bootstrap-servers: ${kafka_bootstrap_servers:hadoop-001:39092,hadoop-002:39092,hadoop-003:39092,hadoop-004:39092,hadoop-005:39092,hadoop-006:39092,hadoop-007:39092,hadoop-008:39092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: submerge-tracking-water-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      concurrency: 25
      retry-concurrency: 8
      max-poll-records: 500
      session-timeout-ms: 30000
      heartbeat-interval-ms: 3000
      max-poll-interval-ms: 300000
      fetch-min-size: 1
      fetch-max-wait: 500
      async-processing:
        enabled: true
        timeout-seconds: 75
      retry:
        async-processing:
          enabled: true
          timeout-seconds: 150
    topics:
      image-events: common_event_alarm
      retry-topic: image_retry_topic
      resource-topic: common_event_resource
      test-events: test-events           # 测试事件主题
      test-string: test-string           # 测试字符串主题

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ********************************************************************************************************************************************************
    username: postgres
    password: ${DB_PASSWORD:postgres}
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 25
      max-wait: 60000
      validation-query: SELECT 1

  data:
    redis:
      host: ${REDIS_HOST:hadoop-001}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 15
      timeout: 3000
      connect-timeout: 3000
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000

image:
  download:
    connect-timeout: 3              # 连接超时3秒 (业务指标要求)
    read-timeout: 15                # 读取超时15秒 (业务指标要求)
    write-timeout: 15               # 写入超时15秒 (业务指标要求)
    local-path: /opt/flood-image-transfer/downloads
    trust-all-ssl: true
    monitor-interval: 10000

url-address:
  address-list:
    - address: ${IMAGE_SERVER_1:https://hadoop-001:18001}
      max-token-count: 80
      token-bucket-key: address-1
      rate: 8
    - address: ${IMAGE_SERVER_2:https://hadoop-002:18001}
      max-token-count: 80
      token-bucket-key: address-2
      rate: 8

minio:
  endpoint: ${MINIO_ENDPOINT:http://hadoop-001:9000}
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  secret-key: ${MINIO_SECRET_KEY:minioadmin}
  bucket-name: ${MINIO_BUCKET:flood-images}

async:
  virtual-thread:
    enabled: true
    name-prefix: "kafka-async-vpn-"
  platform-thread:
    core-pool-size: 8
    max-pool-size: 40
  processing:
    timeout-seconds: 400
    batch-size: 80
    parallel-enabled: true

common:
  sync-upload-max-size: 10
  async-upload-max-size: 1000
  minio-public-url: ${MINIO_PUBLIC_URL:http://hadoop-001:9000}  # MinIO公共访问URL，用于URL替换功能