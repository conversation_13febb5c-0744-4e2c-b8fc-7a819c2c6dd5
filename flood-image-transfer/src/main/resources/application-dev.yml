spring:
  kafka:
    listener:
      ack-mode: manual
    bootstrap-servers: ${kafka_bootstrap_servers:192.168.106.122:9092,192.168.106.123:9092,192.168.106.124:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: submerge-tracking-water-group
      auto-offset-reset: latest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      concurrency: 20
      retry-concurrency: 5
      max-poll-records: 300
      session-timeout-ms: 30000
      heartbeat-interval-ms: 3000
      max-poll-interval-ms: 300000
      fetch-min-size: 1
      fetch-max-wait: 500
      # JSON反序列化配置
      trusted-packages: com.sdses.ai.imagetransfer.entity
      use-type-info-headers: false
      async-processing:
        enabled: true
        timeout-seconds: 60
      retry:
        async-processing:
          enabled: true
          timeout-seconds: 120
    topics:
      image-events: common_event_alarm
      retry-topic: image_retry_topic
      resource-topic: common_event_resource
      test-events: test-events           # 测试事件主题
      test-string: test-string           # 测试字符串主题

  # 单数据源配置（直接使用 spring.datasource）
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **********************************************************************************************************************************************************
    username: postgres
    password: Hh5xR1o!Xx
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 3
      min-idle: 3
      max-active: 10
      max-wait: 60000
      validation-query: SELECT 1

  data:
    redis:
      host: ***********
      port: 6399
      password: "Bigdata@2024"
      database: 15
      timeout: 3000
      connect-timeout: 3000
      pool:
        max-active: 15
        max-idle: 8
        min-idle: 3
        max-wait: 3000

image:
  download:
    connect-timeout: 3
    read-timeout: 15
    write-timeout: 15
    local-path: D:\Java_shensi\hyperfusion-flood-prevention\flood-image-transfer\doc\test_download
    trust-all-ssl: true
    monitor-interval: 5000

url-address:
  address-list:
    - address: https://************:18001
      max-token-count: 50
      token-bucket-key: address-1
      rate: 5
    - address: https://************:18001
      max-token-count: 50
      token-bucket-key: address-2
      rate: 5

minio:
  endpoint: http://************:9030
  access-key: t1q5wneMCiVK4eNDegNZ
  secret-key: oDtUqHc9dZRR2rEVRmQzAcRMp5SPBj4EkhHnspVR
  bucket-name: common-event-video-data

# 通用配置
common:
  minio-public-url: http://************:9030  # MinIO公共访问URL，用于URL替换功能

async:
  virtual-thread:
    enabled: true
    name-prefix: "kafka-async-dev-"
  platform-thread:
    core-pool-size: 5
    max-pool-size: 20
  processing:
    timeout-seconds: 180
    batch-size: 50
    parallel-enabled: true

