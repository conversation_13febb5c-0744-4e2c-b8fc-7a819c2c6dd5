spring:
  kafka:
    listener:
      ack-mode: manual
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:192.168.102.153:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: ${KAFKA_GROUP_ID:submerge-tracking-water-group}
      auto-offset-reset: ${KAFKA_AUTO_OFFSET_RESET:latest}
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer

      concurrency: ${KAFKA_CONCURRENCY:4}             # 测试环境并发数
      retry-concurrency: ${RETRY_CONCURRENCY:2}        # 测试环境重试并发数
      max-poll-records: 500       # 测试环境每次拉取的最大记录数
      session-timeout-ms: 30000   # 测试环境会话超时时间

      # JSON反序列化配置
      trusted-packages: com.sdses.ai.imagetransfer.entity
      use-type-info-headers: false

      heartbeat-interval-ms: 3000   # 测试环境心跳间隔时间
      max-poll-interval-ms: 300000  # 测试环境最大轮询间隔时间
      fetch-min-size: 10240             # 测试环境最小拉取字节数
      fetch-max-wait: 500           # 测试环境最大等待时间
      async-processing:
        enabled: true
        timeout-seconds: 120         # 测试环境异步处理超时时间
      retry:
        async-processing:
          enabled: true
          timeout-seconds: 180      # 测试环境重试异步处理超时时间
    topics:
      image-events: ${KAFKA_IMAGE_EVENT_TOPIC:test_common_event_alarm}
      retry-topic: ${KAFKA_RETRY_TOPIC:test_common_event_alarm_retry}
      resource-topic: ${KAFKA_RESOURCE_TOPIC:test_common_event_resource}

  # 动态数据源配置 - 使用 dynamic-datasource-spring-boot-starter
  datasource:
    dynamic:
      primary: pg1                     # 设置默认的数据源或者数据源组，默认值即为 master
      strict: false                    # 严格匹配数据源，默认false. true未匹配到指定数据源时抛异常，false使用默认数据源
      datasource:
        # 主数据源配置 (primary) - PostgreSQL
        pg1:
          driver-class-name: org.postgresql.Driver
          url: ****************************************************************************************************************************************************
          username: postgres
          password: postgres
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 10               # 初始连接数
            min-idle: 10                   # 最小空闲连接数
            max-active: 100                # 最大活跃连接数
            max-wait: 60000                # 获取连接最大等待时间(ms)
            validation-query: SELECT 1     # 连接验证查询
            test-while-idle: true          # 空闲时验证连接
            test-on-borrow: false          # 获取连接时验证
            test-on-return: false          # 归还连接时验证
            time-between-eviction-runs-millis: 60000  # 检测空闲连接间隔时间
            min-evictable-idle-time-millis: 300000    # 连接最小空闲时间
            max-evictable-idle-time-millis: 900000    # 连接最大空闲时间

        # 第二数据源配置 (secondary) - Doris 数据库
        doris1:
          driver-class-name: com.mysql.cj.jdbc.Driver  # Doris 使用 MySQL JDBC 驱动
          url: ********************************************************************************************************************************************************************************
          username: datacube_doris_sh
          password: Rs3k_Nx8Tz
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 5                # 初始连接数 (Doris 建议较少连接)
            min-idle: 5                    # 最小空闲连接数
            max-active: 30                 # 最大活跃连接数 (Doris 适中连接数)
            max-wait: 60000                # 获取连接最大等待时间(ms)
            validation-query: SELECT 1     # 连接验证查询
            test-while-idle: true          # 空闲时验证连接
            test-on-borrow: true           # Doris 建议获取连接时验证
            test-on-return: false          # 归还连接时验证
            time-between-eviction-runs-millis: 60000   # 检测空闲连接间隔时间
            min-evictable-idle-time-millis: 300000     # 连接最小空闲时间
            max-evictable-idle-time-millis: 600000     # Doris 连接最大空闲时间(10分钟)
            # Doris 特定配置
            connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
            filters: stat,wall                         # 启用监控和防火墙
            max-pool-prepared-statement-per-connection-size: 20
            use-global-data-source-stat: true         # 启用全局数据源统计

  # redis配置
  data:
    redis:
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6399}
      password: ${REDIS_PASSWORD:123456}
      database: 15
      timeout: 3000
      connect-timeout: 3000
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000

# 图片下载配置 - 性能优化版本
image:
  download:
    # 基本超时配置 (优化后)
    connect-timeout: 3          # 连接超时3秒 (业务指标要求)
    read-timeout: 15            # 读取超时15秒 (业务指标要求)
    write-timeout: 15           # 写入超时15秒 (业务指标要求)
    call-timeout: 30            # 整个请求超时30秒
    trust-all-ssl: true         # 信任所有SSL证书
    monitor-interval: 5000      # 监控间隔

    # 连接池配置 (新增)
    connection-pool:
      max-idle-connections: 50        # 最大空闲连接数 (优化)
      keep-alive-duration: 300        # 连接保活时间(秒)
      max-requests: 200               # 最大并发请求数
      max-requests-per-host: 50       # 每个主机最大并发请求数

    # 重试配置 (新增)
    retry:
      retry-on-connection-failure: true  # 连接失败时重试
      max-retries: 3                     # 最大重试次数
      retry-interval: 1000               # 重试间隔(毫秒)

    # 性能配置 (新增)
    performance:
      enable-gzip: true                  # 启用Gzip压缩
      enable-http2: true                 # 启用HTTP/2
      max-concurrent-downloads: 20       # 最大并发下载数
      max-image-size: 52428800          # 最大图片大小(50MB)
      enable-metrics: true               # 启用性能指标收集

# 三调服务器地址
url-address:
  address-list:
    - address: https://************:18001
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: address-1
      rate: ${BUCKET_RATE:30}
    - address: https://************:18001
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: address-2
      rate: ${BUCKET_RATE:30}
    - address: https://*************:18001
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: address-3
      rate: ${BUCKET_RATE:30}
    - address: https://*************:18001
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: address-4
      rate: ${BUCKET_RATE:30}
    # 蒙版图服务器
    - address: http://*************:20112
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: mask-1
      rate: ${BUCKET_RATE:30}
    - address: http://*************:20112
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: mask-2
      rate: ${BUCKET_RATE:30}
    - address: http://*************:20112
      max-token-count: ${BUCKET_MAX_TOKEN_COUNT:100}
      token-bucket-key: mask-3
      rate: ${BUCKET_RATE:30}

minio:
  endpoint: ${MINIO_ENDPOINT:http://**************:39000}
  access-key: ${MINIO_ACCESS_KEY:minio-admin}
  secret-key: ${MINIO_SECRET_KEY:2c_fUDeU}
  bucket-name: ${MINIO_BUCKET_NAME:workflow-test}
  # MinIO连接池配置 - 提升连接复用和性能
  connection-pool:
    enabled: true                     # 启用连接池
    max-connections: 100              # 最大连接数
    max-connections-per-route: 50     # 每个路由的最大连接数
    connection-timeout: 10000         # 连接超时时间(ms)
    socket-timeout: 30000             # Socket超时时间(ms)
    connection-request-timeout: 5000  # 连接请求超时时间(ms)
    keep-alive-time: 60000           # 连接保活时间(ms)
    max-idle-time: 300000            # 最大空闲时间(ms)
    validate-after-inactivity: true  # 空闲后验证连接
    retry-count: 3                   # 重试次数

async:
  virtual-thread:
    enabled: true
    name-prefix: "kafka-async-"
  platform-thread:
    core-pool-size: 20
    max-pool-size: 50
  processing:
    timeout-seconds: 300
    batch-size: 100
    parallel-enabled: true

common:
  sync-upload-max-size: 10
  async-upload-max-size: 1000
  minio-public-url: http://100.70.3.202:9030