package com.sdses.ai.imagetransfer.controller;

import com.sdses.ai.imagetransfer.service.async.AsyncProcessingMonitor;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 异步处理控制器
 * 提供异步处理监控和管理的REST接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@RestController
@RequestMapping("/api/async")
@RequiredArgsConstructor
public class AsyncProcessingController {

    private final AsyncProcessingMonitor asyncProcessingMonitor;

    /**
     * 获取异步处理性能统计
     * 
     * @return 性能统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getPerformanceStats() {
        AsyncProcessingMonitor.PerformanceStats stats = asyncProcessingMonitor.getPerformanceStats();
        
        Map<String, Object> response = new HashMap<>();
        response.put("totalProcessedMessages", stats.getTotalProcessedMessages());
        response.put("totalSuccessMessages", stats.getTotalSuccessMessages());
        response.put("totalFailedMessages", stats.getTotalFailedMessages());
        response.put("successRate", String.format("%.2f%%", stats.getSuccessRate()));
        response.put("averageProcessingTimeMs", String.format("%.2f", stats.getAverageProcessingTimeMs()));
        response.put("lastProcessingTime", stats.getLastProcessingTime());
        response.put("threadPoolCount", stats.getThreadPoolStats().size());
        
        if (stats.getLastProcessingResult() != null) {
            Map<String, Object> lastResult = new HashMap<>();
            lastResult.put("totalMessages", stats.getLastProcessingResult().getTotalMessages());
            lastResult.put("successCount", stats.getLastProcessingResult().getSuccessCount());
            lastResult.put("failedCount", stats.getLastProcessingResult().getFailedCount());
            lastResult.put("processingTimeMs", stats.getLastProcessingResult().getProcessingTime().toMillis());
            lastResult.put("messagesPerSecond", String.format("%.2f", stats.getLastProcessingResult().getMessagesPerSecond()));
            lastResult.put("parallelProcessing", stats.getLastProcessingResult().isParallelProcessing());
            response.put("lastProcessingResult", lastResult);
        }
        
        return response;
    }

    /**
     * 获取线程池状态
     * 
     * @return 线程池状态信息
     */
    @GetMapping("/thread-pools")
    public Map<String, Object> getThreadPoolStats() {
        AsyncProcessingMonitor.PerformanceStats stats = asyncProcessingMonitor.getPerformanceStats();
        
        Map<String, Object> response = new HashMap<>();
        response.put("threadPools", stats.getThreadPoolStats());
        response.put("totalPools", stats.getThreadPoolStats().size());
        
        return response;
    }

    /**
     * 检查异步处理健康状态
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Map<String, Object> getHealthStatus() {
        boolean isHealthy = asyncProcessingMonitor.isHealthy();
        AsyncProcessingMonitor.PerformanceStats stats = asyncProcessingMonitor.getPerformanceStats();
        
        Map<String, Object> response = new HashMap<>();
        response.put("healthy", isHealthy);
        response.put("status", isHealthy ? "UP" : "DOWN");
        
        Map<String, Object> details = new HashMap<>();
        details.put("totalMessages", stats.getTotalProcessedMessages());
        details.put("successRate", stats.getSuccessRate());
        details.put("lastProcessingTime", stats.getLastProcessingTime());
        details.put("threadPoolsActive", stats.getThreadPoolStats().size());
        
        response.put("details", details);
        
        return response;
    }

    /**
     * 重置统计信息
     * 
     * @return 操作结果
     */
    @PostMapping("/reset-stats")
    public Map<String, Object> resetStats() {
        asyncProcessingMonitor.resetStats();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "统计信息已重置");
        response.put("timestamp", java.time.LocalDateTime.now());
        
        return response;
    }

    /**
     * 获取异步处理配置信息
     * 
     * @return 配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getAsyncConfig() {
        Map<String, Object> response = new HashMap<>();
        
        // 这里可以添加从配置文件读取的异步处理配置信息
        Map<String, Object> virtualThreadConfig = new HashMap<>();
        virtualThreadConfig.put("enabled", true);
        virtualThreadConfig.put("description", "JDK 21虚拟线程，提供高并发处理能力");
        
        Map<String, Object> processingConfig = new HashMap<>();
        processingConfig.put("parallelEnabled", true);
        processingConfig.put("batchSize", 100);
        processingConfig.put("timeoutSeconds", 300);
        
        response.put("virtualThread", virtualThreadConfig);
        response.put("processing", processingConfig);
        response.put("jdkVersion", System.getProperty("java.version"));
        response.put("availableProcessors", Runtime.getRuntime().availableProcessors());
        
        return response;
    }

    /**
     * 获取JVM和系统信息
     * 
     * @return 系统信息
     */
    @GetMapping("/system-info")
    public Map<String, Object> getSystemInfo() {
        Runtime runtime = Runtime.getRuntime();
        
        Map<String, Object> response = new HashMap<>();
        response.put("jdkVersion", System.getProperty("java.version"));
        response.put("jvmName", System.getProperty("java.vm.name"));
        response.put("availableProcessors", runtime.availableProcessors());
        response.put("maxMemoryMB", runtime.maxMemory() / 1024 / 1024);
        response.put("totalMemoryMB", runtime.totalMemory() / 1024 / 1024);
        response.put("freeMemoryMB", runtime.freeMemory() / 1024 / 1024);
        response.put("usedMemoryMB", (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024);
        
        // 虚拟线程支持检查
        boolean virtualThreadSupported = false;
        try {
            // 检查是否支持虚拟线程 (JDK 21+)
            Thread.class.getMethod("ofVirtual");
            virtualThreadSupported = true;
        } catch (NoSuchMethodException e) {
            // JDK版本不支持虚拟线程
        }
        
        response.put("virtualThreadSupported", virtualThreadSupported);
        response.put("timestamp", java.time.LocalDateTime.now());
        
        return response;
    }
}
