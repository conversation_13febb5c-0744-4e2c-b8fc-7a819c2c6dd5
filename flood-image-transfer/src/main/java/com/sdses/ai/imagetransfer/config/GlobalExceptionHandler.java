package com.sdses.ai.imagetransfer.config;

import com.sdses.ai.imagetransfer.exception.ApiException;
import com.sdses.ai.imagetransfer.exception.BusinessException;
import com.sdses.ai.imagetransfer.exception.DataNotFoundException;
import com.sdses.ai.imagetransfer.exception.ValidationException;
import com.sdses.ai.imagetransfer.model.ResVoT;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 统一处理应用程序中的各种异常，返回标准化的错误响应
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     * 
     * @param ex 业务异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleBusinessException(BusinessException ex, HttpServletRequest request) {
        log.warn("业务异常: {} - 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        ResVoT<Object> response = ResVoT.error(ex.getErrorMessage(), ex.getErrorCode(), ex.getContext());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理API异常
     * 
     * @param ex API异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(ApiException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleApiException(ApiException ex, HttpServletRequest request) {
        log.warn("API异常: {} - 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        ResVoT<Object> response = ResVoT.error(ex.getErrorMessage(), ex.getErrorCode());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理数据未找到异常
     * 
     * @param ex 数据未找到异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(DataNotFoundException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleDataNotFoundException(DataNotFoundException ex, HttpServletRequest request) {
        log.warn("数据未找到异常: {} - 请求路径: {}", ex.getMessage(), request.getRequestURI());
        
        ResVoT<Object> response = ResVoT.error(ex.getErrorMessage(), ex.getErrorCode(), ex.getContext());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理参数验证异常
     * 
     * @param ex 验证异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleValidationException(ValidationException ex, HttpServletRequest request) {
        log.warn("参数验证异常: {} - 请求路径: {}", ex.getMessage(), request.getRequestURI());
        
        ResVoT<Object> response = ResVoT.error(ex.getErrorMessage(), ex.getErrorCode(), ex.getContext());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理Spring参数验证异常
     * 
     * @param ex 方法参数验证异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex, HttpServletRequest request) {
        log.warn("方法参数验证异常 - 请求路径: {}", request.getRequestURI(), ex);
        
        Map<String, String> validationErrors = new HashMap<>();
        ex.getBindingResult().getFieldErrors().forEach(error -> 
            validationErrors.put(error.getField(), error.getDefaultMessage())
        );

        ResVoT<Object> response = ResVoT.error("参数验证失败", "400", validationErrors);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理方法参数类型不匹配异常
     * 
     * @param ex 方法参数类型不匹配异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex, HttpServletRequest request) {
        log.warn("方法参数类型不匹配异常: {} - 请求路径: {}", ex.getMessage(), request.getRequestURI());
        
        String message = String.format("参数 '%s' 类型错误，期望类型: %s", 
                                     ex.getName(), 
                                     ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "未知");

        ResVoT<Object> response = ResVoT.error(message, "400");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理IllegalArgumentException
     * 
     * @param ex 非法参数异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleIllegalArgumentException(IllegalArgumentException ex, HttpServletRequest request) {
        log.warn("非法参数异常: {} - 请求路径: {}", ex.getMessage(), request.getRequestURI());

        ResVoT<Object> response = ResVoT.error("参数错误: " + ex.getMessage(), "400");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理RuntimeException
     * 
     * @param ex 运行时异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleRuntimeException(RuntimeException ex, HttpServletRequest request) {
        log.error("运行时异常: {} - 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);

        ResVoT<Object> response = ResVoT.error("系统运行时异常: " + ex.getMessage(), "500");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理数据库操作异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(DataAccessException.class)
    public ResVoT<?> handleDataAccessException(DataAccessException e) {
        log.error("数据库操作失败", e);
        return ResVoT.error("数据库服务异常", "500");
    }

    /**
     * 处理所有其他异常
     * 
     * @param ex 异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResVoT<Object>> handleGenericException(Exception ex, HttpServletRequest request) {
        log.error("系统异常: {} - 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);

        ResVoT<Object> response = ResVoT.error("系统内部错误，请联系管理员", "500");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

}
