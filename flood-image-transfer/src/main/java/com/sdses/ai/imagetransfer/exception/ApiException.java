package com.sdses.ai.imagetransfer.exception;

/**
 * API异常类
 * 用于处理API调用相关的异常情况
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
public class ApiException extends BusinessException {

    /**
     * 构造函数 - 仅包含错误消息
     * 
     * @param message 错误消息
     */
    public ApiException(String message) {
        super("API_ERROR", message);
    }

    /**
     * 构造函数 - 包含错误代码和消息
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     */
    public ApiException(String errorCode, String message) {
        super(errorCode, message);
    }

    /**
     * 构造函数 - 包含错误消息和原因异常
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public ApiException(String message, Throwable cause) {
        super("API_ERROR", message, cause);
    }

    /**
     * 构造函数 - 包含错误代码、消息和原因异常
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public ApiException(String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }

    // 静态工厂方法

    /**
     * 创建API异常 - 仅消息
     */
    public static ApiException of(String message) {
        return new ApiException(message);
    }

    /**
     * 创建API异常 - 错误代码和消息
     */
    public static ApiException of(String errorCode, String message) {
        return new ApiException(errorCode, message);
    }

    /**
     * 创建API异常 - 消息和原因
     */
    public static ApiException of(String message, Throwable cause) {
        return new ApiException(message, cause);
    }

    /**
     * 创建API异常 - 错误代码、消息和原因
     */
    public static ApiException of(String errorCode, String message, Throwable cause) {
        return new ApiException(errorCode, message, cause);
    }
}
