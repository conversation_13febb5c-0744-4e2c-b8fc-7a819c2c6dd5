package com.sdses.ai.imagetransfer.exception;

import lombok.Getter;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况，提供统一的异常处理机制
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Getter
public class BusinessException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 错误消息
     */
    private final String errorMessage;

    /**
     * 附加上下文信息
     */
    private final Object context;

    /**
     * 构造函数 - 仅包含错误消息
     * 
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.errorCode = "500";
        this.errorMessage = message;
        this.context = null;
    }

    /**
     * 构造函数 - 包含错误代码和消息
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     */
    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.context = null;
    }

    /**
     * 构造函数 - 包含错误代码、消息和上下文
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param context 附加上下文信息
     */
    public BusinessException(String errorCode, String message, Object context) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.context = context;
    }

    /**
     * 构造函数 - 包含错误消息和原因异常
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "500";
        this.errorMessage = message;
        this.context = null;
    }

    /**
     * 构造函数 - 包含错误代码、消息和原因异常
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public BusinessException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.context = null;
    }

    /**
     * 构造函数 - 完整参数
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param context 附加上下文信息
     * @param cause 原因异常
     */
    public BusinessException(String errorCode, String message, Object context, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.context = context;
    }

    // 静态工厂方法，提供便捷的异常创建方式

    /**
     * 创建业务异常 - 仅消息
     */
    public static BusinessException of(String message) {
        return new BusinessException(message);
    }

    /**
     * 创建业务异常 - 错误代码和消息
     */
    public static BusinessException of(String errorCode, String message) {
        return new BusinessException(errorCode, message);
    }

    /**
     * 创建业务异常 - 错误代码、消息和上下文
     */
    public static BusinessException of(String errorCode, String message, Object context) {
        return new BusinessException(errorCode, message, context);
    }

    /**
     * 创建业务异常 - 消息和原因
     */
    public static BusinessException of(String message, Throwable cause) {
        return new BusinessException(message, cause);
    }

    /**
     * 创建业务异常 - 错误代码、消息和原因
     */
    public static BusinessException of(String errorCode, String message, Throwable cause) {
        return new BusinessException(errorCode, message, cause);
    }

    @Override
    public String toString() {
        return String.format("BusinessException{errorCode='%s', errorMessage='%s', context=%s}", 
                           errorCode, errorMessage, context);
    }
}
