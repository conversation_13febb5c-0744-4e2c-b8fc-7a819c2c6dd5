package com.sdses.ai.imagetransfer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sdses.ai.imagetransfer.common.dto.QueryResultListDTO;
import com.sdses.ai.imagetransfer.common.vo.UploadVO;
import com.sdses.ai.imagetransfer.entity.CommonEventResourceLast;
import com.sdses.ai.imagetransfer.entity.EventMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-14 13:55
 */
public interface ApiService {

    CommonEventResourceLast queryResult(String eventId);

    List<UploadVO> syncUpload(List<EventMessage> messageList);

    String asyncUpload(List<EventMessage> messageList);

    List<UploadVO> asyncStatus(String taskId);

    /**
     * 根据内码、外码分页查询结果集合(Doris)
     *
     * @param dto 查询参数DTO
     * @return 分页结果
     */
    IPage<CommonEventResourceLast> queryResultList(QueryResultListDTO dto);
}
