package com.sdses.ai.imagetransfer.utils;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁工具类
 * 基于Redisson实现，适配Spring Boot 3.5.0 + JDK 21
 * 用于控制多副本场景下的任务执行
 * <AUTHOR>
 * @create 2025-07-07
 */
@Slf4j
@Component
public class DistributedLockUtil {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 尝试获取锁并执行任务
     * @param lockKey 锁的key
     * @param waitTime 等待获取锁的时间（秒）
     * @param leaseTime 锁的持有时间（秒）
     * @param task 要执行的任务
     * @return 是否成功执行任务
     */
    public boolean tryLockAndExecute(String lockKey, long waitTime, long leaseTime, Runnable task) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean acquired = false;
        
        try {
            log.info("尝试获取分布式锁: {}", lockKey);
            acquired = lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
            
            if (acquired) {
                log.info("成功获取分布式锁: {}，开始执行任务", lockKey);
                long startTime = System.currentTimeMillis();
                
                task.run();
                
                long endTime = System.currentTimeMillis();
                log.info("任务执行完成，耗时: {}ms", endTime - startTime);
                return true;
            } else {
                log.info("未能获取分布式锁: {}，跳过任务执行", lockKey);
                return false;
            }
            
        } catch (InterruptedException e) {
            log.warn("获取分布式锁被中断: {}", lockKey, e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.error("执行分布式锁任务失败: {}", lockKey, e);
            return false;
        } finally {
            if (acquired && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                    log.info("释放分布式锁: {}", lockKey);
                } catch (Exception e) {
                    log.error("释放分布式锁失败: {}", lockKey, e);
                }
            }
        }
    }

    /**
     * 尝试获取锁并执行有返回值的任务
     * @param lockKey 锁的key
     * @param waitTime 等待获取锁的时间（秒）
     * @param leaseTime 锁的持有时间（秒）
     * @param task 要执行的任务
     * @param <T> 返回值类型
     * @return 任务执行结果，如果未获取到锁则返回null
     */
    public <T> T tryLockAndExecute(String lockKey, long waitTime, long leaseTime, Supplier<T> task) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean acquired = false;
        
        try {
            log.info("尝试获取分布式锁: {}", lockKey);
            acquired = lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
            
            if (acquired) {
                log.info("成功获取分布式锁: {}，开始执行任务", lockKey);
                long startTime = System.currentTimeMillis();
                
                T result = task.get();
                
                long endTime = System.currentTimeMillis();
                log.info("任务执行完成，耗时: {}ms", endTime - startTime);
                return result;
            } else {
                log.info("未能获取分布式锁: {}，跳过任务执行", lockKey);
                return null;
            }
            
        } catch (InterruptedException e) {
            log.warn("获取分布式锁被中断: {}", lockKey, e);
            Thread.currentThread().interrupt();
            return null;
        } catch (Exception e) {
            log.error("执行分布式锁任务失败: {}", lockKey, e);
            return null;
        } finally {
            if (acquired && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                    log.info("释放分布式锁: {}", lockKey);
                } catch (Exception e) {
                    log.error("释放分布式锁失败: {}", lockKey, e);
                }
            }
        }
    }

    /**
     * 使用默认参数执行定时任务
     * 等待时间: 1秒，持有时间: 10分钟
     * @param lockKey 锁的key
     * @param task 要执行的任务
     * @return 是否成功执行任务
     */
    public boolean executeScheduledTask(String lockKey, Runnable task) {
        return tryLockAndExecute(lockKey, 1, 600, task); // 等待1秒，持有10分钟
    }

    /**
     * 检查锁是否存在
     * @param lockKey 锁的key
     * @return 锁是否存在
     */
    public boolean isLocked(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        return lock.isLocked();
    }

    /**
     * 强制释放锁（谨慎使用）
     * @param lockKey 锁的key
     * @return 是否成功释放
     */
    public boolean forceUnlock(String lockKey) {
        try {
            RLock lock = redissonClient.getLock(lockKey);
            if (lock.isLocked()) {
                lock.forceUnlock();
                log.warn("强制释放分布式锁: {}", lockKey);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("强制释放分布式锁失败: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 获取锁的剩余时间（毫秒）
     * @param lockKey 锁的key
     * @return 剩余时间，-1表示永不过期，-2表示锁不存在
     */
    public long getLockRemainingTime(String lockKey) {
        try {
            RLock lock = redissonClient.getLock(lockKey);
            return lock.remainTimeToLive();
        } catch (Exception e) {
            log.error("获取锁剩余时间失败: {}", lockKey, e);
            return -2;
        }
    }

    /**
     * 生成定时任务锁的key
     * @param taskName 任务名称
     * @return 锁的key
     */
    public static String generateScheduledTaskLockKey(String taskName) {
        return "scheduled:lock:" + taskName;
    }

    /**
     * 生成业务锁的key
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 锁的key
     */
    public static String generateBusinessLockKey(String businessType, String businessId) {
        return "business:lock:" + businessType + ":" + businessId;
    }
}
