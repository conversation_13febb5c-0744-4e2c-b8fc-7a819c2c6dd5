package com.sdses.ai.imagetransfer.config;

import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PreDestroy;
import java.util.concurrent.TimeUnit;

/**
 * MinIO连接池管理器
 * 配置和管理MinIO客户端的连接池
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */
@Slf4j
@Configuration
public class MinioConnectionPoolManager {

    @Autowired
    private MinioConfig minioConfig;

    private OkHttpClient httpClient;

    /**
     * 创建带连接池的MinIO客户端
     */
    @Bean
    public MinioClient minioClientWithPool() {
        try {
            log.info("初始化MinIO连接池客户端");
            
            MinioConfig.ConnectionPool poolConfig = minioConfig.getConnectionPool();
            
            if (poolConfig.isEnabled()) {
                // 创建连接池
                ConnectionPool connectionPool = new ConnectionPool(
                        poolConfig.getMaxConnections(),
                        poolConfig.getKeepAliveTime(),
                        TimeUnit.MILLISECONDS
                );

                // 创建OkHttpClient with 连接池
                httpClient = new OkHttpClient.Builder()
                        .connectionPool(connectionPool)
                        .connectTimeout(poolConfig.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                        .readTimeout(poolConfig.getSocketTimeout(), TimeUnit.MILLISECONDS)
                        .writeTimeout(poolConfig.getSocketTimeout(), TimeUnit.MILLISECONDS)
                        .retryOnConnectionFailure(true)
                        .build();

                log.info("MinIO连接池配置: 最大连接数={}, 连接超时={}ms, Socket超时={}ms, 保活时间={}ms",
                        poolConfig.getMaxConnections(),
                        poolConfig.getConnectionTimeout(),
                        poolConfig.getSocketTimeout(),
                        poolConfig.getKeepAliveTime());

                // 创建MinIO客户端
                MinioClient client = MinioClient.builder()
                        .endpoint(minioConfig.getEndpoint())
                        .credentials(minioConfig.getAccessKey(), minioConfig.getSecretKey())
                        .httpClient(httpClient)
                        .build();

                log.info("MinIO连接池客户端创建成功");
                return client;
                
            } else {
                log.info("MinIO连接池未启用，使用默认客户端");
                return MinioClient.builder()
                        .endpoint(minioConfig.getEndpoint())
                        .credentials(minioConfig.getAccessKey(), minioConfig.getSecretKey())
                        .build();
            }
            
        } catch (Exception e) {
            log.error("MinIO连接池客户端创建失败", e);
            throw new RuntimeException("MinIO连接池客户端创建失败", e);
        }
    }

    /**
     * 获取连接池统计信息
     */
    public String getConnectionPoolStats() {
        if (httpClient != null && httpClient.connectionPool() != null) {
            ConnectionPool pool = httpClient.connectionPool();
            return String.format("MinIO连接池状态 - 空闲连接: %d, 总连接: %d",
                    pool.idleConnectionCount(), pool.connectionCount());
        }
        return "MinIO连接池未启用或不可用";
    }

    /**
     * 检查连接池健康状态
     */
    public boolean isConnectionPoolHealthy() {
        try {
            if (httpClient != null && httpClient.connectionPool() != null) {
                ConnectionPool pool = httpClient.connectionPool();
                int totalConnections = pool.connectionCount();
                int idleConnections = pool.idleConnectionCount();
                
                // 检查连接池是否健康
                boolean healthy = totalConnections <= minioConfig.getConnectionPool().getMaxConnections()
                        && idleConnections >= 0;
                
                log.debug("MinIO连接池健康检查: 总连接={}, 空闲连接={}, 健康状态={}",
                        totalConnections, idleConnections, healthy);
                
                return healthy;
            }
            return true; // 如果没有连接池，认为是健康的
        } catch (Exception e) {
            log.warn("MinIO连接池健康检查失败", e);
            return false;
        }
    }

    /**
     * 清理连接池中的空闲连接
     */
    public void evictIdleConnections() {
        try {
            if (httpClient != null && httpClient.connectionPool() != null) {
                httpClient.connectionPool().evictAll();
                log.info("MinIO连接池空闲连接清理完成");
            }
        } catch (Exception e) {
            log.warn("MinIO连接池空闲连接清理失败", e);
        }
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void cleanup() {
        try {
            if (httpClient != null) {
                log.info("开始清理MinIO连接池资源");
                
                // 关闭连接池
                if (httpClient.connectionPool() != null) {
                    httpClient.connectionPool().evictAll();
                }
                
                // 关闭调度器
                if (httpClient.dispatcher() != null) {
                    httpClient.dispatcher().executorService().shutdown();
                }
                
                log.info("MinIO连接池资源清理完成");
            }
        } catch (Exception e) {
            log.warn("MinIO连接池资源清理失败", e);
        }
    }

    /**
     * 获取连接池配置信息
     */
    public String getConnectionPoolConfig() {
        MinioConfig.ConnectionPool config = minioConfig.getConnectionPool();
        return String.format(
                "MinIO连接池配置 - 启用: %s, 最大连接: %d, 连接超时: %dms, Socket超时: %dms, 保活时间: %dms",
                config.isEnabled(),
                config.getMaxConnections(),
                config.getConnectionTimeout(),
                config.getSocketTimeout(),
                config.getKeepAliveTime()
        );
    }
}
