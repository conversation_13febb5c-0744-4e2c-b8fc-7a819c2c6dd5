package com.sdses.ai.imagetransfer.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("pg1")
public interface FloodImageTransferHisMapper extends BaseMapper<FloodImageTransferHis> {

    Integer saveOrUpdate(@Param("info") FloodImageTransferHis info);

    Integer saveOrUpdateRetry(@Param("info") FloodImageTransferHis info);

    List<FloodImageTransferHis> list48HoursData(@Param("minRetryCount") Integer minRetryCount,
                                                @Param("maxRetryCount") Integer maxRetryCount,
                                                @Param("time") String time,
                                                @Param("disposeTypeList") List<String> disposeTypeList);

    Integer deleteDataByHours(@Param("hours") Integer hours, @Param("time") String time);

    List<FloodImageTransferHis> listByParams(@Param("minRetryCount") Integer minRetryCount, @Param("maxRetryCount") Integer maxRetryCount, @Param("time") String time, @Param("disposeType") String disposeType, @Param("hours") Integer hours);

    List<FloodImageTransferHis> list15MinData(@Param("minRetryCount") Integer minRetryCount, @Param("maxRetryCount") Integer maxRetryCount, @Param("formattedDateTime") String formattedDateTime, @Param("disposeTypeList") List<String> disposeTypeList);
}
