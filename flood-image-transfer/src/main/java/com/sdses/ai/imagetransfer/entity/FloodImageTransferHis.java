package com.sdses.ai.imagetransfer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 图片转存操作记录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "flood_image_transfer_his")
public class FloodImageTransferHis {
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @TableField(value = "kafka_msg")
    private String kafkaMsg;

    @TableField(value = "minio_image_url")
    private String minioImageUrl;

    @TableField(value = "dispose_type")
    private String disposeType;

    @TableField(value = "retry_count")
    private int retryCount;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(value = "event_time")
    private LocalDateTime eventTime;

    @TableField(value = "task_id")
    private String taskId;

    @TableField(value = "data_source")
    private String dataSource;
}