package com.sdses.ai.imagetransfer.service.logic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sdses.ai.imagetransfer.common.bo.LogicBO;
import com.sdses.ai.imagetransfer.common.enums.DisposeTypeEnum;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import com.sdses.ai.imagetransfer.service.FloodImageTransferHisService;
import com.sdses.ai.imagetransfer.service.JsonService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-07 18:17
 */
@Slf4j
@Service
public class ScheduledService {

    @Resource
    private JsonService jsonService;
    @Resource
    private LogicService logicService;
    @Resource
    private FloodImageTransferHisService hisService;


    // 处理失败数据（异步优化版本）
    public void taskErrorData() {
        LocalDateTime taskStartTime = LocalDateTime.now();
        String taskId = "task_error_" + System.currentTimeMillis();

        log.info("【定时任务】开始执行error数据补偿任务 - taskId: {}, 触发时间: {}", taskId, taskStartTime);

        try {
            // 定义格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // LocalDateTime 转字符串
            String formattedDateTime = taskStartTime.format(formatter);

            // 同步执行数据库查询操作
            log.info("【定时任务】开始查询数据库 - taskId: {}", taskId);
            List<String> disposeTypeList = List.of(DisposeTypeEnum.ERROR.getCode());
            List<FloodImageTransferHis> hisList = hisService.list48HoursData(1, 5,
                    formattedDateTime, disposeTypeList);

            long queryTime = System.currentTimeMillis();
            log.info("【定时任务】数据库查询完成 - taskId: {}, 查询到记录数: {}, 耗时: {}ms",
                    taskId, hisList != null ? hisList.size() : 0,
                    queryTime - taskStartTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());

            // 异步执行图片导出操作
            if (hisList != null && !hisList.isEmpty()) {
                log.info("【定时任务】开始异步处理图片导出 - taskId: {}, 待处理记录数: {}", taskId, hisList.size());

                // 使用虚拟线程异步执行图片导出
                Thread.startVirtualThread(() -> {
                    try {
                        log.info("【异步任务】虚拟线程开始执行图片导出 - taskId: {}, 线程: {}",
                                taskId, Thread.currentThread().getName());

                        long asyncStartTime = System.currentTimeMillis();

                        // 异步执行图片导出逻辑
                        this.exportImagesAsync(hisList, taskId);

                        long asyncEndTime = System.currentTimeMillis();
                        log.info("【异步任务】图片导出完成 - taskId: {}, 总耗时: {}ms, 线程: {}",
                                taskId, asyncEndTime - asyncStartTime, Thread.currentThread().getName());

                    } catch (Exception e) {
                        log.error("【异步任务】图片导出异常 - taskId: {}, 线程: {}",
                                taskId, Thread.currentThread().getName(), e);
                    }
                });

                log.info("【定时任务】异步任务已提交 - taskId: {}, 定时任务执行完成", taskId);
            } else {
                log.info("【定时任务】无需处理的error数据 - taskId: {}", taskId);
            }

        } catch (Exception e) {
            log.error("【定时任务】补偿error数据的定时任务执行异常 - taskId: {}, 触发时间: {}",
                    taskId, taskStartTime, e);
        }
    }

    public void taskDataByParams(Integer hours, String disposeType, Integer minRetryCount, Integer maxRetryCount) {
        try {
            LocalDateTime time = LocalDateTime.now();
            // 定义格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // LocalDateTime 转字符串
            String formattedDateTime = time.format(formatter);

            // 查询DB 48小时内的记录
            List<FloodImageTransferHis> hisList = hisService.listByParams(minRetryCount, maxRetryCount,
                    formattedDateTime, disposeType, hours);

            this.exportImages(hisList);
        } catch (Exception e) {
            log.error("定时任务执行失败", e);
        }
    }

    // 处理start数据（异步优化版本）
    public void taskStartData() {
        LocalDateTime taskStartTime = LocalDateTime.now();
        String taskId = "task_start_" + System.currentTimeMillis();

        log.info("【定时任务】开始执行start数据补偿任务 - taskId: {}, 触发时间: {}", taskId, taskStartTime);

        try {
            // 定义格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // LocalDateTime 转字符串
            String formattedDateTime = taskStartTime.format(formatter);

            // 同步执行数据库查询操作
            log.info("【定时任务】开始查询数据库 - taskId: {}", taskId);
            List<String> disposeTypeList = List.of(DisposeTypeEnum.START.getCode());
            List<FloodImageTransferHis> hisList = hisService.list15MinData(0, 5,
                    formattedDateTime, disposeTypeList);

            long queryTime = System.currentTimeMillis();
            log.info("【定时任务】数据库查询完成 - taskId: {}, 查询到记录数: {}, 耗时: {}ms",
                    taskId, hisList != null ? hisList.size() : 0,
                    queryTime - taskStartTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());

            // 异步执行图片导出操作
            if (hisList != null && !hisList.isEmpty()) {
                log.info("【定时任务】开始异步处理图片导出 - taskId: {}, 待处理记录数: {}", taskId, hisList.size());

                // 使用虚拟线程异步执行图片导出
                Thread.startVirtualThread(() -> {
                    try {
                        log.info("【异步任务】虚拟线程开始执行图片导出 - taskId: {}, 线程: {}",
                                taskId, Thread.currentThread().getName());

                        long asyncStartTime = System.currentTimeMillis();

                        // 异步执行图片导出逻辑
                        this.exportImagesAsync(hisList, taskId);

                        long asyncEndTime = System.currentTimeMillis();
                        log.info("【异步任务】图片转存完成 - taskId: {}, 总耗时: {}ms, 线程: {}",
                                taskId, asyncEndTime - asyncStartTime, Thread.currentThread().getName());

                    } catch (Exception e) {
                        log.error("【异步任务】图片转存异常 - taskId: {}, 线程: {}",
                                taskId, Thread.currentThread().getName(), e);
                    }
                });

                log.info("【定时任务】异步任务已提交 - taskId: {}, 定时任务执行完成", taskId);
            } else {
                log.info("【定时任务】无需处理的start数据 - taskId: {}", taskId);
            }

        } catch (Exception e) {
            log.error("【定时任务】补偿start数据的定时任务执行异常 - taskId: {}, 触发时间: {}",
                    taskId, taskStartTime, e);
        }
    }


    // 定时任务核心处理逻辑（原同步版本，保留给其他方法使用）
    private void exportImages(List<FloodImageTransferHis> hisList) {
        if (CollUtil.isEmpty(hisList)) {
            log.info("DB中不存在需要处理{}类型数据", DisposeTypeEnum.ERROR.getCode());
            return;
        }

        // 判断imageUrl是否存在三调图片服务器中
        for (FloodImageTransferHis hisInfo : hisList) {
            LocalDateTime startTime = LocalDateTime.now();
            String kafkaMsg = hisInfo.getKafkaMsg();
            if (StrUtil.isEmpty(kafkaMsg)) {
                log.error("kafkaMsg为空，跳过处理: {}", hisInfo.getId());
                continue;
            }

            // JSON反序列化为实体类
            EventMessage eventMessage = jsonService.json2EventMessageBean(kafkaMsg);

            // 判断是否有合法的imag_url
            LogicBO checkLogicBO = logicService.checkImageUrl(eventMessage, hisInfo, startTime);
            boolean checkFlag = checkLogicBO.isContinue();
            if (!checkFlag) {
                return;
            }

            // 判断服务器地址、获取令牌和上传minio
            logicService.checkExistAndUpload(eventMessage, startTime, hisInfo, false);
        }
    }

    /**
     * 异步版本的图片导出处理逻辑
     * 专门为虚拟线程异步执行设计，增强了监控和异常处理
     *
     * @param hisList 待处理的历史记录列表
     * @param taskId  任务ID，用于日志跟踪
     */
    private void exportImagesAsync(List<FloodImageTransferHis> hisList, String taskId) {
        if (CollUtil.isEmpty(hisList)) {
            log.info("【异步任务】无需处理的数据 - taskId: {}", taskId);
            return;
        }

        log.info("【异步任务】开始处理图片导出 - taskId: {}, 总记录数: {}", taskId, hisList.size());

        int successCount = 0;
        int failureCount = 0;
        int skipCount = 0;

        // 逐个处理历史记录
        for (int i = 0; i < hisList.size(); i++) {
            FloodImageTransferHis hisInfo = hisList.get(i);
            String eventId = hisInfo.getId();

            try {
                log.debug("【异步任务】处理记录 {}/{} - taskId: {}, eventId: {}",
                        i + 1, hisList.size(), taskId, eventId);

                LocalDateTime startTime = LocalDateTime.now();
                String kafkaMsg = hisInfo.getKafkaMsg();

                // 检查kafkaMsg是否为空
                if (StrUtil.isEmpty(kafkaMsg)) {
                    log.warn("【异步任务】kafkaMsg为空，跳过处理 - taskId: {}, eventId: {}", taskId, eventId);
                    skipCount++;
                    continue;
                }

                // JSON反序列化为实体类
                EventMessage eventMessage;
                try {
                    eventMessage = jsonService.json2EventMessageBean(kafkaMsg);
                } catch (Exception e) {
                    log.error("【异步任务】JSON反序列化失败 - taskId: {}, eventId: {}", taskId, eventId, e);
                    failureCount++;
                    continue;
                }

                // 判断是否有合法的image_url
                LogicBO checkLogicBO = logicService.checkImageUrl(eventMessage, hisInfo, startTime);
                boolean checkFlag = checkLogicBO.isContinue();
                if (!checkFlag) {
                    log.warn("【异步任务】图片URL检查失败 - taskId: {}, eventId: {}", taskId, eventId);
                    failureCount++;
                    continue;
                }

                // 执行核心业务逻辑：判断服务器地址、获取令牌和上传minio
                LogicBO result = logicService.checkExistAndUpload(eventMessage, startTime, hisInfo, false);

                if (result != null && result.isContinue()) {
                    successCount++;
                    log.debug("【异步任务】处理成功 - taskId: {}, eventId: {}", taskId, eventId);
                } else {
                    failureCount++;
                    log.warn("【异步任务】处理失败 - taskId: {}, eventId: {}", taskId, eventId);
                }

            } catch (Exception e) {
                failureCount++;
                log.error("【异步任务】处理记录时发生异常 - taskId: {}, eventId: {}", taskId, eventId, e);
            }
        }

        // 记录最终处理结果
        log.info("【异步任务】图片导出处理完成 - taskId: {}, 总数: {}, 成功: {}, 失败: {}, 跳过: {}",
                taskId, hisList.size(), successCount, failureCount, skipCount);

        // 如果失败率过高，记录警告日志
        double failureRate = (double) failureCount / hisList.size();
        if (failureRate > 0.5) {
            log.warn("【异步任务】失败率过高 - taskId: {}, 失败率: {:.2%}, 建议检查系统状态",
                    taskId, failureRate);
        }
    }

    // 处理未处理数据
    public void taskUntreatedData() {
        // 查询未处理数据

        //
    }
}
