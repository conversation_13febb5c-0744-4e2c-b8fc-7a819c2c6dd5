package com.sdses.ai.imagetransfer.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sdses.ai.imagetransfer.config.LocalDateTimeWithOffsetSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description topic:common_event_resource中的消息体
 * @create 2025-07-07 10:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.ALWAYS)
public class ResourceMessage {

    @JsonProperty("camera_index_code")
    private String cameraIndexCode;

    @JsonProperty("camera_channel_code")
    private String cameraChannelCode;

    @JsonProperty("camera_foreign_code")
    private String cameraForeignCode;

    @JsonProperty("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "Asia/Shanghai")
    @JsonSerialize(using = LocalDateTimeWithOffsetSerializer.class)
    private LocalDateTime startTime;

    @JsonProperty("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "Asia/Shanghai")
    @JsonSerialize(using = LocalDateTimeWithOffsetSerializer.class)
    private LocalDateTime endTime;

    @JsonProperty("event_time")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "Asia/Shanghai")
    @JsonSerialize(using = LocalDateTimeWithOffsetSerializer.class)
    private LocalDateTime eventTime;

    @JsonProperty("event_id")
    private String eventId;

    @JsonProperty("minio_video_url")
    private String minioVideoUrl;

    @JsonProperty("minio_image_url")
    private String minioImageUrl;

    @JsonProperty("status")
    private String status;

    @JsonProperty("image_retry")
    private Integer imageRetry;

    @JsonProperty("video_retry")
    private Integer videoRetry;

    @JsonProperty("image_msg")
    private String imageMsg;

    @JsonProperty("video_msg")
    private String videoMsg;

    @JsonProperty("duration")
    private long duration;

    @JsonProperty("source_system")
    private String sourceSystem;

    @JsonProperty("source_module")
    private String sourceModule;

    @JsonProperty("info")
    private String info;

}
