package com.sdses.ai.imagetransfer.controller;

import com.sdses.ai.imagetransfer.common.dto.DeleteDataDTO;
import com.sdses.ai.imagetransfer.common.dto.ProcessingDTO;
import com.sdses.ai.imagetransfer.model.ResVoT;
import com.sdses.ai.imagetransfer.service.ImageProcessingService;
import com.sdses.ai.imagetransfer.service.logic.ScheduledService;
import com.sdses.ai.imagetransfer.utils.DistributedLockUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人工调用接口
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api")
public class ManualProcessingController {

    private final ImageProcessingService processingService;
    private final ScheduledService scheduledService;
    private final DistributedLockUtil distributedLockUtil;


    /**
     * 人工处理的事件
     *
     * @param dto
     * @return
     */
    @PostMapping("/process-failed-events")
    public ResVoT<String> processFailedEvents(@RequestBody ProcessingDTO dto) {
        // 查询需要人工处理的事件
        Integer count = processingService.processFailedEvents(dto);

        String msg = String.format("操作成功 共成功处理%d条数据", count);
        return ResVoT.success(msg);
    }

    /**
     * 手动触发 定时任务
     *
     * @return
     */
    @GetMapping("/process-task")
    public ResVoT<String> processTask() {
        String lockKey = DistributedLockUtil.generateScheduledTaskLockKey("scheduled-task-15min");

        log.info("手动触发 定时任务开始执行，尝试获取分布式锁: {}", lockKey);

        boolean executed = distributedLockUtil.executeScheduledTask(lockKey, () -> {
            try {
                log.info("开始执行定时任务业务逻辑");
                scheduledService.taskErrorData();
                log.info("定时任务业务逻辑执行完成");
            } catch (Exception e) {
                log.error("定时任务执行失败", e);
                throw new RuntimeException("定时任务执行失败", e);
            }
        });

        if (executed) {
            log.info("手动触发定时任务执行成功");
        } else {
            log.info("手动触发定时任务跳过执行（其他实例正在执行或获取锁失败）");
        }
        return ResVoT.success("手动触发定时任务 成功");
    }

    /**
     * 手动触发 定时任务 带条件
     *
     * @return
     */
    @GetMapping("/process-task-by-hours")
    public ResVoT<String> processTaskByHours(@RequestParam(name = "hours", required = false) Integer hours,
                                             @RequestParam(name = "disposeType", required = false) String disposeType,
                                             @RequestParam(name = "minRetryCount")Integer minRetryCount,
                                             @RequestParam(name = "maxRetryCount")Integer maxRetryCount) {
        String lockKey = DistributedLockUtil.generateScheduledTaskLockKey("scheduled-task-15min");

        log.info("手动触发 定时任务开始执行，尝试获取分布式锁: {}", lockKey);

        boolean executed = distributedLockUtil.executeScheduledTask(lockKey, () -> {
            try {
                log.info("开始执行定时任务业务逻辑");
                scheduledService.taskDataByParams(hours, disposeType,minRetryCount,maxRetryCount);
                log.info("定时任务业务逻辑执行完成");
            } catch (Exception e) {
                log.error("定时任务执行失败", e);
                throw new RuntimeException("定时任务执行失败", e);
            }
        });

        if (executed) {
            log.info("手动触发定时任务执行成功");
        } else {
            log.info("手动触发定时任务跳过执行（其他实例正在执行或获取锁失败）");
        }
        return ResVoT.success("手动触发定时任务 成功");
    }

    /**
     * 定时删除指定小时之前的数据记录 物理删除 谨慎操作！！！
     *
     * @param dto
     * @return
     */
    @DeleteMapping("/delete-data")
    public ResVoT<String> deleteData(@RequestBody DeleteDataDTO dto) {
        Integer count = processingService.deleteData(dto);
        String msg = String.format("操作成功 共成功删除%d条数据", count);
        return ResVoT.success(msg);
    }

    /**
     * 处理指定记录 重新进行图片转存操作
     *
     * @param eventIds
     * @return
     */
    @PostMapping("/reprocess-event")
    public ResVoT<String> reprocessEvent(@RequestBody List<String> eventIds) {
        String msg = processingService.reprocessEvent(eventIds);
        return ResVoT.success(msg);
    }
}    