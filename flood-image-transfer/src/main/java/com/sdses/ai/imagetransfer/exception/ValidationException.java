package com.sdses.ai.imagetransfer.exception;

import java.util.List;
import java.util.Map;

/**
 * 参数验证异常类
 * 用于处理参数验证失败的情况
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
public class ValidationException extends BusinessException {

    /**
     * 构造函数 - 仅包含错误消息
     * 
     * @param message 错误消息
     */
    public ValidationException(String message) {
        super("VALIDATION_ERROR", message);
    }

    /**
     * 构造函数 - 包含错误消息和验证错误详情
     * 
     * @param message 错误消息
     * @param validationErrors 验证错误详情
     */
    public ValidationException(String message, Map<String, String> validationErrors) {
        super("VALIDATION_ERROR", message, validationErrors);
    }

    /**
     * 构造函数 - 包含错误消息和验证错误列表
     * 
     * @param message 错误消息
     * @param validationErrors 验证错误列表
     */
    public ValidationException(String message, List<String> validationErrors) {
        super("VALIDATION_ERROR", message, validationErrors);
    }

    /**
     * 构造函数 - 包含错误消息和原因异常
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public ValidationException(String message, Throwable cause) {
        super("VALIDATION_ERROR", message, cause);
    }

    // 静态工厂方法

    /**
     * 创建验证异常 - 仅消息
     */
    public static ValidationException of(String message) {
        return new ValidationException(message);
    }

    /**
     * 创建验证异常 - 消息和验证错误详情
     */
    public static ValidationException of(String message, Map<String, String> validationErrors) {
        return new ValidationException(message, validationErrors);
    }

    /**
     * 创建验证异常 - 消息和验证错误列表
     */
    public static ValidationException of(String message, List<String> validationErrors) {
        return new ValidationException(message, validationErrors);
    }

    /**
     * 创建验证异常 - 消息和原因
     */
    public static ValidationException of(String message, Throwable cause) {
        return new ValidationException(message, cause);
    }

    /**
     * 创建参数为空的验证异常
     */
    public static ValidationException parameterRequired(String parameterName) {
        return new ValidationException(String.format("Parameter '%s' is required", parameterName));
    }

    /**
     * 创建参数格式错误的验证异常
     */
    public static ValidationException parameterFormat(String parameterName, String expectedFormat) {
        return new ValidationException(String.format("Parameter '%s' format is invalid, expected: %s", 
                                                    parameterName, expectedFormat));
    }

    /**
     * 创建参数值超出范围的验证异常
     */
    public static ValidationException parameterOutOfRange(String parameterName, String range) {
        return new ValidationException(String.format("Parameter '%s' is out of range, expected: %s", 
                                                    parameterName, range));
    }
}
