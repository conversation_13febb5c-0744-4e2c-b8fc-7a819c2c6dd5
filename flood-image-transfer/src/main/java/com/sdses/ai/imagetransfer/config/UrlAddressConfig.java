package com.sdses.ai.imagetransfer.config;

import com.sdses.ai.imagetransfer.common.bo.AddressBO;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "url-address")
public class UrlAddressConfig {
    private List<AddressBO> addressList;  // 直接绑定列表

    // Getter 和 Setter
    public List<AddressBO> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<AddressBO> addressList) {
        this.addressList = addressList;
    }
}
