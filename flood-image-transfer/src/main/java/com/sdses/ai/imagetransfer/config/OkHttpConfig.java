package com.sdses.ai.imagetransfer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * OkHttp配置类
 * 用于配置图片下载的网络参数，优化下载性能
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "image.download")
public class OkHttpConfig {

    // 基本超时配置
    private int connectTimeout = 3;        // 连接超时(秒)
    private int readTimeout = 15;          // 读取超时(秒)
    private int writeTimeout = 10;         // 写入超时(秒)
    private int callTimeout = 30;          // 整个请求超时(秒)
    
    // SSL配置
    private boolean trustAllSsl = true;    // 是否信任所有SSL证书
    
    // 连接池配置
    private ConnectionPool connectionPool = new ConnectionPool();
    
    // 重试配置
    private Retry retry = new Retry();
    
    // 性能配置
    private Performance performance = new Performance();

    /**
     * 连接池配置
     */
    @Data
    public static class ConnectionPool {
        private int maxIdleConnections = 50;      // 最大空闲连接数 - 从20优化为50
        private long keepAliveDuration = 300;     // 连接保活时间(秒) - 从5分钟优化为5分钟
        private int maxRequests = 200;            // 最大并发请求数
        private int maxRequestsPerHost = 50;      // 每个主机最大并发请求数
    }

    /**
     * 重试配置
     */
    @Data
    public static class Retry {
        private boolean retryOnConnectionFailure = true;  // 连接失败时重试
        private int maxRetries = 3;                       // 最大重试次数
        private long retryInterval = 1000;               // 重试间隔(毫秒)
    }

    /**
     * 性能配置
     */
    @Data
    public static class Performance {
        private boolean enableGzip = true;                // 启用Gzip压缩
        private boolean enableHttp2 = true;               // 启用HTTP/2
        private int maxConcurrentDownloads = 20;          // 最大并发下载数
        private long maxImageSize = 50 * 1024 * 1024;    // 最大图片大小(50MB)
        private boolean enableMetrics = true;             // 启用性能指标收集
    }

    /**
     * 获取连接池保活时间(毫秒)
     */
    public long getKeepAliveDurationMillis() {
        return connectionPool.keepAliveDuration * 1000;
    }

    /**
     * 验证配置的合理性
     */
    public void validate() {
        if (connectTimeout <= 0 || connectTimeout > 60) {
            throw new IllegalArgumentException("connectTimeout must be between 1 and 60 seconds");
        }
        if (readTimeout <= 0 || readTimeout > 300) {
            throw new IllegalArgumentException("readTimeout must be between 1 and 300 seconds");
        }
        if (connectionPool.maxIdleConnections <= 0) {
            throw new IllegalArgumentException("maxIdleConnections must be positive");
        }
        if (connectionPool.maxRequests <= 0) {
            throw new IllegalArgumentException("maxRequests must be positive");
        }
        if (performance.maxConcurrentDownloads <= 0) {
            throw new IllegalArgumentException("maxConcurrentDownloads must be positive");
        }
    }

    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format(
            "OkHttp配置: 连接超时=%ds, 读取超时=%ds, 连接池=%d/%ds, 并发=%d/%d, HTTP/2=%s",
            connectTimeout, readTimeout,
            connectionPool.maxIdleConnections, connectionPool.keepAliveDuration,
            connectionPool.maxRequests, connectionPool.maxRequestsPerHost,
            performance.enableHttp2 ? "启用" : "禁用"
        );
    }
}
