package com.sdses.ai.imagetransfer.demo;

import com.sdses.ai.imagetransfer.utils.DistributedLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 分布式锁演示类
 * 适配Spring Boot 3.5.0 + JDK 21
 * 启动应用时自动运行演示
 * <AUTHOR>
 * @create 2025-07-07
 */
@Slf4j
@Component
public class DistributedLockDemo implements CommandLineRunner {

    @Autowired
    private DistributedLockUtil distributedLockUtil;

    @Override
    public void run(String... args) throws Exception {
        if (args.length > 0 && "demo".equals(args[0])) {
            log.info("=== 分布式锁功能演示开始 (Spring Boot 3.5.0 + JDK 21) ===");
            
            // 演示1：基本分布式锁功能
            demonstrateBasicLock();
            
            // 演示2：定时任务锁
            demonstrateScheduledTaskLock();
            
            // 演示3：并发场景
            demonstrateConcurrentScenario();
            
            // 演示4：锁状态检查
            demonstrateLockStatus();
            
            // 演示5：JDK 21虚拟线程
            demonstrateVirtualThreads();
            
            log.info("=== 分布式锁功能演示结束 ===");
        }
    }

    /**
     * 演示基本分布式锁功能
     */
    private void demonstrateBasicLock() {
        log.info("\n--- 演示1：基本分布式锁功能 ---");
        
        String lockKey = "demo:basic:lock";
        
        // 第一次获取锁
        boolean result1 = distributedLockUtil.tryLockAndExecute(lockKey, 1, 5, () -> {
            log.info("第一次获取锁成功，执行业务逻辑");
            try {
                Thread.sleep(2000); // 模拟业务处理
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        log.info("第一次获取锁结果: {}", result1);
        
        // 第二次获取锁（应该失败，因为锁还在持有中）
        boolean result2 = distributedLockUtil.tryLockAndExecute(lockKey, 1, 5, () -> {
            log.info("第二次获取锁成功，执行业务逻辑");
        });
        
        log.info("第二次获取锁结果: {}", result2);
    }

    /**
     * 演示定时任务锁
     */
    private void demonstrateScheduledTaskLock() {
        log.info("\n--- 演示2：定时任务分布式锁 ---");
        
        String taskName = "demo-scheduled-task";
        String lockKey = DistributedLockUtil.generateScheduledTaskLockKey(taskName);
        
        log.info("定时任务锁Key: {}", lockKey);
        
        // 模拟多个实例同时执行定时任务
        for (int i = 1; i <= 3; i++) {
            final int instanceId = i;
            
            boolean executed = distributedLockUtil.executeScheduledTask(lockKey, () -> {
                log.info("实例 {} 正在执行定时任务", instanceId);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                log.info("实例 {} 定时任务执行完成", instanceId);
            });
            
            log.info("实例 {} 执行结果: {}", instanceId, executed ? "成功" : "跳过");
        }
    }

    /**
     * 演示并发场景
     */
    private void demonstrateConcurrentScenario() {
        log.info("\n--- 演示3：并发场景 ---");
        
        String lockKey = "demo:concurrent:test";
        
        // 启动多个线程模拟并发
        for (int i = 1; i <= 5; i++) {
            final int threadId = i;
            
            new Thread(() -> {
                boolean result = distributedLockUtil.tryLockAndExecute(lockKey, 1, 3, () -> {
                    log.info("线程 {} 获取锁成功，开始执行", threadId);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    log.info("线程 {} 执行完成", threadId);
                });
                
                log.info("线程 {} 结果: {}", threadId, result ? "成功" : "失败");
            }, "DemoThread-" + threadId).start();
        }
        
        // 等待所有线程完成
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 演示锁状态检查
     */
    private void demonstrateLockStatus() {
        log.info("\n--- 演示4：锁状态检查 ---");
        
        String lockKey = "demo:status:check";
        
        // 检查锁初始状态
        boolean initialStatus = distributedLockUtil.isLocked(lockKey);
        log.info("锁初始状态: {}", initialStatus ? "已锁定" : "未锁定");
        
        // 获取锁并检查状态
        distributedLockUtil.tryLockAndExecute(lockKey, 1, 10, () -> {
            boolean lockedStatus = distributedLockUtil.isLocked(lockKey);
            log.info("锁持有期间状态: {}", lockedStatus ? "已锁定" : "未锁定");
            
            long remainingTime = distributedLockUtil.getLockRemainingTime(lockKey);
            log.info("锁剩余时间: {}ms", remainingTime);
            
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // 等待一下再检查状态
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        boolean finalStatus = distributedLockUtil.isLocked(lockKey);
        log.info("锁释放后状态: {}", finalStatus ? "已锁定" : "未锁定");
    }

    /**
     * 演示JDK 21虚拟线程
     */
    private void demonstrateVirtualThreads() {
        log.info("\n--- 演示5：JDK 21虚拟线程 ---");
        
        String lockKey = "demo:virtual:threads";
        
        // 使用虚拟线程（JDK 21特性）
        for (int i = 1; i <= 3; i++) {
            final int threadId = i;
            
            Thread.ofVirtual().name("VirtualThread-" + threadId).start(() -> {
                boolean result = distributedLockUtil.tryLockAndExecute(lockKey, 1, 2, () -> {
                    log.info("虚拟线程 {} 获取锁成功，开始执行", threadId);
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    log.info("虚拟线程 {} 执行完成", threadId);
                });
                
                log.info("虚拟线程 {} 结果: {}", threadId, result ? "成功" : "失败");
            });
        }
        
        // 等待虚拟线程完成
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 演示锁Key生成规范
     */
    public void demonstrateLockKeyGeneration() {
        log.info("\n--- 锁Key生成规范演示 ---");
        
        // 定时任务锁
        String scheduledKey1 = DistributedLockUtil.generateScheduledTaskLockKey("image-cleanup");
        String scheduledKey2 = DistributedLockUtil.generateScheduledTaskLockKey("data-sync");
        
        // 业务锁
        String businessKey1 = DistributedLockUtil.generateBusinessLockKey("user", "123");
        String businessKey2 = DistributedLockUtil.generateBusinessLockKey("order", "456");
        String businessKey3 = DistributedLockUtil.generateBusinessLockKey("image", "process:789");
        
        log.info("定时任务锁Key:");
        log.info("  图片清理: {}", scheduledKey1);
        log.info("  数据同步: {}", scheduledKey2);
        
        log.info("业务锁Key:");
        log.info("  用户操作: {}", businessKey1);
        log.info("  订单处理: {}", businessKey2);
        log.info("  图片处理: {}", businessKey3);
    }
}
