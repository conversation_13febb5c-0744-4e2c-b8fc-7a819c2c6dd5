package com.sdses.ai.imagetransfer.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import jakarta.annotation.Resource;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * 异步处理配置类
 * 利用JDK 21的虚拟线程特性提供高并发异步处理能力
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig {

    @Value("${async.virtual-thread.enabled:true}")
    private boolean virtualThreadEnabled;

    @Value("${async.virtual-thread.name-prefix:kafka-async-}")
    private String threadNamePrefix;

    @Value("${async.platform-thread.core-pool-size:10}")
    private int corePoolSize;

    @Value("${async.platform-thread.max-pool-size:50}")
    private int maxPoolSize;

    @Resource
    private GracefulShutdownManager gracefulShutdownManager;

    /**
     * 创建用于Kafka消息异步处理的执行器
     * 优先使用JDK 21的虚拟线程，提供更好的并发性能
     */
    @Bean("kafkaAsyncExecutor")
    public Executor kafkaAsyncExecutor() {
        ExecutorService executor;
        if (virtualThreadEnabled) {
            log.info("启用虚拟线程执行器用于Kafka异步处理，线程名前缀: {}", threadNamePrefix);
            executor = createVirtualThreadExecutor();
        } else {
            log.info("启用平台线程执行器用于Kafka异步处理，核心线程数: {}, 最大线程数: {}",
                    corePoolSize, maxPoolSize);
            executor = createPlatformThreadExecutor();
        }

        // 注册到优雅停止管理器
        gracefulShutdownManager.registerExecutorService(executor);
        return executor;
    }

    /**
     * 创建虚拟线程执行器 (JDK 21特性)
     * 虚拟线程非常轻量，可以创建大量线程而不会消耗过多系统资源
     */
    private ExecutorService createVirtualThreadExecutor() {
        ThreadFactory factory = Thread.ofVirtual()
                .name(threadNamePrefix, 0)
                .factory();

        return Executors.newThreadPerTaskExecutor(factory);
    }

    /**
     * 创建平台线程执行器 (兼容模式)
     * 当虚拟线程不可用时的备选方案
     */
    private ExecutorService createPlatformThreadExecutor() {
        ThreadFactory factory = Thread.ofPlatform()
                .name(threadNamePrefix, 0)
                .factory();

        return Executors.newFixedThreadPool(maxPoolSize, factory);
    }

    /**
     * 创建用于图片下载的专用执行器
     * 图片下载是IO密集型操作，适合使用虚拟线程
     */
    @Bean("imageDownloadExecutor")
    public Executor imageDownloadExecutor() {
        ExecutorService executor;
        if (virtualThreadEnabled) {
            log.info("启用虚拟线程执行器用于图片下载，线程名前缀: image-download-");
            ThreadFactory factory = Thread.ofVirtual()
                    .name("image-download-", 0)
                    .factory();
            executor = Executors.newThreadPerTaskExecutor(factory);
        } else {
            ThreadFactory factory = Thread.ofPlatform()
                    .name("image-download-", 0)
                    .factory();
            executor = Executors.newFixedThreadPool(20, factory);
        }

        // 注册到优雅停止管理器
        gracefulShutdownManager.registerExecutorService(executor);
        return executor;
    }

    /**
     * 创建用于MinIO上传的专用执行器
     * MinIO上传也是IO密集型操作，适合使用虚拟线程
     */
    @Bean("minioUploadExecutor")
    public Executor minioUploadExecutor() {
        ExecutorService executor;
        if (virtualThreadEnabled) {
            log.info("启用虚拟线程执行器用于MinIO上传，线程名前缀: minio-upload-");
            ThreadFactory factory = Thread.ofVirtual()
                    .name("minio-upload-", 0)
                    .factory();
            executor = Executors.newThreadPerTaskExecutor(factory);
        } else {
            ThreadFactory factory = Thread.ofPlatform()
                    .name("minio-upload-", 0)
                    .factory();
            executor = Executors.newFixedThreadPool(15, factory);
        }

        // 注册到优雅停止管理器
        gracefulShutdownManager.registerExecutorService(executor);
        return executor;
    }
}
