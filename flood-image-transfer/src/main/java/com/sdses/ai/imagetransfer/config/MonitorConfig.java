package com.sdses.ai.imagetransfer.config;

import com.sdses.ai.imagetransfer.component.RedisTokenBucket;
import com.sdses.ai.imagetransfer.monitor.TokenBucketMonitor;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * 监控配置类
 * 用于解决循环依赖问题，设置监控器的依赖关系
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Slf4j
@Configuration
public class MonitorConfig {

    @Resource
    private RedisTokenBucket redisTokenBucket;
    
    @Resource
    private TokenBucketMonitor tokenBucketMonitor;

    @PostConstruct
    public void setupMonitorDependencies() {
        // 设置令牌桶的监控器引用
        redisTokenBucket.setTokenBucketMonitor(tokenBucketMonitor);
        log.info("令牌桶监控器依赖关系配置完成");
    }
}
