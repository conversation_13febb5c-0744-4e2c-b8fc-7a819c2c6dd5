package com.sdses.ai.imagetransfer.consumer;

import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.service.async.AsyncMessageProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 图片事件消费者
 * 支持同步和异步两种处理模式，利用JDK 21虚拟线程提供高并发处理能力
 *
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageEventConsumer {

    private final AsyncMessageProcessor asyncMessageProcessor;

    @Value("${kafka.consumer.async-processing.enabled:true}")
    private boolean asyncProcessingEnabled;

    @Value("${kafka.consumer.async-processing.timeout-seconds:60}")
    private long asyncTimeoutSeconds;

    /**
     * Kafka消息监听器
     * 根据配置选择同步或异步处理模式
     */
    @KafkaListener(topics = "${spring.kafka.topics.image-events}",
            containerFactory = "kafkaListenerContainerFactory")
    public void listen(List<EventMessage> messages, Acknowledgment ack) {
        log.info("接收到 {} 条消息，异步处理模式: {}", messages.size(), asyncProcessingEnabled);

        try {
            processMessagesAsync(messages, ack);
        } catch (Exception e) {
            log.error("处理消息批次失败，消息数量: {}", messages.size(), e);
            // 发生异常时仍然确认消息，避免重复消费
            ack.acknowledge();
        }
    }

    /**
     * 异步处理消息
     * 利用虚拟线程提供高并发处理能力
     */
    private void processMessagesAsync(List<EventMessage> messages, Acknowledgment ack) {
        try {
            CompletableFuture<AsyncMessageProcessor.ProcessingResult> future =
                asyncMessageProcessor.processMessagesAsync(messages);

            // 等待异步处理完成，设置超时时间
            AsyncMessageProcessor.ProcessingResult result = future.get(asyncTimeoutSeconds, TimeUnit.SECONDS);
            log.info("异步处理完成: {}", result);

            // 异步处理完成后确认消息
            ack.acknowledge();

        } catch (Exception e) {
            log.error("异步处理消息失败", e);
            // 即使异步处理失败，也要确认消息避免重复消费
            ack.acknowledge();
        }
    }
}
