package com.sdses.ai.imagetransfer.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdses.ai.imagetransfer.entity.CommonEventResourceLast;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-14 09:16
 */
@Mapper
@DS("doris1")
public interface CommonEventResourceLastMapper extends BaseMapper<CommonEventResourceLast> {

    List<EventMessage> listEventMessageByDB(@Param("startTime") String startTime,
                                            @Param("endTime") String endTime,
                                            @Param("eventType") String eventType);
}
