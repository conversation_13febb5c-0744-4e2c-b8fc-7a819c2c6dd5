package com.sdses.ai.imagetransfer.entity;


import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sdses.ai.imagetransfer.config.LocalDateTimeWithOffsetSerializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 三调kafka中的消息体结构
 * 支持JSON反序列化，忽略未知属性以提高兼容性
 */
@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EventMessage {

    @JsonProperty(value = "camera_index_code")
    private String cameraIndexCode;

    @JsonProperty(value = "camera_channel_code")
    private String cameraChannelCode;

    @JsonProperty(value = "camera_foreign_code")
    private String cameraForeignCode;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    @JsonProperty(value = "event_time")
    @JsonSerialize(using = LocalDateTimeWithOffsetSerializer.class)
    private LocalDateTime eventTime;

    @JsonProperty(value = "source_system")
    private String sourceSystem;

    @JsonProperty(value = "source_module")
    private String sourceModule;

    @JsonProperty(value = "info")
    private String info;

    @JsonProperty(value = "event_type")
    private String eventType;

    @JsonProperty(value = "event_id")
    private String eventId;

    @JsonProperty(value = "image_url")
    private String imageUrl;

    @JsonProperty(value = "video_url")
    private String videoUrl;

    @JsonProperty(value = "image_base64")
    private String imageBase64;

    @JsonProperty(value = "only_picture")
    private Integer onlyPicture;


}    