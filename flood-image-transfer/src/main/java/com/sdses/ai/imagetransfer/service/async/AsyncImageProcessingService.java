package com.sdses.ai.imagetransfer.service.async;

import cn.hutool.core.util.StrUtil;
import com.sdses.ai.imagetransfer.common.bo.LogicBO;
import com.sdses.ai.imagetransfer.common.enums.DisposeTypeEnum;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import com.sdses.ai.imagetransfer.entity.ResourceMessage;
import com.sdses.ai.imagetransfer.service.BaseService;
import com.sdses.ai.imagetransfer.service.FloodImageTransferHisService;
import com.sdses.ai.imagetransfer.service.logic.LogicService;
import com.sdses.ai.imagetransfer.utils.KafkaUtil;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 异步图片处理服务
 * 利用JDK 21虚拟线程提供高并发的异步消息处理能力
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncImageProcessingService {

    private final FloodImageTransferHisService hisService;
    private final LogicService logicService;
    private final AsyncProcessingMonitor asyncProcessingMonitor;

    @Resource
    private AsyncTaskTracker asyncTaskTracker;
    @Resource
    private BaseService baseService;
    @Resource
    private KafkaUtil kafkaUtil;

    /**
     * 异步处理单个消息
     * 使用虚拟线程执行器，支持高并发处理
     * 
     * @param eventMessage 事件消息
     * @return CompletableFuture<Boolean> 处理结果
     */
    @Async("kafkaAsyncExecutor")
    public CompletableFuture<Boolean> processMessageAsync(EventMessage eventMessage) {
        String taskId = "msg-" + eventMessage.getEventId() + "-" + UUID.randomUUID().toString().substring(0, 8);

        // 开始跟踪任务
        asyncTaskTracker.startTask(taskId, "MESSAGE_PROCESSING",
                String.format("处理消息: %s", eventMessage.getEventId()));

        try {
            log.debug("开始异步处理消息: {}, 任务ID: {}", eventMessage.getEventId(), taskId);
            LocalDateTime startTime = LocalDateTime.now();

            // 处理消息的核心逻辑
            LogicBO logicBO = processMessage(eventMessage, startTime, null, null, null);
            boolean result = logicBO.isContinue();

            // 完成任务跟踪
            asyncTaskTracker.completeTask(taskId, result);

            log.debug("异步处理消息完成: {}, 任务ID: {}, 结果: {}",
                    eventMessage.getEventId(), taskId, result);
            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            // 异常情况下完成任务跟踪
            asyncTaskTracker.completeTask(taskId, false);

            log.error("异步处理消息失败: {}, 任务ID: {}", eventMessage.getEventId(), taskId, e);
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * 异步处理消息批次
     * 将批次中的每个消息分发到不同的虚拟线程处理
     *
     * @param messages 消息列表
     * @return CompletableFuture<Integer> 成功处理的消息数量
     */
    @Async("kafkaAsyncExecutor")
    public CompletableFuture<Integer> processBatchAsync(List<EventMessage> messages) {
        log.info("开始异步处理消息批次，消息数量: {}", messages.size());

        // 创建所有消息的异步处理任务
        List<CompletableFuture<Boolean>> futures = messages.stream()
                .map(this::processMessageAsync)
                .toList();

        // 等待所有任务完成并统计成功数量
        CompletableFuture<Void> allOf = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );

        return allOf.thenApply(v -> {
            int successCount = (int) futures.stream()
                    .mapToInt(future -> {
                        try {
                            return future.get() ? 1 : 0;
                        } catch (Exception e) {
                            log.error("获取异步处理结果失败", e);
                            return 0;
                        }
                    })
                    .sum();

            log.info("批次处理完成，成功处理: {}/{}", successCount, messages.size());

            // 记录处理结果到监控系统
            recordProcessingMetrics(messages.size(), successCount);

            return successCount;
        });
    }

    /**
     * 异步处理消息批次（带任务ID和数据源）
     * 专门用于API异步上传场景，支持任务状态跟踪
     *
     * @param messages 消息列表
     * @param taskId 任务ID
     * @param dataSource 数据源标识
     * @return CompletableFuture<Integer> 成功处理的消息数量
     */
    @Async("kafkaAsyncExecutor")
    public CompletableFuture<Integer> processBatchAsync(List<EventMessage> messages, String taskId, String dataSource) {
        log.info("开始异步处理消息批次，taskId: {}, 数据源: {}, 消息数量: {}", taskId, dataSource, messages.size());

        // 开始跟踪批次任务
        asyncTaskTracker.startTask(taskId, "API_BATCH_UPLOAD",
                String.format("API异步上传批次，数据源: %s, 消息数量: %d", dataSource, messages.size()));

        try {
            // 创建所有消息的异步处理任务
            List<CompletableFuture<Boolean>> futures = messages.stream()
                    .map(message -> processMessageWithTaskInfo(message, taskId, dataSource))
                    .toList();

            // 等待所有任务完成并统计成功数量
            CompletableFuture<Void> allOf = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            return allOf.thenApply(v -> {
                int successCount = (int) futures.stream()
                        .mapToInt(future -> {
                            try {
                                return future.get() ? 1 : 0;
                            } catch (Exception e) {
                                log.error("获取异步处理结果失败", e);
                                return 0;
                            }
                        })
                        .sum();

                log.info("API异步批次处理完成，taskId: {}, 成功处理: {}/{}", taskId, successCount, messages.size());

                // 完成任务跟踪
                asyncTaskTracker.completeTask(taskId, successCount > 0);

                // 记录处理结果到监控系统
                recordProcessingMetrics(messages.size(), successCount);

                return successCount;

            }).exceptionally(throwable -> {
                log.error("API异步批次处理失败，taskId: {}", taskId, throwable);

                // 异常情况下完成任务跟踪
                asyncTaskTracker.completeTask(taskId, false);

                return 0;
            });

        } catch (Exception e) {
            log.error("提交API异步批次处理任务失败，taskId: {}", taskId, e);

            // 异常情况下完成任务跟踪
            asyncTaskTracker.completeTask(taskId, false);

            return CompletableFuture.completedFuture(0);
        }
    }

    /**
     * 处理单个消息（带任务信息）
     * 用于API异步上传场景
     *
     * @param eventMessage 事件消息
     * @param taskId 任务ID
     * @param dataSource 数据源标识
     * @return CompletableFuture<Boolean> 处理结果
     */
    @Async("kafkaAsyncExecutor")
    public CompletableFuture<Boolean> processMessageWithTaskInfo(EventMessage eventMessage, String taskId, String dataSource) {
        String messageTaskId = taskId + "-" + eventMessage.getEventId();

        // 开始跟踪单个消息任务
        asyncTaskTracker.startTask(messageTaskId, "API_MESSAGE_UPLOAD",
                String.format("API异步上传消息: %s", eventMessage.getEventId()));

        try {
            log.debug("开始异步处理消息: {}, taskId: {}, 数据源: {}", eventMessage.getEventId(), taskId, dataSource);
            LocalDateTime startTime = LocalDateTime.now();

            // 处理消息的核心逻辑
            LogicBO logicBO = processMessage(eventMessage, startTime, taskId, dataSource, null);
            boolean result = logicBO != null && logicBO.isContinue();

            // 完成任务跟踪
            asyncTaskTracker.completeTask(messageTaskId, result);

            log.debug("异步处理消息完成: {}, taskId: {}, 结果: {}",
                    eventMessage.getEventId(), taskId, result);
            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            // 异常情况下完成任务跟踪
            asyncTaskTracker.completeTask(messageTaskId, false);

            log.error("异步处理消息失败: {}, taskId: {}", eventMessage.getEventId(), taskId, e);
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * 处理单个消息的核心逻辑
     *
     * @param eventMessage 事件消息
     * @param startTime    开始时间
     * @param taskId
     * @param dataSource
     * @param isRetry
     * @return 处理是否成功
     */
    public LogicBO processMessage(EventMessage eventMessage,
                                  LocalDateTime startTime,
                                  String taskId,
                                  String dataSource,
                                  Boolean isRetry) {
        String eventId = eventMessage.getEventId();
        FloodImageTransferHis hisInfo = null;

        try {
            // 1.根据eventId查询库中是否存在记录
            hisInfo = getOrCreateHisInfo(eventMessage, taskId, dataSource);
            if (hisInfo == null) {
                log.error("创建历史记录失败: {}", eventId);
                return new LogicBO(null, false);
            }

            // 2.判断是否有合法的image_url
            LogicBO checkLogicBO = logicService.checkImageUrl(eventMessage, hisInfo, startTime);
            if (!checkLogicBO.isContinue()) {
                log.debug("图片URL检查失败，跳过处理: {}", eventId);
                return new LogicBO(null, false);
            }

            if (isRetry == null) {
                isRetry = true;
            }

            return logicService.checkExistAndUpload(eventMessage, startTime, hisInfo, isRetry);
        } catch (Exception e) {
            log.error("处理消息时发生异常: {}", eventId, e);

            // 异常时确保更新状态
            handleProcessingException(hisInfo, eventMessage, startTime, e);

            return new LogicBO(null, false);
        }
    }

    /**
     * 处理处理异常
     */
    private void handleProcessingException(FloodImageTransferHis hisInfo, EventMessage eventMessage,
                                           LocalDateTime startTime, Exception e) {
        if (hisInfo != null) {
            try {
                // 更新状态为错误
                updateStatusSafely(hisInfo, DisposeTypeEnum.ERROR.getCode(), "处理消息时发生异常: " + e.getMessage());

                // 发送失败消息到Kafka
                sendErrorMessageToKafka(eventMessage, startTime, hisInfo, "处理消息时发生异常: " + e.getMessage());

            } catch (Exception updateException) {
                log.error("更新异常状态失败: {}", eventMessage.getEventId(), updateException);
            }
        }
    }

    /**
     * 安全地更新状态
     */
    private void updateStatusSafely(FloodImageTransferHis hisInfo, String newStatus, String reason) {
        try {
            String oldStatus = hisInfo.getDisposeType();
            hisInfo.setDisposeType(newStatus);
            hisInfo.setUpdateTime(LocalDateTime.now());

            boolean success = hisService.saveOrUpdate(hisInfo);

            if (success) {
                log.info("状态更新成功: {} -> {}, 原因: {}, eventId: {}",
                        oldStatus, newStatus, reason, hisInfo.getId());
            } else {
                log.error("状态更新失败: eventId: {}, 原因: {}", hisInfo.getId(), reason);
            }

        } catch (Exception e) {
            log.error("更新状态异常: eventId: {}, 新状态: {}, 原因: {}",
                    hisInfo.getId(), newStatus, reason, e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    /**
     * 发送错误消息到Kafka
     */
    private void sendErrorMessageToKafka(EventMessage eventMessage, LocalDateTime startTime,
                                         FloodImageTransferHis hisInfo, String errorMessage) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            ResourceMessage resourceMessage = baseService.buildResourceInfo(
                    eventMessage, startTime, endTime, null,
                    hisInfo.getRetryCount(), DisposeTypeEnum.ERROR.getCode(),
                    errorMessage
            );

            kafkaUtil.sendToResourceTopic(resourceMessage);
            log.debug("发送错误消息到Kafka成功: {}", eventMessage.getEventId());

        } catch (Exception e) {
            log.error("发送错误消息到Kafka失败: {}", eventMessage.getEventId(), e);
        }
    }

    /**
     * 获取或创建历史记录信息
     *
     * @param eventMessage 事件消息
     * @param taskId
     * @param dataSource
     * @return 历史记录信息
     */
    private FloodImageTransferHis getOrCreateHisInfo(EventMessage eventMessage, String taskId, String dataSource) {
        String eventId = eventMessage.getEventId();

        // 先查询是否已存在（快速数据库查询）
        FloodImageTransferHis existInfo = hisService.getInfo(eventId);
        if (existInfo != null) {
            log.debug("找到已存在的历史记录: {}", eventId);

            if (StrUtil.isNotEmpty(taskId)) {
                existInfo.setTaskId(taskId);
            }
            if (StrUtil.isNotEmpty(dataSource)) {
                existInfo.setDataSource(dataSource);
            }

            return existInfo;
        }

        // 构建新的历史记录对象
        FloodImageTransferHis hisInfo = hisService.buildInfo(
                eventMessage.getEventId(),
                eventMessage,
                DisposeTypeEnum.START.getCode(),
                null,
                taskId,
                dataSource
        );

        // 使用虚拟线程异步保存（完全不阻塞主线程）
        Thread.startVirtualThread(() -> {
            try {
                long startTime = System.currentTimeMillis();
                boolean success = hisService.saveOrUpdate(hisInfo);
                long saveTime = System.currentTimeMillis() - startTime;

                if (success) {
                    log.debug("虚拟线程异步保存历史记录成功: {}, 耗时: {}ms", eventId, saveTime);
                } else {
                    log.error("虚拟线程异步保存历史记录失败: {}", eventId);
                }
            } catch (Exception e) {
                log.error("虚拟线程异步保存历史记录异常: {}", eventId, e);
            }
        });

        // 立即返回对象，不等待数据库保存完成
        log.debug("立即返回历史记录对象，数据库保存在后台进行: {}", eventId);
        return hisInfo;
    }

    /**
     * 记录处理指标到监控系统
     *
     * @param totalMessages 总消息数
     * @param successCount 成功处理数
     */
    private void recordProcessingMetrics(int totalMessages, int successCount) {
        try {
            // 创建简化的处理结果用于监控
            AsyncMessageProcessor.ProcessingResult result = AsyncMessageProcessor.ProcessingResult.builder()
                    .totalMessages(totalMessages)
                    .successCount(successCount)
                    .failedCount(totalMessages - successCount)
                    .processingTime(java.time.Duration.ofMillis(0)) // 这里可以记录实际处理时间
                    .startTime(java.time.LocalDateTime.now())
                    .endTime(java.time.LocalDateTime.now())
                    .parallelProcessing(true)
                    .build();

            asyncProcessingMonitor.recordProcessingResult(result);
        } catch (Exception e) {
            log.warn("记录处理指标失败", e);
        }
    }


}
