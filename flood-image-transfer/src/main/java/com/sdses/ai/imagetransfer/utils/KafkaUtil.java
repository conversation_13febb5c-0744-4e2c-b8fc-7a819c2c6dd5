package com.sdses.ai.imagetransfer.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sdses.ai.imagetransfer.config.KafkaTopicProperties;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.ResourceMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;

@Slf4j
@Configuration
public class KafkaUtil {

    private final KafkaTopicProperties topicProperties;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 构造函数注入主题配置
     */
    public KafkaUtil(KafkaTopicProperties topicProperties) {
        this.topicProperties = topicProperties;
    }

    /**
     * 向事件主题中发送消息
     * @param message
     */
    public void sendToEventTopic(EventMessage message) {
        try {
            String jsonMsg = objectMapper.writeValueAsString(message);
            kafkaTemplate.send(topicProperties.getImageEvents(), message.getEventId(), jsonMsg);
            log.info("消息已发送到事件主题 [{}]: {}", topicProperties.getImageEvents(), message.getEventId());
        } catch (JsonProcessingException e) {
            log.error("sendToEventTopic.objectMapper.writeValueAsString()发生异常: {}", e.getLocalizedMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 向重试主题中发送消息
     * @param message
     */
    public void sendToRetryTopic(EventMessage message) {
        try {
            String jsonMsg = objectMapper.writeValueAsString(message);
            kafkaTemplate.send(topicProperties.getRetryTopic(), message.getEventId(), jsonMsg);
            log.info("消息已发送到重试主题 [{}]: {}", topicProperties.getRetryTopic(), message.getEventId());
        } catch (JsonProcessingException e) {
            log.error("sendToRetryTopic.objectMapper.writeValueAsString()发生异常: {}", e.getLocalizedMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 向资源主题中发送消息
     * @param message
     */
    public void sendToResourceTopic(ResourceMessage message) {
        try {
            String jsonMsg = objectMapper.writeValueAsString(message);
            kafkaTemplate.send(topicProperties.getResourceTopic(), message.getEventId(), jsonMsg);
            log.info("消息已发送到Resource主题 [{}]: {}", topicProperties.getResourceTopic(), message.getEventId());
        } catch (JsonProcessingException e) {
            log.error("sendToResourceTopic.objectMapper.writeValueAsString()发生异常: {}", e.getLocalizedMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取主题配置信息
     * @return 主题配置描述
     */
    public String getTopicConfigInfo() {
        return topicProperties.getTopicsDescription();
    }
}
