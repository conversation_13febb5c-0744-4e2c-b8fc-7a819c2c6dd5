package com.sdses.ai.imagetransfer.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sdses.ai.imagetransfer.common.enums.DisposeTypeEnum;
import com.sdses.ai.imagetransfer.entity.CommonEventResourceLast;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import com.sdses.ai.imagetransfer.mapper.CommonEventResourceLastMapper;
import com.sdses.ai.imagetransfer.mapper.FloodImageTransferHisMapper;
import com.sdses.ai.imagetransfer.service.TestService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-14 09:14
 */
@Slf4j
@Service
public class TestServiceImpl implements TestService {

    @Resource
    private FloodImageTransferHisMapper hisMapper;

    @Resource
    private CommonEventResourceLastMapper commonEventResourceLastMapper;

    @Override
    public void selectPG() {
        FloodImageTransferHis floodImageTransferHis = hisMapper.selectById("7HtM5Nx3TvhBSPA5RzYwSdReHCaGcwmO");
        log.info("PG查询结果 ==> {}", JSONUtil.toJsonStr(floodImageTransferHis));
    }

    @Override
    public void selectDoris() {
        List<CommonEventResourceLast> commonEventResourceLasts = commonEventResourceLastMapper.selectList(new LambdaQueryWrapper<CommonEventResourceLast>()
                .eq(CommonEventResourceLast::getMinioImageUrl, "http://*************:9030/common-event-video-data/images/20250626/7a5823e87d13450aad6cce56aeeca29e/7a5823e87d13450aad6cce56aeeca29e_20250626153755.jpg"));
        log.info("doris查询结果 ==> {}", JSONUtil.toJsonStr(commonEventResourceLasts));
    }

    @Override
    public void testDisposeType() {
        List<String> disposeTypeList = List.of(DisposeTypeEnum.START.getCode());
        List<FloodImageTransferHis> hisList = hisMapper.list48HoursData(null, null, null, disposeTypeList);
        log.info("hisList ==> {}", JSONUtil.toJsonStr(hisList));
    }
}
