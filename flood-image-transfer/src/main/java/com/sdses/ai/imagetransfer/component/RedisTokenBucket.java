package com.sdses.ai.imagetransfer.component;

import cn.hutool.core.collection.CollUtil;
import com.sdses.ai.imagetransfer.common.bo.AddressBO;
import com.sdses.ai.imagetransfer.config.UrlAddressConfig;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Redis 令牌桶工具类（分布式限流）
 */
@Slf4j
@Component
public class RedisTokenBucket {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private UrlAddressConfig urlAddressConfig;

    // 延迟注入避免循环依赖
    private com.sdses.ai.imagetransfer.monitor.TokenBucketMonitor tokenBucketMonitor;

    // Lua 脚本：原子性地获取令牌，包含负数令牌检测和重置
    private static final String TOKEN_BUCKET_LUA_SCRIPT = """
                -- 令牌桶 key（包含令牌数和最后更新时间）
                local tokenCountKey = KEYS[1]
                local lastRefillTimeKey = KEYS[2]

                -- 参数：速率（令牌/秒）、容量、当前时间戳（毫秒）、需要的令牌数
                local rate = tonumber(ARGV[1])
                local capacity = tonumber(ARGV[2])
                local now = tonumber(ARGV[3])
                local requiredTokens = tonumber(ARGV[4])

                -- 参数验证
                if not rate or not capacity or not now or not requiredTokens then
                    return 0  -- 参数无效，拒绝请求
                end

                if rate <= 0 or capacity <= 0 or now <= 0 or requiredTokens <= 0 then
                    return 0  -- 参数值无效，拒绝请求
                end

                -- 获取当前令牌数和最后更新时间（默认 0）
                local currentTokens = tonumber(redis.call('get', tokenCountKey) or "0")
                local lastRefillTime = tonumber(redis.call('get', lastRefillTimeKey) or "0")
            
                -- 检测并修复负数令牌
                if currentTokens < 0 then
                    currentTokens = 0
                    redis.call('set', tokenCountKey, 0)
                    -- 返回特殊标识表示检测到负数令牌
                    return -1
                end

                -- 计算时间差（秒），并生成新令牌
                local timeElapsed = (now - lastRefillTime) / 1000
                local newTokens = math.floor(timeElapsed * rate)

                -- 更新令牌数（不超过容量）
                currentTokens = math.min(currentTokens + newTokens, capacity)
                -- 更新最后更新时间
                redis.call('set', lastRefillTimeKey, now)

                -- 判断是否能获取足够的令牌
                if currentTokens >= requiredTokens then
                    -- 扣除令牌
                    currentTokens = currentTokens - requiredTokens
                    redis.call('set', tokenCountKey, currentTokens)
                    return 1  -- 成功获取令牌
                else
                    -- 令牌不足，不更新令牌数
                    redis.call('set', tokenCountKey, currentTokens)
                    return 0  -- 失败
                end
            """;

    // 预编译的Lua脚本对象
    private RedisScript<Long> tokenBucketScript;

    @PostConstruct
    public void init() {
        this.tokenBucketScript = new DefaultRedisScript<>(TOKEN_BUCKET_LUA_SCRIPT, Long.class);

        List<AddressBO> addressList = urlAddressConfig.getAddressList();
        if (CollUtil.isNotEmpty(addressList)) {
            for (AddressBO bo : addressList) {
                // 令牌初始化时 需要区分三调的服务器地址 一个服务器地址对应一个令牌桶
                this.initBucket(bo.getTokenBucketKey(), bo.getMaxTokenCount(), 24);
            }
        }
        // image_transfer

        log.info("Redis 令牌桶 批量初始化成功");
    }

    /**
     * 设置监控器（延迟注入避免循环依赖）
     */
    public void setTokenBucketMonitor(com.sdses.ai.imagetransfer.monitor.TokenBucketMonitor monitor) {
        this.tokenBucketMonitor = monitor;
    }

    /**
     * 尝试获取令牌（核心方法）
     *
     * @param bucketKey 令牌桶唯一标识
     * @param rate      令牌生成速率（个/秒）
     * @param capacity  桶容量
     * @param requested 请求的令牌数
     * @return true 获取成功，false 获取失败
     */
    public boolean tryAcquire(String bucketKey, double rate, int capacity, int requested) {
        long startTime = System.currentTimeMillis();
        boolean success = false;

        String tokenKey = "rate_limiter:" + bucketKey + ":tokens";
        String timestampKey = "rate_limiter:" + bucketKey + ":timestamp";

        List<String> keys = List.of(tokenKey, timestampKey);

        // 直接传递数值类型，不转换为字符串
        Long currentTime = System.currentTimeMillis();
        Object[] args = new Object[]{
                rate,           // double类型
                capacity,       // int类型
                currentTime,    // long类型
                requested       // int类型
        };

        try {
            Long result = redisTemplate.execute(
                    tokenBucketScript,
                    keys,
                    args
            );

            // 检查是否检测到负数令牌
            if (result != null && result == -1) {
                log.warn("令牌桶 {} 检测到负数令牌，已强制重置为0", bucketKey);
                return false; // 检测到负数令牌时拒绝本次请求
            }

            log.debug("令牌桶 {} 获取结果: {}, 参数: rate={}, capacity={}, time={}, requested={}",
                     bucketKey, result, rate, capacity, currentTime, requested);

            success = result != null && result == 1;
            return success;
        } catch (Exception e) {
            log.error("令牌桶 {} 获取令牌失败: {}", bucketKey, e.getMessage(), e);
            return false;
        } finally {
            // 记录监控数据
            if (tokenBucketMonitor != null) {
                long waitTime = System.currentTimeMillis() - startTime;
                tokenBucketMonitor.recordTokenAcquisition(bucketKey, success, waitTime);
            }
        }
    }

    /**
     * 获取单个令牌（简化版）
     */
    public boolean tryAcquire(String bucketKey, double rate, int capacity) {
        return tryAcquire(bucketKey, rate, capacity, 1);
    }

    /**
     * 自旋等待获取令牌（适用于必须获取令牌的场景）
     *
     * @param bucketKey     令牌桶唯一标识
     * @param rate          令牌生成速率（个/秒）
     * @param capacity      桶容量
     * @param maxWaitMillis 最大等待时间（毫秒），-1 表示无限等待
     * @return true 获取成功，false 超时失败
     * @throws InterruptedException 如果线程被中断
     */
    public boolean tryAcquireWithSpinWait(String bucketKey, double rate, int capacity, long maxWaitMillis)
            throws InterruptedException {

        long startTime = System.currentTimeMillis();
        long remainingWait = maxWaitMillis;

        while (true) {
            // 尝试获取令牌
            if (tryAcquire(bucketKey, rate, capacity)) {
                return true;
            }

            // 检查是否超时（maxWaitMillis = -1 表示无限等待）
            if (maxWaitMillis >= 0) {
                remainingWait = maxWaitMillis - (System.currentTimeMillis() - startTime);
                if (remainingWait <= 0) {
                    return false; // 超时返回失败
                }
            }

            // 自旋等待：短暂休眠避免CPU空转（建议 50~200ms）
            Thread.sleep(Math.min(100, remainingWait > 0 ? remainingWait : 100));
        }
    }

    /**
     * 带过期时间的令牌桶初始化（防止长期不用的key堆积）
     * 初始化时检查是否存在负数令牌，如果存在则重置为0
     */
    public void initBucket(String bucketKey, int capacity, long ttlHours) {
        String tokenKey = "rate_limiter:" + bucketKey + ":tokens";
        String timestampKey = "rate_limiter:" + bucketKey + ":timestamp";

        // 检查是否已存在令牌桶，如果存在则验证令牌数是否为负数
        Object existingTokens = redisTemplate.opsForValue().get(tokenKey);
        if (existingTokens != null) {
            try {
                long tokenCount = Long.parseLong(existingTokens.toString());
                if (tokenCount < 0) {
                    log.warn("令牌桶初始化时发现负数令牌: {} = {}, 重置为0", bucketKey, tokenCount);
                    redisTemplate.opsForValue().set(tokenKey, 0);
                }
            } catch (NumberFormatException e) {
                log.warn("令牌桶初始化时发现无效令牌格式: {} = {}, 重置为容量值: {}",
                        bucketKey, existingTokens, capacity);
                redisTemplate.opsForValue().set(tokenKey, String.valueOf(capacity));
            }
        } else {
            // 如果不存在，则初始化为容量值
            redisTemplate.opsForValue().setIfAbsent(tokenKey, capacity);
        }

        redisTemplate.opsForValue().setIfAbsent(timestampKey, System.currentTimeMillis());

        // 设置过期时间
        redisTemplate.expire(tokenKey, ttlHours, TimeUnit.HOURS);
        redisTemplate.expire(timestampKey, ttlHours, TimeUnit.HOURS);
    }

    /**
     * 删除令牌桶（释放资源）
     */
    public void deleteBucket(String bucketKey) {
        String tokenKey = "rate_limiter:" + bucketKey + ":tokens";
        String timestampKey = "rate_limiter:" + bucketKey + ":timestamp";
        redisTemplate.delete(List.of(tokenKey, timestampKey));
    }

    /**
     * 获取当前令牌数（仅用于监控，不保证实时性）
     */
    public Long getCurrentTokens(String bucketKey) {
        String tokenKey = "rate_limiter:" + bucketKey + ":tokens";
        Object val = redisTemplate.opsForValue().get(tokenKey);
        return val != null ? Long.parseLong(val.toString()) : null;
    }
}
