package com.sdses.ai.imagetransfer.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 事件录屏结果表实体类
 */
@Data
@TableName("common_event_resource_last") // 指定表名
public class CommonEventResourceLast {
    @TableField("camera_foreign_code")
    @JsonProperty("camera_foreign_code")
    private String cameraForeignCode;

    @TableField("event_time")
    @JsonProperty("event_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    @TableField("start_time")
    @JsonProperty("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @TableField("end_time")
    @JsonProperty("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @TableField("event_id")
    @JsonProperty("event_id")
    private String eventId;

    @TableField("camera_index_code")
    @JsonProperty("camera_index_code")
    private String cameraIndexCode;

    @TableField("camera_channel_code")
    @JsonProperty("camera_channel_code")
    private String cameraChannelCode;

    @TableField("image_msg")
    @JsonProperty("image_msg")
    private String imageMsg;

    @TableField("video_msg")
    @JsonProperty("video_msg")
    private String videoMsg;

    @TableField("image_retry")
    @JsonProperty("image_retry")
    private Integer imageRetry;

    @TableField("video_retry")
    @JsonProperty("video_retry")
    private Integer videoRetry;

    @TableField("status")
    @JsonProperty("status")
    private String status;

    @TableField("duration")
    @JsonProperty("duration")
    private Integer duration;

    @TableField("info")
    @JsonProperty("info")
    private String info;

    @TableField("source_system")
    @JsonProperty("source_system")
    private String sourceSystem;

    @TableField("system_module")
    @JsonProperty("system_module")
    private String systemModule;

    @TableField("minio_video_url")
    @JsonProperty("minio_video_url")
    private String minioVideoUrl;

    @TableField("minio_image_url")
    @JsonProperty("minio_image_url")
    private String minioImageUrl;

    @TableField("accept_time")
    @JsonProperty("accept_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptTime;

    @TableField("`partition`")
    @JsonProperty("partition")
    private Long partition;

    @TableField("`offset`")
    @JsonProperty("offset")
    private Long offset;

    @TableField("sink_time")
    @JsonProperty("sink_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sinkTime;

}
