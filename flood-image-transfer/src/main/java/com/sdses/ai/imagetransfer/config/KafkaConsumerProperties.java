package com.sdses.ai.imagetransfer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Kafka消费者配置属性类
 * 用于统一管理Kafka消费者相关的配置参数
 * 所有配置值从YAML文件中读取，无硬编码默认值
 *
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@Data
@ConfigurationProperties(prefix = "spring.kafka.consumer")
public class KafkaConsumerProperties {

    /**
     * 消费者组ID
     */
    private String groupId;

    /**
     * 主消费者并发数
     * 控制主要业务消息的消费者线程数量
     */
    private Integer concurrency;

    /**
     * 重试消费者并发数
     * 控制重试消息的消费者线程数量
     */
    private Integer retryConcurrency;

    /**
     * 每次拉取的最大记录数
     * 控制每次从Kafka拉取的消息数量
     */
    private Integer maxPollRecords;

    /**
     * 会话超时时间(毫秒)
     * 消费者与Kafka集群的会话超时时间
     */
    private Integer sessionTimeoutMs;

    /**
     * 心跳间隔时间(毫秒)
     * 消费者向Kafka发送心跳的间隔时间
     */
    private Integer heartbeatIntervalMs;

    /**
     * 最大轮询间隔时间(毫秒)
     * 两次poll()调用之间的最大时间间隔
     */
    private Integer maxPollIntervalMs;

    /**
     * 最小拉取字节数
     * 每次拉取的最小数据量
     */
    private Integer fetchMinSize;

    /**
     * 最大等待时间(毫秒)
     * 等待足够数据的最大时间
     */
    private Integer fetchMaxWait;

    /**
     * 自动偏移量重置策略
     */
    private String autoOffsetReset;

    /**
     * 是否启用自动提交
     */
    private Boolean enableAutoCommit;

    /**
     * JSON反序列化信任包
     * 指定允许反序列化的包路径，提高安全性
     */
    private String trustedPackages;

    /**
     * 是否使用类型信息头
     * 控制JSON反序列化时是否使用消息头中的类型信息
     */
    private Boolean useTypeInfoHeaders;

    /**
     * 异步处理配置
     */
    private AsyncProcessing asyncProcessing;

    /**
     * 重试异步处理配置
     */
    private Retry retry;

    /**
     * 异步处理配置类
     */
    @Data
    public static class AsyncProcessing {
        /**
         * 是否启用异步处理
         */
        private Boolean enabled;

        /**
         * 异步处理超时时间(秒)
         */
        private Integer timeoutSeconds;
    }

    /**
     * 重试配置类
     */
    @Data
    public static class Retry {
        /**
         * 重试异步处理配置
         */
        private AsyncProcessing asyncProcessing;
    }

    /**
     * 获取环境特定的配置说明
     * @return 配置说明
     */
    public String getConfigDescription() {
        return String.format(
            "Kafka Consumer Config - Concurrency: %d, Retry: %d, MaxPoll: %d, Session: %dms, JSON: %s, AsyncEnabled: %s",
            concurrency, retryConcurrency, maxPollRecords, sessionTimeoutMs, trustedPackages,
            asyncProcessing != null ? asyncProcessing.getEnabled() : false
        );
    }

    /**
     * 验证配置的合理性
     * @return 是否配置合理
     */
    public boolean isConfigValid() {
        if (concurrency == null || retryConcurrency == null || maxPollRecords == null
            || sessionTimeoutMs == null || heartbeatIntervalMs == null) {
            return false;
        }

        return concurrency > 0
            && retryConcurrency > 0
            && maxPollRecords > 0
            && sessionTimeoutMs > heartbeatIntervalMs * 3
            && heartbeatIntervalMs > 0;
    }

    /**
     * 获取异步处理超时时间
     * @return 超时时间(秒)
     */
    public Integer getAsyncTimeoutSeconds() {
        return asyncProcessing != null ? asyncProcessing.getTimeoutSeconds() : 60;
    }

    /**
     * 是否启用异步处理
     * @return 是否启用
     */
    public Boolean isAsyncProcessingEnabled() {
        return asyncProcessing != null ? asyncProcessing.getEnabled() : false;
    }

    /**
     * 获取重试异步处理超时时间
     * @return 超时时间(秒)
     */
    public Integer getRetryAsyncTimeoutSeconds() {
        return retry != null && retry.getAsyncProcessing() != null
            ? retry.getAsyncProcessing().getTimeoutSeconds() : 120;
    }

    /**
     * 是否启用重试异步处理
     * @return 是否启用
     */
    public Boolean isRetryAsyncProcessingEnabled() {
        return retry != null && retry.getAsyncProcessing() != null
            ? retry.getAsyncProcessing().getEnabled() : false;
    }
}
