package com.sdses.ai.imagetransfer.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import jakarta.annotation.Resource;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 定时任务配置类
 * 支持优雅停止的定时任务调度器配置
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@Configuration
@EnableScheduling
public class SchedulingConfig {

    @Value("${scheduling.pool-size:5}")
    private int poolSize;

    @Value("${scheduling.thread-name-prefix:scheduled-task-}")
    private String threadNamePrefix;

    @Value("${scheduling.wait-for-tasks-to-complete-on-shutdown:true}")
    private boolean waitForTasksToCompleteOnShutdown;

    @Value("${scheduling.await-termination-seconds:10}")
    private int awaitTerminationSeconds;

    @Resource
    private GracefulShutdownManager gracefulShutdownManager;

    /**
     * 创建支持优雅停止的任务调度器
     */
    @Bean("taskScheduler")
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        
        // 基本配置
        scheduler.setPoolSize(poolSize);
        scheduler.setThreadNamePrefix(threadNamePrefix);
        
        // 优雅停止配置
        scheduler.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        scheduler.setAwaitTerminationSeconds(awaitTerminationSeconds);
        
        // 拒绝策略：调用者运行
        scheduler.setRejectedExecutionHandler((r, executor) -> {
            log.warn("定时任务被拒绝执行，将在调用线程中运行: {}", r.toString());
            if (!executor.isShutdown()) {
                r.run();
            }
        });
        
        // 初始化调度器
        scheduler.initialize();
        
        log.info("创建定时任务调度器: 线程池大小={}, 线程名前缀={}, 优雅停止={}", 
                poolSize, threadNamePrefix, waitForTasksToCompleteOnShutdown);
        
        // 注册到优雅停止管理器
        ScheduledExecutorService scheduledExecutorService = scheduler.getScheduledExecutor();
        if (scheduledExecutorService != null) {
            gracefulShutdownManager.registerScheduledExecutorService(scheduledExecutorService);
            log.debug("定时任务调度器已注册到优雅停止管理器");
        }
        
        return scheduler;
    }

    /**
     * 创建用于分布式锁定时任务的调度器
     * 专门用于需要分布式锁控制的定时任务
     */
    @Bean("distributedLockTaskScheduler")
    public TaskScheduler distributedLockTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        
        // 分布式锁任务通常不需要太多线程
        scheduler.setPoolSize(Math.max(2, poolSize / 2));
        scheduler.setThreadNamePrefix("distributed-lock-task-");
        
        // 优雅停止配置
        scheduler.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        scheduler.setAwaitTerminationSeconds(awaitTerminationSeconds);
        
        // 拒绝策略：丢弃任务（分布式锁任务可以跳过）
        scheduler.setRejectedExecutionHandler((r, executor) -> {
            log.warn("分布式锁定时任务被拒绝执行，任务将被丢弃: {}", r.toString());
        });
        
        scheduler.initialize();
        
        log.info("创建分布式锁定时任务调度器: 线程池大小={}", scheduler.getPoolSize());
        
        // 注册到优雅停止管理器
        ScheduledExecutorService scheduledExecutorService = scheduler.getScheduledExecutor();
        if (scheduledExecutorService != null) {
            gracefulShutdownManager.registerScheduledExecutorService(scheduledExecutorService);
            log.debug("分布式锁定时任务调度器已注册到优雅停止管理器");
        }
        
        return scheduler;
    }
}
