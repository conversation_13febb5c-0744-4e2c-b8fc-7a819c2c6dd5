package com.sdses.ai.imagetransfer.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;

import java.time.Instant;

@Data
@Getter
public class ResVoT<T>  {
    private Boolean flag;

    private String message;

    private String code;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Instant time;

    private T t;

    public ResVoT(Boolean flag, String message) {
        this.flag = flag;
        this.message = message;
        this.time = Instant.now();
    }

    public ResVoT(Boolean flag, String message, String code) {
        this.flag = flag;
        this.message = message;
        this.code = code;
        this.time = Instant.now();
    }

    public ResVoT() {
        this.time = Instant.now();
    }

    public ResVoT(Boolean flag, String message, String code, T t) {
        this.flag = flag;
        this.message = message;
        this.code = code;
        this.time = Instant.now();
        this.t = t;
    }

    public static <T>ResVoT<T> error(String message) {
        return new ResVoT<>(false, message,"500");
    }

    public static <T> ResVoT<T> error(T t) {
        return new ResVoT<>(false, "error", "500", t);
    }

    public static <T>ResVoT<T> error(String message,String code) {
        return new ResVoT<>(false, message,code);
    }
    public static <T>ResVoT<T> error(String message,String code,T t) {
        return new ResVoT<>(false, message,code,t);
    }

    public static <T>ResVoT<T> error(String message,T t) {
        return new ResVoT<>(false, message,"500",t);
    }

    public static <T>ResVoT<T> success(String message) {
        return new ResVoT<>(true, message,"200");
    }

    public static <T> ResVoT<T> success(T t) {
        return new ResVoT<>(true, "success", "200", t);
    }

    public static <T>ResVoT<T> success(String message, T t) {
        return new ResVoT<T>(true, message,"200",t);
    }
}