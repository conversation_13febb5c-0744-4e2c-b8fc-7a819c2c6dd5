package com.sdses.ai.imagetransfer.service.async;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;
import java.util.Set;

/**
 * 异步任务跟踪器
 * 用于跟踪正在执行的异步任务，支持优雅停止时的任务监控
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@Component
public class AsyncTaskTracker {

    // 活跃任务计数器
    private final AtomicInteger activeTaskCount = new AtomicInteger(0);
    
    // 总任务计数器
    private final AtomicLong totalTaskCount = new AtomicLong(0);
    
    // 完成任务计数器
    private final AtomicLong completedTaskCount = new AtomicLong(0);
    
    // 活跃任务详情 - 任务ID -> 任务信息
    private final ConcurrentHashMap<String, TaskInfo> activeTasks = new ConcurrentHashMap<>();

    /**
     * 开始跟踪任务
     * 
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @param description 任务描述
     */
    public void startTask(String taskId, String taskType, String description) {
        TaskInfo taskInfo = new TaskInfo(taskId, taskType, description, LocalDateTime.now());
        activeTasks.put(taskId, taskInfo);
        
        int activeCount = activeTaskCount.incrementAndGet();
        long totalCount = totalTaskCount.incrementAndGet();
        
        log.debug("开始跟踪任务: {} [{}] - 活跃任务数: {}, 总任务数: {}", 
                taskId, taskType, activeCount, totalCount);
    }

    /**
     * 完成任务跟踪
     * 
     * @param taskId 任务ID
     * @param success 是否成功
     */
    public void completeTask(String taskId, boolean success) {
        TaskInfo taskInfo = activeTasks.remove(taskId);
        if (taskInfo != null) {
            taskInfo.setEndTime(LocalDateTime.now());
            taskInfo.setSuccess(success);
            
            int activeCount = activeTaskCount.decrementAndGet();
            long completedCount = completedTaskCount.incrementAndGet();
            
            log.debug("完成任务跟踪: {} [{}] - 成功: {}, 活跃任务数: {}, 完成任务数: {}", 
                    taskId, taskInfo.getTaskType(), success, activeCount, completedCount);
        } else {
            log.warn("尝试完成未跟踪的任务: {}", taskId);
        }
    }

    /**
     * 获取活跃任务数量
     */
    public int getActiveTaskCount() {
        return activeTaskCount.get();
    }

    /**
     * 获取总任务数量
     */
    public long getTotalTaskCount() {
        return totalTaskCount.get();
    }

    /**
     * 获取完成任务数量
     */
    public long getCompletedTaskCount() {
        return completedTaskCount.get();
    }

    /**
     * 获取活跃任务ID集合
     */
    public Set<String> getActiveTaskIds() {
        return activeTasks.keySet();
    }

    /**
     * 获取活跃任务详情
     */
    public Map<String, TaskInfo> getActiveTasks() {
        return new ConcurrentHashMap<>(activeTasks);
    }

    /**
     * 检查是否有活跃任务
     */
    public boolean hasActiveTasks() {
        return activeTaskCount.get() > 0;
    }

    /**
     * 等待所有任务完成
     * 
     * @param timeoutMillis 超时时间（毫秒）
     * @return 是否所有任务都已完成
     */
    public boolean waitForAllTasksCompletion(long timeoutMillis) {
        long startTime = System.currentTimeMillis();
        
        while (hasActiveTasks() && (System.currentTimeMillis() - startTime) < timeoutMillis) {
            try {
                Thread.sleep(100); // 每100ms检查一次
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待任务完成时被中断");
                return false;
            }
        }
        
        boolean allCompleted = !hasActiveTasks();
        if (allCompleted) {
            log.info("所有任务已完成");
        } else {
            log.warn("等待任务完成超时，仍有 {} 个活跃任务", getActiveTaskCount());
        }
        
        return allCompleted;
    }

    /**
     * 获取任务统计信息
     */
    public TaskStatistics getStatistics() {
        return new TaskStatistics(
                getActiveTaskCount(),
                getTotalTaskCount(),
                getCompletedTaskCount(),
                LocalDateTime.now()
        );
    }

    /**
     * 重置统计信息
     */
    public void reset() {
        activeTasks.clear();
        activeTaskCount.set(0);
        totalTaskCount.set(0);
        completedTaskCount.set(0);
        log.info("任务跟踪器统计信息已重置");
    }

    /**
     * 任务信息
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class TaskInfo {
        private String taskId;
        private String taskType;
        private String description;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean success;

        public TaskInfo(String taskId, String taskType, String description, LocalDateTime startTime) {
            this.taskId = taskId;
            this.taskType = taskType;
            this.description = description;
            this.startTime = startTime;
        }
    }

    /**
     * 任务统计信息
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class TaskStatistics {
        private int activeTaskCount;
        private long totalTaskCount;
        private long completedTaskCount;
        private LocalDateTime timestamp;
    }
}
