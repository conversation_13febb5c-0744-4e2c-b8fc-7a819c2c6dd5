package com.sdses.ai.imagetransfer.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;


/**
 * 动态数据源配置验证类
 * 用于验证动态数据源配置是否正确加载
 * 适配Spring Boot 3.5.0 + MyBatis Plus 3.5.x + dynamic-datasource-spring-boot-starter 4.3.0
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "spring.datasource.dynamic.primary", havingValue = "pg1")
public class DynamicDataSourceConfig {

    /**
     * 初始化时验证动态数据源配置
     */
    @PostConstruct
    public void init() {
        log.info("=== 动态数据源配置验证 ===");
        log.info("Spring Boot 版本: 3.5.0");
        log.info("MyBatis Plus 版本: 3.5.x");
        log.info("Dynamic DataSource 版本: 4.3.0");
        log.info("主数据源: pg1 (PostgreSQL - 写操作)");
        log.info("从数据源: doris1 (Doris - 读/分析操作)");
        log.info("@DS注解支持: 已启用");
        log.info("Druid连接池: 已启用");
        log.info("DataSourceAutoConfiguration: 已排除");
        log.info("=== 动态数据源配置验证完成 ===");
    }
}
