package com.sdses.ai.imagetransfer.service;

import com.sdses.ai.imagetransfer.common.bo.AddressBO;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.ResourceMessage;

import java.time.LocalDateTime;
import java.util.List;

public interface BaseService {

    // 获取yaml文件中的url数据
    List<AddressBO> getAddressList();

    // 判断指定url是否存在
    boolean isExist(String url);

    AddressBO getAddressInfoByUrl(String url);

    ResourceMessage buildResourceInfo(EventMessage eventMessage,
                                      LocalDateTime startTime,
                                      LocalDateTime endTime,
                                      String minioImageUrl,
                                      int retryCount,
                                      String status,
                                      String imageMsg);

    void checkRetryCount(int maxRetryCount,
                         int retryCount,
                         EventMessage eventMessage,
                         LocalDateTime startTime, String msg);
}
