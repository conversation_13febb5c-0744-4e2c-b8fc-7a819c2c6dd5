package com.sdses.ai.imagetransfer.service.impl;

import cn.hutool.core.util.StrUtil;
import com.sdses.ai.imagetransfer.common.bo.AddressBO;
import com.sdses.ai.imagetransfer.common.constants.BaseConstant;
import com.sdses.ai.imagetransfer.common.enums.DisposeTypeEnum;
import com.sdses.ai.imagetransfer.config.UrlAddressConfig;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.ResourceMessage;
import com.sdses.ai.imagetransfer.service.BaseService;
import com.sdses.ai.imagetransfer.utils.KafkaUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class BaseServiceImpl implements BaseService {

    @Resource
    private UrlAddressConfig urlAddressConfig;
    @Resource
    private KafkaUtil kafkaUtil;

    // 获取yaml文件中的url数据
    @Override
    public List<AddressBO> getAddressList() {
        return urlAddressConfig.getAddressList();
    }

    // 判断指定url是否存在
    @Override
    public boolean isExist(String url) {
        List<AddressBO> addressList = this.getAddressList();

        for (AddressBO bo : addressList) {
            String address = bo.getAddress();

            if (url.startsWith(address)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public AddressBO getAddressInfoByUrl(String url) {
        List<AddressBO> addressList = this.getAddressList();

        for (AddressBO bo : addressList) {
            String address = bo.getAddress();

            if (url.startsWith(address)) {
                return bo;
            }
        }

        return null;
    }

    /**
     * 构建响应体
     *
     * @param eventMessage
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param minioImageUrl minio图片地址
     * @param retryCount    重试次数
     * @param status
     * @param imageMsg
     * @return
     */
    @Override
    public ResourceMessage buildResourceInfo(EventMessage eventMessage,
                                             LocalDateTime startTime,
                                             LocalDateTime endTime,
                                             String minioImageUrl,
                                             int retryCount,
                                             String status,
                                             String imageMsg) {
        long seconds = 0;
        if (startTime != null && endTime != null) {
            // 计算两个时间相差秒数
            Duration duration = Duration.between(startTime, endTime);
            seconds = duration.getSeconds();
        }

        return ResourceMessage.builder()
                .cameraIndexCode(eventMessage.getCameraIndexCode())
                .cameraChannelCode(eventMessage.getCameraChannelCode())
                .cameraForeignCode(eventMessage.getCameraForeignCode())
                .startTime(startTime)
                .endTime(endTime)
                .eventTime(eventMessage.getEventTime())
                .eventId(eventMessage.getEventId())
                .minioVideoUrl(null)
                .minioImageUrl(minioImageUrl)
                .status(status)
                .imageRetry(retryCount)
                .videoRetry(null)
                .imageMsg(imageMsg)
                .videoMsg(null)
                .sourceSystem(BaseConstant.SOURCE_SYSTEM)
                .sourceModule(BaseConstant.SOURCE_MODULE)
                .info(eventMessage.getInfo())
                .build();
    }

    /**
     * 判断时间是否已经达到重试次数上线
     *
     * @param maxRetryCount 最大重试次数
     * @param retryCount    重试次数 应该是只有1和2两种
     * @param eventMessage
     * @param startTime
     * @param msg
     */
    @Override
    public void checkRetryCount(int maxRetryCount,
                                int retryCount,
                                EventMessage eventMessage,
                                LocalDateTime startTime,
                                String msg) {
        log.info("判断时间是否已经达到重试次数上线 maxRetryCount: {}, retryCount: {}", maxRetryCount, retryCount);

        String imageMsg;
        if (retryCount < maxRetryCount) {
            log.info("延迟3秒发送消息到重试机制的topic");
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            kafkaUtil.sendToRetryTopic(eventMessage);

            imageMsg = "延迟3秒发送消息到重试机制的topic";
        } else {
            imageMsg = "重试次数已达到上限";
        }

        if (StrUtil.isNotEmpty(msg)) {
            imageMsg = msg + ";" + imageMsg;
        }

        LocalDateTime endTime = LocalDateTime.now();
        // 推送时间失败处理结果
        ResourceMessage resourceMessage = this.buildResourceInfo(eventMessage, startTime, endTime, null,
                retryCount, DisposeTypeEnum.ERROR.getCode(), imageMsg);
        kafkaUtil.sendToResourceTopic(resourceMessage);
    }

}
