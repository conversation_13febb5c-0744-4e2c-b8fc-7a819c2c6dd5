package com.sdses.ai.imagetransfer.monitor;

import cn.hutool.core.collection.CollUtil;
import com.sdses.ai.imagetransfer.common.bo.AddressBO;
import com.sdses.ai.imagetransfer.component.RedisTokenBucket;
import com.sdses.ai.imagetransfer.config.UrlAddressConfig;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jmx.export.annotation.ManagedAttribute;
import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 分布式令牌桶状态监控器
 * 提供令牌桶的实时监控、统计分析和告警功能
 *
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Slf4j
@Service
@ManagedResource(objectName = "com.sdses.ai.imagetransfer:type=TokenBucketMonitor", description = "令牌桶监控器")
public class TokenBucketMonitor {

    @Resource
    private RedisTokenBucket redisTokenBucket;

    @Resource
    private UrlAddressConfig urlAddressConfig;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${image.download.monitor-interval:5000}")
    private long monitorInterval;

    @Value("${token-bucket.monitor.alert.failure-rate-threshold:0.8}")
    private double failureRateThreshold;

    @Value("${token-bucket.monitor.alert.negative-reset-threshold:5}")
    private int negativeResetThreshold;

    @Value("${token-bucket.monitor.history.retention-hours:24}")
    private int historyRetentionHours;

    // 监控数据存储
    private final Map<String, TokenBucketMetrics> bucketMetrics = new ConcurrentHashMap<>();
    private final Map<String, List<MonitorRecord>> monitorHistory = new ConcurrentHashMap<>();
    private final AtomicLong totalAcquisitions = new AtomicLong(0);
    private final AtomicLong totalFailures = new AtomicLong(0);
    private final AtomicLong totalNegativeResets = new AtomicLong(0);

    @PostConstruct
    public void init() {
        log.info("初始化令牌桶监控器，监控间隔: {}ms", monitorInterval);
        initializeMetrics();
    }

    /**
     * 初始化监控指标
     */
    private void initializeMetrics() {
        List<AddressBO> addressList = urlAddressConfig.getAddressList();
        if (CollUtil.isNotEmpty(addressList)) {
            for (AddressBO addressBO : addressList) {
                String bucketKey = addressBO.getTokenBucketKey();
                TokenBucketMetrics metrics = new TokenBucketMetrics();
                metrics.setBucketKey(bucketKey);
                metrics.setAddress(addressBO.getAddress());
                metrics.setCapacity(addressBO.getMaxTokenCount());
                metrics.setRate(addressBO.getRate());
                metrics.setStatus(BucketStatus.NORMAL);
                bucketMetrics.put(bucketKey, metrics);
                monitorHistory.put(bucketKey, new ArrayList<>());
                log.info("初始化令牌桶监控指标: {}", bucketKey);
            }
        }
    }

    /**
     * 定时监控令牌桶状态
     */
    @Scheduled(fixedRateString = "${image.download.monitor-interval:5000}")
    public void monitorTokenBucketStatus() {
        try {
            collectMetrics();
            analyzeMetrics();
            checkAlerts();
            cleanupHistory();
        } catch (Exception e) {
            log.error("令牌桶状态监控失败", e);
        }
    }

    /**
     * 收集监控指标
     */
    @Async
    public void collectMetrics() {
        long startTime = System.currentTimeMillis();

        for (TokenBucketMetrics metrics : bucketMetrics.values()) {
            try {
                collectBucketMetrics(metrics);
            } catch (Exception e) {
                log.error("收集令牌桶 {} 指标失败", metrics.getBucketKey(), e);
                metrics.setStatus(BucketStatus.ERROR);
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        log.debug("监控指标收集完成，耗时: {}ms", duration);
    }

    /**
     * 收集单个令牌桶的监控指标
     */
    private void collectBucketMetrics(TokenBucketMetrics metrics) {
        String bucketKey = metrics.getBucketKey();

        // 获取当前令牌数
        Long currentTokens = redisTokenBucket.getCurrentTokens(bucketKey);
        if (currentTokens != null) {
            metrics.setCurrentTokens(currentTokens.intValue());

            // 检测负数令牌
            if (currentTokens < 0) {
                metrics.incrementNegativeResets();
                totalNegativeResets.incrementAndGet();
                log.warn("检测到令牌桶 {} 负数令牌: {}", bucketKey, currentTokens);
                metrics.setStatus(BucketStatus.NEGATIVE_RESET);
            } else {
                // 计算令牌使用率
                double utilizationRate = 1.0 - (double) currentTokens / metrics.getCapacity();
                metrics.setUtilizationRate(utilizationRate);

                // 根据使用率判断状态
                if (utilizationRate > 0.9) {
                    metrics.setStatus(BucketStatus.HIGH_LOAD);
                } else if (utilizationRate > 0.7) {
                    metrics.setStatus(BucketStatus.MEDIUM_LOAD);
                } else {
                    metrics.setStatus(BucketStatus.NORMAL);
                }
            }
        } else {
            metrics.setStatus(BucketStatus.NOT_FOUND);
            log.warn("令牌桶 {} 不存在或无法访问", bucketKey);
        }

        // 获取最后更新时间
        String timestampKey = "rate_limiter:" + bucketKey + ":timestamp";
        Object lastUpdateObj = redisTemplate.opsForValue().get(timestampKey);
        if (lastUpdateObj != null) {
            try {
                long lastUpdate = Long.parseLong(lastUpdateObj.toString());
                metrics.setLastUpdateTime(lastUpdate);

                // 检查是否长时间未更新（可能表示没有请求）
                long timeSinceUpdate = System.currentTimeMillis() - lastUpdate;
                if (timeSinceUpdate > 300000) { // 5分钟
                    metrics.setStatus(BucketStatus.IDLE);
                }
            } catch (NumberFormatException e) {
                log.warn("令牌桶 {} 时间戳格式错误: {}", bucketKey, lastUpdateObj);
            }
        }

        // 记录监控历史
        MonitorRecord record = new MonitorRecord();
        record.setTimestamp(System.currentTimeMillis());
        record.setTokens(metrics.getCurrentTokens());
        record.setUtilizationRate(metrics.getUtilizationRate());
        record.setStatus(metrics.getStatus());

        List<MonitorRecord> history = monitorHistory.get(bucketKey);
        if (history != null) {
            history.add(record);
        }

        metrics.setLastMonitorTime(System.currentTimeMillis());
    }

    /**
     * 分析监控指标
     */
    private void analyzeMetrics() {
        for (TokenBucketMetrics metrics : bucketMetrics.values()) {
            analyzeFailureRate(metrics);
            analyzeWaitTime(metrics);
            analyzeHotspots(metrics);
        }
    }

    /**
     * 分析失败率
     */
    private void analyzeFailureRate(TokenBucketMetrics metrics) {
        long totalRequests = metrics.getSuccessCount() + metrics.getFailureCount();
        if (totalRequests > 0) {
            double failureRate = (double) metrics.getFailureCount() / totalRequests;
            metrics.setFailureRate(failureRate);

            if (failureRate > failureRateThreshold) {
                metrics.setStatus(BucketStatus.HIGH_FAILURE);
                log.warn("令牌桶 {} 失败率过高: {}", metrics.getBucketKey(), failureRate);
            }
        }
    }

    /**
     * 分析平均等待时间
     */
    private void analyzeWaitTime(TokenBucketMetrics metrics) {
        if (metrics.getTotalWaitTime() > 0 && metrics.getWaitCount() > 0) {
            double avgWaitTime = (double) metrics.getTotalWaitTime() / metrics.getWaitCount();
            metrics.setAverageWaitTime(avgWaitTime);

            if (avgWaitTime > 5000) { // 超过5秒
                log.warn("令牌桶 {} 平均等待时间过长: {}ms", metrics.getBucketKey(), avgWaitTime);
            }
        }
    }

    /**
     * 分析热点令牌桶
     */
    private void analyzeHotspots(TokenBucketMetrics metrics) {
        // 计算请求频率
        long currentTime = System.currentTimeMillis();
        long timeWindow = 60000; // 1分钟窗口

        List<MonitorRecord> history = monitorHistory.get(metrics.getBucketKey());
        if (history != null) {
            long recentRequests = history.stream()
                .filter(record -> currentTime - record.getTimestamp() <= timeWindow)
                .count();

            metrics.setRequestFrequency(recentRequests);

            if (recentRequests > metrics.getRate() * 60 * 0.8) { // 超过配置速率的80%
                metrics.setHotspot(true);
                log.info("检测到热点令牌桶: {}, 1分钟内请求数: {}", metrics.getBucketKey(), recentRequests);
            } else {
                metrics.setHotspot(false);
            }
        }
    }

    /**
     * 检查告警条件
     */
    private void checkAlerts() {
        for (TokenBucketMetrics metrics : bucketMetrics.values()) {
            checkNegativeTokenAlert(metrics);
            checkHighFailureRateAlert(metrics);
            checkLongWaitTimeAlert(metrics);
        }
    }

    /**
     * 检查负数令牌告警
     */
    private void checkNegativeTokenAlert(TokenBucketMetrics metrics) {
        if (metrics.getNegativeResetCount() >= negativeResetThreshold) {
            log.error("【告警】令牌桶 {} 负数重置次数过多: {}, 地址: {}",
                metrics.getBucketKey(), metrics.getNegativeResetCount(), metrics.getAddress());
            // 这里可以集成告警系统，如发送邮件、短信等
        }
    }

    /**
     * 检查高失败率告警
     */
    private void checkHighFailureRateAlert(TokenBucketMetrics metrics) {
        if (metrics.getFailureRate() > failureRateThreshold) {
            log.error("【告警】令牌桶 {} 失败率过高: {:.2%}, 地址: {}",
                metrics.getBucketKey(), metrics.getFailureRate(), metrics.getAddress());
        }
    }

    /**
     * 检查长等待时间告警
     */
    private void checkLongWaitTimeAlert(TokenBucketMetrics metrics) {
        if (metrics.getAverageWaitTime() > 10000) { // 超过10秒
            log.error("【告警】令牌桶 {} 平均等待时间过长: {}ms, 地址: {}",
                metrics.getBucketKey(), metrics.getAverageWaitTime(), metrics.getAddress());
        }
    }

    /**
     * 清理历史数据
     */
    private void cleanupHistory() {
        long cutoffTime = System.currentTimeMillis() - (historyRetentionHours * 3600000L);

        for (List<MonitorRecord> history : monitorHistory.values()) {
            history.removeIf(record -> record.getTimestamp() < cutoffTime);
        }
    }

    /**
     * 记录令牌获取操作（供外部调用）
     */
    public void recordTokenAcquisition(String bucketKey, boolean success, long waitTime) {
        TokenBucketMetrics metrics = bucketMetrics.get(bucketKey);
        if (metrics != null) {
            if (success) {
                metrics.incrementSuccess();
            } else {
                metrics.incrementFailure();
                totalFailures.incrementAndGet();
            }

            if (waitTime > 0) {
                metrics.addWaitTime(waitTime);
            }

            totalAcquisitions.incrementAndGet();
        }
    }

    /**
     * 生成监控报告
     */
    @ManagedOperation(description = "生成令牌桶监控报告")
    public String generateMonitorReport() {
        StringBuilder report = new StringBuilder();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        report.append("=== 令牌桶监控报告 ===\n");
        report.append("生成时间: ").append(LocalDateTime.now().format(formatter)).append("\n");
        report.append("总获取次数: ").append(totalAcquisitions.get()).append("\n");
        report.append("总失败次数: ").append(totalFailures.get()).append("\n");
        report.append("总负数重置次数: ").append(totalNegativeResets.get()).append("\n\n");

        for (TokenBucketMetrics metrics : bucketMetrics.values()) {
            report.append("令牌桶: ").append(metrics.getBucketKey()).append("\n");
            report.append("  地址: ").append(metrics.getAddress()).append("\n");
            report.append("  状态: ").append(metrics.getStatus()).append("\n");
            report.append("  当前令牌数: ").append(metrics.getCurrentTokens()).append("/").append(metrics.getCapacity()).append("\n");
            report.append("  使用率: ").append(String.format("%.2f%%", metrics.getUtilizationRate() * 100)).append("\n");
            report.append("  成功次数: ").append(metrics.getSuccessCount()).append("\n");
            report.append("  失败次数: ").append(metrics.getFailureCount()).append("\n");
            report.append("  失败率: ").append(String.format("%.2f%%", metrics.getFailureRate() * 100)).append("\n");
            report.append("  平均等待时间: ").append(String.format("%.2fms", metrics.getAverageWaitTime())).append("\n");
            report.append("  负数重置次数: ").append(metrics.getNegativeResetCount()).append("\n");
            report.append("  是否热点: ").append(metrics.isHotspot() ? "是" : "否").append("\n");
            report.append("  请求频率: ").append(metrics.getRequestFrequency()).append("/分钟\n\n");
        }

        return report.toString();
    }

    // ========== JMX 管理接口 ==========

    @ManagedAttribute(description = "总令牌获取次数")
    public long getTotalAcquisitions() {
        return totalAcquisitions.get();
    }

    @ManagedAttribute(description = "总失败次数")
    public long getTotalFailures() {
        return totalFailures.get();
    }

    @ManagedAttribute(description = "总负数重置次数")
    public long getTotalNegativeResets() {
        return totalNegativeResets.get();
    }

    @ManagedAttribute(description = "监控的令牌桶数量")
    public int getMonitoredBucketCount() {
        return bucketMetrics.size();
    }

    @ManagedAttribute(description = "整体失败率")
    public double getOverallFailureRate() {
        long total = totalAcquisitions.get();
        return total > 0 ? (double) totalFailures.get() / total : 0.0;
    }

    @ManagedOperation(description = "获取指定令牌桶的详细信息")
    public String getBucketDetails(String bucketKey) {
        TokenBucketMetrics metrics = bucketMetrics.get(bucketKey);
        if (metrics == null) {
            return "令牌桶不存在: " + bucketKey;
        }

        return String.format(
            "令牌桶: %s\n地址: %s\n状态: %s\n当前令牌: %d/%d\n使用率: %.2f%%\n成功: %d, 失败: %d\n失败率: %.2f%%",
            metrics.getBucketKey(), metrics.getAddress(), metrics.getStatus(),
            metrics.getCurrentTokens(), metrics.getCapacity(), metrics.getUtilizationRate() * 100,
            metrics.getSuccessCount(), metrics.getFailureCount(), metrics.getFailureRate() * 100
        );
    }

    @ManagedOperation(description = "重置所有统计数据")
    public void resetStatistics() {
        totalAcquisitions.set(0);
        totalFailures.set(0);
        totalNegativeResets.set(0);

        for (TokenBucketMetrics metrics : bucketMetrics.values()) {
            metrics.reset();
        }

        for (List<MonitorRecord> history : monitorHistory.values()) {
            history.clear();
        }

        log.info("令牌桶监控统计数据已重置");
    }

    @ManagedOperation(description = "获取热点令牌桶列表")
    public String getHotspotBuckets() {
        StringBuilder result = new StringBuilder("热点令牌桶:\n");

        bucketMetrics.values().stream()
            .filter(TokenBucketMetrics::isHotspot)
            .forEach(metrics -> result.append(String.format("- %s (频率: %d/分钟)\n",
                metrics.getBucketKey(), metrics.getRequestFrequency())));

        return result.toString();
    }

    // ========== 数据模型类 ==========

    /**
     * 令牌桶监控指标
     */
    @Data
    public static class TokenBucketMetrics {
        private String bucketKey;
        private String address;
        private int capacity;
        private double rate;
        private int currentTokens;
        private double utilizationRate;
        private BucketStatus status;
        private long lastUpdateTime;
        private long lastMonitorTime;

        // 统计数据
        private final AtomicLong successCount = new AtomicLong(0);
        private final AtomicLong failureCount = new AtomicLong(0);
        private final AtomicLong negativeResetCount = new AtomicLong(0);
        private final AtomicLong totalWaitTime = new AtomicLong(0);
        private final AtomicLong waitCount = new AtomicLong(0);

        // 分析结果
        private double failureRate;
        private double averageWaitTime;
        private boolean hotspot;
        private long requestFrequency;

        public void incrementSuccess() {
            successCount.incrementAndGet();
        }

        public void incrementFailure() {
            failureCount.incrementAndGet();
        }

        public void incrementNegativeResets() {
            negativeResetCount.incrementAndGet();
        }

        public void addWaitTime(long waitTime) {
            totalWaitTime.addAndGet(waitTime);
            waitCount.incrementAndGet();
        }

        public long getSuccessCount() {
            return successCount.get();
        }

        public long getFailureCount() {
            return failureCount.get();
        }

        public long getNegativeResetCount() {
            return negativeResetCount.get();
        }

        public long getTotalWaitTime() {
            return totalWaitTime.get();
        }

        public long getWaitCount() {
            return waitCount.get();
        }

        public void reset() {
            successCount.set(0);
            failureCount.set(0);
            negativeResetCount.set(0);
            totalWaitTime.set(0);
            waitCount.set(0);
            failureRate = 0.0;
            averageWaitTime = 0.0;
            hotspot = false;
            requestFrequency = 0;
        }
    }

    /**
     * 令牌桶状态枚举
     */
    public enum BucketStatus {
        NORMAL("正常"),
        HIGH_LOAD("高负载"),
        MEDIUM_LOAD("中等负载"),
        HIGH_FAILURE("高失败率"),
        NEGATIVE_RESET("负数重置"),
        IDLE("空闲"),
        NOT_FOUND("不存在"),
        ERROR("错误");

        private final String description;

        BucketStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        @Override
        public String toString() {
            return description;
        }
    }

    /**
     * 监控记录
     */
    @Data
    public static class MonitorRecord {
        private long timestamp;
        private int tokens;
        private double utilizationRate;
        private BucketStatus status;
    }
}
