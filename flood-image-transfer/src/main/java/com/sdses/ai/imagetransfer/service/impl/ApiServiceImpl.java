package com.sdses.ai.imagetransfer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdses.ai.imagetransfer.common.bo.LogicBO;
import com.sdses.ai.imagetransfer.common.constants.BaseConstant;
import com.sdses.ai.imagetransfer.common.dto.QueryResultListDTO;
import com.sdses.ai.imagetransfer.common.vo.UploadVO;
import com.sdses.ai.imagetransfer.entity.CommonEventResourceLast;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import com.sdses.ai.imagetransfer.exception.BusinessException;
import com.sdses.ai.imagetransfer.mapper.CommonEventResourceLastMapper;
import com.sdses.ai.imagetransfer.mapper.FloodImageTransferHisMapper;
import com.sdses.ai.imagetransfer.service.ApiService;
import com.sdses.ai.imagetransfer.service.JsonService;
import com.sdses.ai.imagetransfer.service.async.AsyncImageProcessingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-14 13:55
 */
@Slf4j
@Service
public class ApiServiceImpl implements ApiService {

    @Value("${common.sync-upload-max-size:10}")
    private int syncUploadMaxSize;
    @Value("${common.async-upload-max-size:1000}")
    private int asyncUploadMaxSize;
    @Value("${minio.endpoint}")
    private String minioEndpoint;
    @Value("${common.minio-public-url:}")
    private String minioPublicUrl;

    @Resource
    private CommonEventResourceLastMapper commonEventResourceLastMapper;
    @Resource
    private AsyncImageProcessingService asyncImageProcessingService;
    @Resource
    private FloodImageTransferHisMapper hisMapper;
    @Resource
    private JsonService jsonService;

    @Override
    public CommonEventResourceLast queryResult(String eventId) {
        if (StrUtil.isEmpty(eventId)) {
            log.error("eventId为空");
            return null;
        }
        List<CommonEventResourceLast> list = commonEventResourceLastMapper.selectList(new LambdaQueryWrapper<CommonEventResourceLast>()
                .eq(CommonEventResourceLast::getEventId, eventId));
        if (CollUtil.isEmpty(list)) {
            log.info("根据eventId: {}未能查询到记录", eventId);
            return null;
        }

        // 替换minio地址 支持外部访问
        CommonEventResourceLast first = list.getFirst();
        String minioImageUrl = first.getMinioImageUrl();
        if (StrUtil.isNotEmpty(minioImageUrl)) {
            minioImageUrl = minioImageUrl.replace(minioEndpoint, minioPublicUrl);
            first.setMinioImageUrl(minioImageUrl);
        }

        return first;
    }

    @Override
    public List<UploadVO> syncUpload(List<EventMessage> messageList) {
        if (CollUtil.isEmpty(messageList)) {
            log.error("messageList为空");
            throw new BusinessException("messageList不能为空");
        }

        if (messageList.size() > syncUploadMaxSize) {
            log.error("messageList数量超过限制: {}", syncUploadMaxSize);
            throw new BusinessException("messageList数量超过限制: " + syncUploadMaxSize);
        }

        //
        String taskId = IdUtil.fastSimpleUUID();
        String dataSource = "api";
        List<UploadVO> voList = new ArrayList<>();
        for (EventMessage eventMessage : messageList) {
            LocalDateTime startTime = LocalDateTime.now();

            LogicBO logicBO = asyncImageProcessingService.processMessage(eventMessage, startTime,
                    taskId, dataSource, false);

            if (ObjectUtil.isNotNull(logicBO) && ObjectUtil.isNotNull(logicBO.getHisInfo())) {
                FloodImageTransferHis hisInfo = logicBO.getHisInfo();
                // bean转换
                UploadVO vo = this.buildUploadVO(hisInfo);

                voList.add(vo);
            }
        }

        return voList;
    }

    private UploadVO buildUploadVO(FloodImageTransferHis hisInfo) {
        if (ObjectUtil.isNull(hisInfo)) {
            return new UploadVO();
        }

        UploadVO vo = BeanUtil.toBean(hisInfo, UploadVO.class);

        vo.setEventId(hisInfo.getId());
        // 处理minio地址
        if (StrUtil.isNotEmpty(vo.getMinioImageUrl())) {
            vo.setMinioImageUrl(vo.getMinioImageUrl().replace(minioEndpoint, minioPublicUrl));
        }

        String kafkaMsg = hisInfo.getKafkaMsg();
        if (StrUtil.isNotEmpty(kafkaMsg)) {
            EventMessage eventMessage = jsonService.json2EventMessageBean(kafkaMsg);

            vo.setCameraIndexCode(eventMessage.getCameraIndexCode());
            vo.setCameraChannelCode(eventMessage.getCameraChannelCode());
            vo.setCameraForeignCode(eventMessage.getCameraForeignCode());
            vo.setOriginImageUrl(eventMessage.getImageUrl());
        }

        return vo;
    }

    /**
     * 异步上传处理
     * 立即返回taskId，后台异步处理消息列表
     *
     * @param messageList 消息列表
     * @return taskId 任务唯一标识符，用于后续查询任务状态
     */
    @Override
    public String asyncUpload(List<EventMessage> messageList) {
        log.info("接收异步上传请求，消息数量: {}", messageList != null ? messageList.size() : 0);

        // 1. 参数验证
        if (CollUtil.isEmpty(messageList)) {
            log.error("messageList为空");
            throw new BusinessException("messageList不能为空");
        }

        if (messageList.size() > asyncUploadMaxSize) {
            log.error("messageList数量超过限制: {}, 最大允许: {}", messageList.size(), asyncUploadMaxSize);
            throw new BusinessException("messageList数量超过限制: " + asyncUploadMaxSize);
        }

        // 2. 生成唯一任务ID
        String taskId = IdUtil.fastSimpleUUID();
        String dataSource = "async-api";

        log.info("创建异步上传任务，taskId: {}, 消息数量: {}", taskId, messageList.size());

        try {
            // 3. 提交异步处理任务（立即返回，不等待处理完成）
            asyncImageProcessingService.processBatchAsync(messageList, taskId, dataSource)
                    .whenComplete((successCount, throwable) -> {
                        if (throwable != null) {
                            log.error("异步上传任务执行失败，taskId: {}", taskId, throwable);
                        } else {
                            log.info("异步上传任务执行完成，taskId: {}, 成功处理: {}/{}",
                                    taskId, successCount, messageList.size());
                        }
                    });

            // 4. 立即返回taskId
            log.info("异步上传任务已提交，taskId: {}，可通过此ID查询任务状态", taskId);
            return taskId;

        } catch (Exception e) {
            log.error("提交异步上传任务失败，taskId: {}", taskId, e);
            throw new BusinessException("提交异步任务失败: " + e.getMessage());
        }
    }

    @Override
    public List<UploadVO> asyncStatus(String taskId) {
        if (StrUtil.isEmpty(taskId)) {
            log.error("缺少任务id");
            throw new BusinessException("缺少任务id");
        }

        // 根据任务id查询集合
        List<FloodImageTransferHis> hisList = hisMapper.selectList(new LambdaQueryWrapper<FloodImageTransferHis>()
                .eq(FloodImageTransferHis::getTaskId, taskId));

        List<UploadVO> voList = new ArrayList<>();

        if (CollUtil.isEmpty(hisList)) {
            log.warn("根据任务id: {}未查询到记录", taskId);
            return List.of();
        }

        for (FloodImageTransferHis hisInfo : hisList) {
            UploadVO vo = this.buildUploadVO(hisInfo);
            voList.add(vo);
        }

        return voList;
    }

    @Override
    public IPage<CommonEventResourceLast> queryResultList(QueryResultListDTO dto) {
        log.info("调用分页查询结果集合接口，入参: {}", dto);

        // 参数校验
        if (dto == null) {
            log.error("查询参数不能为空");
            throw new BusinessException("查询参数不能为空");
        }

        // 设置默认分页参数
        Integer pageNum = dto.getPage();
        Integer pageSize = dto.getPageSize();
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
            log.info("页码为空或无效，设置默认值: {}", pageNum);
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
            log.info("页大小为空或无效，设置默认值: {}", pageSize);
        }

        // 限制最大页大小
        if (pageSize > 1000) {
            pageSize = 1000;
            log.warn("页大小超过最大限制，调整为: {}", pageSize);
        }

        // 获取查询条件
        String cameraForeignCode = dto.getCameraForeignCode();
        String cameraIndexCode = dto.getCameraIndexCode();
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();

        log.info("查询条件 - 外码: {}, 内码: {}, 开始时间: {}, 结束时间: {}, 页码: {}, 页大小: {}",
                cameraForeignCode, cameraIndexCode, startTime, endTime, pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<CommonEventResourceLast> queryWrapper = new LambdaQueryWrapper<>();

        // 固定仅查询指定system_module
        queryWrapper.eq(CommonEventResourceLast::getSourceSystem, BaseConstant.SOURCE_SYSTEM);
        // TODO 等待表结构更新后 再放开这个限制条件
        // queryWrapper.eq(CommonEventResourceLast::getSystemModule, BaseConstant.SOURCE_MODULE);

        // 外码条件
        if (StrUtil.isNotBlank(cameraForeignCode)) {
            queryWrapper.eq(CommonEventResourceLast::getCameraForeignCode, cameraForeignCode.trim());
            log.debug("添加外码查询条件: {}", cameraForeignCode.trim());
        }

        // 内码条件
        if (StrUtil.isNotBlank(cameraIndexCode)) {
            queryWrapper.eq(CommonEventResourceLast::getCameraIndexCode, cameraIndexCode.trim());
            log.debug("添加内码查询条件: {}", cameraIndexCode.trim());
        }

        // 时间范围条件
        LocalDateTime startDateTime = dto.getParsedStartTime();
        LocalDateTime endDateTime = dto.getParsedEndTime();

        if (startDateTime != null) {
            queryWrapper.ge(CommonEventResourceLast::getEventTime, startDateTime);
            log.debug("添加开始时间条件: event_time >= {}", startDateTime);
        }

        if (endDateTime != null) {
            queryWrapper.le(CommonEventResourceLast::getEventTime, endDateTime);
            log.debug("添加结束时间条件: event_time <= {}", endDateTime);
        }

        // 按照事件时间排列 不传默认倒序
        if (StrUtil.isNotBlank(dto.getOrderBy())) {
            log.debug("根据入参排序: {}", dto.getOrderBy());
            if (BaseConstant.ORDER_BY_ASC.equalsIgnoreCase(dto.getOrderBy())) {
                queryWrapper.orderByAsc(CommonEventResourceLast::getEventTime);
            } else if (BaseConstant.ORDER_BY_DESC.equalsIgnoreCase(dto.getOrderBy())) {
                queryWrapper.orderByDesc(CommonEventResourceLast::getEventTime);
            }
        } else {
            log.debug("默认倒序");
            queryWrapper.orderByDesc(CommonEventResourceLast::getEventTime);
        }

        // 创建分页对象
        Page<CommonEventResourceLast> page = new Page<>(pageNum, pageSize);

        try {
            // 执行分页查询（使用Doris数据源）
            IPage<CommonEventResourceLast> result = commonEventResourceLastMapper.selectPage(page, queryWrapper);

            log.info("查询完成 - 总记录数: {}, 总页数: {}, 当前页: {}, 当前页记录数: {}",
                    result.getTotal(), result.getPages(), result.getCurrent(), result.getRecords().size());

            List<CommonEventResourceLast> records = result.getRecords();
            for (CommonEventResourceLast record : records) {
                // 替换minio地址支持外部访问
                String minioImageUrl = record.getMinioImageUrl();
                minioImageUrl = minioImageUrl.replace(minioEndpoint, minioPublicUrl);
                record.setMinioImageUrl(minioImageUrl);
            }

            return result;

        } catch (Exception e) {
            log.error("分页查询结果集合失败", e);
            throw new BusinessException("查询失败: " + e.getMessage());
        }
    }
}
