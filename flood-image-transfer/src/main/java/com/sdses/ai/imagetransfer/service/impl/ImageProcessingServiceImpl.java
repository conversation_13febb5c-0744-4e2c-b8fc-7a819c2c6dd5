package com.sdses.ai.imagetransfer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sdses.ai.imagetransfer.common.bo.LogicBO;
import com.sdses.ai.imagetransfer.common.dto.DeleteDataDTO;
import com.sdses.ai.imagetransfer.common.dto.ProcessingDTO;
import com.sdses.ai.imagetransfer.common.enums.DisposeTypeEnum;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import com.sdses.ai.imagetransfer.mapper.FloodImageTransferHisMapper;
import com.sdses.ai.imagetransfer.service.FloodImageTransferHisService;
import com.sdses.ai.imagetransfer.service.ImageProcessingService;
import com.sdses.ai.imagetransfer.service.JsonService;
import com.sdses.ai.imagetransfer.service.logic.LogicService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ImageProcessingServiceImpl  implements ImageProcessingService {

    private final LogicService logicService;
    private final JsonService jsonService;
    private final FloodImageTransferHisService hisService;
    private final FloodImageTransferHisMapper floodImageTransferHisMapper;

    @Override
    public Integer processFailedEvents(ProcessingDTO dto) {
        log.info("人工处理失败事件 入参: {}", JSONUtil.toJsonStr(dto));
        String time = dto.getTime();
        Integer minRetryCount = dto.getMinRetryCount();
        Integer maxRetryCount = dto.getMaxRetryCount();

        // 重试次数大于5次 且事件时间不超过48小时的事件
        List<FloodImageTransferHis> hisList = hisService.list48HoursData(minRetryCount, maxRetryCount,
                time, List.of(DisposeTypeEnum.ERROR.getCode()));

        if (CollUtil.isEmpty(hisList)) {
            log.error("没有需要处理的事件");
            return 0;
        }

        int count = 0;
        for (FloodImageTransferHis hisInfo : hisList) {
            LocalDateTime startTime = LocalDateTime.now();
            this.processImage(hisInfo, startTime);
            count++;
        }


        return count;
    }

    // 删除数据
    @Override
    public Integer deleteData(DeleteDataDTO dto) {
        log.info("调用删除数据接口 入参: {}", JSONUtil.toJsonStr(dto));
        Integer hours = dto.getHours();
        String time = dto.getTime();
        if (hours == null || hours <= 0) {
            log.error("非法入参 hours: {}", hours);
            return 0;
        }

        // 时间如果为空 就取系统当前时间为指定时间点
        if (StrUtil.isEmpty(time)) {
            time = DateUtil.now();
            log.info("入参时间为空 取当前系统时间: {}", time);
        }

        //
        return floodImageTransferHisMapper.deleteDataByHours(hours, time);
    }


    @Override
    public String reprocessEvent(List<String> eventIds) {
        if (CollUtil.isEmpty(eventIds)) {
            log.error("eventIds为空");
            return "eventIds为空 直接返回";
        }

        int count = 0;
        // 控制每次查询的量不要太大，超过100条时分批处理
        if (eventIds.size() > 100) {
            log.info("eventIds数量为{}，超过100条，开始分批处理", eventIds.size());

            // 分批处理，每批最大100条
            for (int i = 0; i < eventIds.size(); i += 100) {
                int endIndex = Math.min(i + 100, eventIds.size());
                List<String> batchEventIds = eventIds.subList(i, endIndex);

                log.info("处理第{}批，范围：{}-{}，数量：{}",
                    (i / 100) + 1, i + 1, endIndex, batchEventIds.size());

                int batchCount = this.batchReprocessEvent(batchEventIds);
                count += batchCount;

                log.info("第{}批处理完成，处理数量：{}", (i / 100) + 1, batchCount);
            }

            log.info("分批处理完成，总处理数量：{}", count);
        } else {
            count += this.batchReprocessEvent(eventIds);
        }


        return String.format("重新处理完成，总输入eventIds数量：%d，实际处理数量：%d", eventIds.size(), count);
    }

    // 分批处理
    private Integer batchReprocessEvent(List<String> eventIds) {
        // 查询
        List<FloodImageTransferHis> hisList = hisService.listByIds(eventIds);
        if (CollUtil.isEmpty(hisList)) {
            log.error("根据eventIds未查询到记录");
            return 0;
        }

        int count = 0;
        // 重新处理
        for (FloodImageTransferHis hisInfo : hisList) {
            LocalDateTime startTime = LocalDateTime.now();

            Thread.ofVirtual()
                    .name("async-reprocess-event-thread-", 0)
                    .start(() -> this.processImage(hisInfo, startTime));
            count++;
        }

        return count;
    }

    // 图片转存的业务逻辑
    private void processImage(FloodImageTransferHis hisInfo, LocalDateTime startTime) {
        String kafkaMsg = hisInfo.getKafkaMsg();

        // 核心转存逻辑
        if (StrUtil.isEmpty(kafkaMsg)) {
            log.error("kafkaMsg为空，跳过处理: {}", hisInfo.getId());
            return;
        }

        // JSON反序列化为实体类
        EventMessage eventMessage = jsonService.json2EventMessageBean(kafkaMsg);

        // 判断是否有合法的imag_url
        LogicBO checkLogicBO = logicService.checkImageUrl(eventMessage, hisInfo, startTime);
        boolean checkFlag = checkLogicBO.isContinue();
        if (!checkFlag) {
            log.error("图片URL检查失败，跳过处理: {}", hisInfo.getId());
            return;
        }

        // 判断服务器地址、获取令牌和上传minio
        logicService.checkExistAndUpload(eventMessage, startTime, hisInfo, false);
    }
}
