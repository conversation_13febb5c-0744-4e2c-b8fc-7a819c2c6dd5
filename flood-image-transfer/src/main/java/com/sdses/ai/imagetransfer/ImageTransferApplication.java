package com.sdses.ai.imagetransfer;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 图像传输应用主启动类
 */
@MapperScan("com.sdses.**.mapper")
@EnableKafka
@EnableAsync
@EnableScheduling
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class ImageTransferApplication {
    public static void main(String[] args) {
        SpringApplication.run(ImageTransferApplication.class, args);
        System.out.println("=============== START SUCCESS ===============");
    }
}
