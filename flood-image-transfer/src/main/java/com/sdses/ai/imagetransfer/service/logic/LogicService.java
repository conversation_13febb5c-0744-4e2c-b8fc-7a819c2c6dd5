package com.sdses.ai.imagetransfer.service.logic;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sdses.ai.imagetransfer.common.bo.AddressBO;
import com.sdses.ai.imagetransfer.common.bo.LogicBO;
import com.sdses.ai.imagetransfer.common.enums.DisposeTypeEnum;
import com.sdses.ai.imagetransfer.component.RedisTokenBucket;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import com.sdses.ai.imagetransfer.entity.ResourceMessage;
import com.sdses.ai.imagetransfer.service.BaseService;
import com.sdses.ai.imagetransfer.service.FloodImageTransferHisService;
import com.sdses.ai.imagetransfer.service.ImageDownloadService;
import com.sdses.ai.imagetransfer.utils.KafkaUtil;
import com.sdses.ai.imagetransfer.utils.MinioStreamUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-07 18:25
 */
@Slf4j
@Service
public class LogicService {

    @Resource
    private BaseService baseService;
    @Resource
    private FloodImageTransferHisService hisService;
    @Resource
    private KafkaUtil kafkaUtil;
    @Resource
    private ImageDownloadService imageDownloadService;
    @Resource
    private MinioStreamUtil minioStreamUtil;
    @Resource
    private RedisTokenBucket redisTokenBucket;

    /**
     * 判断是否有合法的imag_url
     * @param eventMessage
     * @param hisInfo
     * @param startTime
     * @return
     */
    public LogicBO checkImageUrl(EventMessage eventMessage,
                                 FloodImageTransferHis hisInfo,
                                 LocalDateTime startTime) {
        // 判断是否有合法的imag_url
        String imageUrl = eventMessage.getImageUrl();
        if (checkImageUrlIsNull(imageUrl)) {
            log.error("图片地址为空，跳过处理: {}", eventMessage.getEventId());

            // 更新消息记录
            hisInfo.setDisposeType(DisposeTypeEnum.FAILED.getCode());
            hisService.saveOrUpdate(hisInfo);

            // 向kafka中推送事件失败处理结果
            LocalDateTime endTime = LocalDateTime.now();
            ResourceMessage resourceMessage = baseService.buildResourceInfo(eventMessage, startTime, endTime, null,
                    hisInfo.getRetryCount(), DisposeTypeEnum.FAILED.getCode(),
                    "图片地址为空");
            kafkaUtil.sendToResourceTopic(resourceMessage);

            return new LogicBO(null, false);
        } else {
            return new LogicBO(null, true);
        }
    }

    /**
     * 判断imag_url是否为空
     * @param imageUrl
     * @return
     */
    private boolean checkImageUrlIsNull(String imageUrl) {
        return StrUtil.isEmpty(imageUrl);
    }

    /**
     * 判断服务器地址
     *
     * @param eventMessage
     * @param startTime
     * @param hisInfo
     * @return
     */
    public LogicBO checkServerAddress(EventMessage eventMessage,
                                      LocalDateTime startTime,
                                      FloodImageTransferHis hisInfo) {
        long t1 = System.currentTimeMillis();
        try {
            String imageUrl = eventMessage.getImageUrl();

            // 判断url是否在三调缓存中
            boolean exist = baseService.isExist(imageUrl);
            if (!exist) {
                // 更新消息记录为 error 重试+1
                hisService.errorAndAddRetryCount(hisInfo);

                // 向kafka中推送事件失败处理结果
                LocalDateTime endTime = LocalDateTime.now();
                ResourceMessage resourceMessage = baseService.buildResourceInfo(eventMessage, startTime, endTime, null,
                        hisInfo.getRetryCount(), DisposeTypeEnum.ERROR.getCode(),
                        "image_url不存在于三调图片服务器列表中");
                kafkaUtil.sendToResourceTopic(resourceMessage);

                return new LogicBO(null, false);
            } else {
                return new LogicBO(null, true);
            }
        } catch (RuntimeException e) {
            log.error("判断url是否在三调缓存中发生异常", e);
            return new LogicBO(null, false);
        } finally {
            log.info("判断url是否在三调缓存中 耗时 {}ms", System.currentTimeMillis() - t1);
        }
    }

    /**
     * 获取令牌
     *
     * @param eventMessage
     * @param startTime
     * @param hisInfo
     * @return
     */
    public LogicBO acquireToken(EventMessage eventMessage,
                                LocalDateTime startTime,
                                FloodImageTransferHis hisInfo) {
        // 细分性能分析：令牌获取
        long tokenStart = System.currentTimeMillis();
        String imageUrl = eventMessage.getImageUrl();

        // 根据url获取三调配置
        AddressBO addressInfo = baseService.getAddressInfoByUrl(imageUrl);
        if (ObjectUtil.isNull(addressInfo)) {
            log.error("根据url获取配置内三调图片服务器失败 url: {}", imageUrl);
            return new LogicBO(null, false);
        }

        // 通过三调服务器配置 获取令牌
        boolean tokenFlag = true;
        try {
            tokenFlag = redisTokenBucket.tryAcquireWithSpinWait(addressInfo.getTokenBucketKey(),
                    addressInfo.getRate(), addressInfo.getMaxTokenCount(), 20000);
        } catch (InterruptedException e) {
            log.error("获取令牌时发生异常: {}", imageUrl);
            return new LogicBO(null, false);
        }
        long tokenTime = System.currentTimeMillis() - tokenStart;
        log.info("获取自旋令牌 耗时: {}ms", tokenTime);

        if (!tokenFlag) {
            log.error("获取令牌失败: {}", imageUrl);

            // 令牌获取失败时更新状态 重试次数+1
            hisService.errorAndAddRetryCount(hisInfo);

            // 发送失败消息
            LocalDateTime endTime = LocalDateTime.now();
            ResourceMessage resourceMessage = baseService.buildResourceInfo(
                    eventMessage, startTime, endTime, null,
                    hisInfo.getRetryCount(), DisposeTypeEnum.ERROR.getCode(),
                    "获取令牌失败"
            );
            kafkaUtil.sendToResourceTopic(resourceMessage);

            return new LogicBO(null, false);
        } else {
            log.info("成功获取令牌");
            return new LogicBO(null, true);
        }
    }

    /**
     * 获取图片流
     *
     * @param eventMessage
     * @param startTime
     * @param hisInfo
     * @param isRetry
     * @return
     */
    public LogicBO downloadImageStream(EventMessage eventMessage,
                                       LocalDateTime startTime,
                                       FloodImageTransferHis hisInfo,
                                       Boolean isRetry) {
        // 细分性能分析：图片下载
        long downloadStart = System.currentTimeMillis();
        InputStream inputStream;
        try {
            String imageUrl = eventMessage.getImageUrl();
            inputStream = imageDownloadService.downloadImage(imageUrl);
        } catch (RuntimeException e) {
            long downloadTime = System.currentTimeMillis() - downloadStart;
            log.warn("获取图片流异常，耗时: {}ms，一般为连接超时或响应超时", downloadTime);

            // 执行实际下载时异常 更新消息记录状态为error 重试次数+1
            hisService.errorAndAddRetryCount(hisInfo);

            if (isRetry) {
                // 判断事件是否已经重试
                baseService.checkRetryCount(2, hisInfo.getRetryCount(), eventMessage, startTime,
                        "获取图片流异常");
            }

            return new LogicBO(null, false);
        }

        if (inputStream == null) {
            long downloadTime = System.currentTimeMillis() - downloadStart;
            log.error("获取图片流失败，耗时: {}ms", downloadTime);

            // 更新消息记录为 error 重试+1
            hisService.errorAndAddRetryCount(hisInfo);

            // 向kafka中推送事件失败处理结果
            LocalDateTime endTime = LocalDateTime.now();
            ResourceMessage resourceMessage = baseService.buildResourceInfo(eventMessage, startTime, endTime, null,
                    hisInfo.getRetryCount(), DisposeTypeEnum.ERROR.getCode(),
                    "获取图片流失败");
            kafkaUtil.sendToResourceTopic(resourceMessage);

            return new LogicBO(null, false);
        }
        long downloadTime = System.currentTimeMillis() - downloadStart;
        log.info("图片下载完成，耗时: {}ms", downloadTime);

        return new LogicBO(null, true, inputStream);
    }

    /**
     * 上传minio
     *
     * @param eventMessage
     * @param startTime
     * @param hisInfo
     * @param isRetry
     * @param inputStream
     * @return
     */
    public LogicBO uploadToMinio(EventMessage eventMessage,
                                 LocalDateTime startTime,
                                 FloodImageTransferHis hisInfo,
                                 Boolean isRetry,
                                 InputStream inputStream) {
        // 细分性能分析：MinIO上传
        long uploadStart = System.currentTimeMillis();
        String minioImageUrl;

        try {
            String cameraCode;
            // 优先使用内码生成 其次使用外码
            if (StrUtil.isNotEmpty(eventMessage.getCameraIndexCode())) {
                cameraCode = eventMessage.getCameraIndexCode();
            } else {
                cameraCode = eventMessage.getCameraForeignCode();
            }

            if (StrUtil.isEmpty(cameraCode)) {
                log.info("内外码均为空 使用肯定不为空的event_id作为cameraCode");
                cameraCode = eventMessage.getEventId();
            }
            log.debug("上传minio过程中 获取 cameraCode: {}", cameraCode);

            // 上传
            minioImageUrl = minioStreamUtil.uploadStreamWithPath(inputStream, cameraCode,
                    eventMessage.getEventTime(), "image/jpeg", -1, eventMessage.getSourceModule());
        } catch (Exception e) {
            long uploadTime = System.currentTimeMillis() - uploadStart;
            log.error("上传minIO发生异常，耗时: {}ms", uploadTime, e);

            // 上传失败 更新状态为error
            hisService.errorAndAddRetryCount(hisInfo);

            if (isRetry) {
                // 判断事件是否已经重试
                baseService.checkRetryCount(2, hisInfo.getRetryCount(), eventMessage, startTime,
                        "上传minIO发生异常");
            }

            return new LogicBO(null, false);
        }

        long uploadTime = System.currentTimeMillis() - uploadStart;
        log.info("MinIO上传完成，耗时: {}ms", uploadTime);


        long t4 = System.currentTimeMillis();
        if (StrUtil.isEmpty(minioImageUrl)) {
            // 上传失败 更新状态为error
            log.info("minioImageUrl为空 标记为上传失败");
            hisService.errorAndAddRetryCount(hisInfo);

            if (isRetry) {
                // 判断事件是否已经重试
                baseService.checkRetryCount(2, hisInfo.getRetryCount(), eventMessage, startTime,
                        "minioImageUrl为空 标记为上传失败");
            }

            return new LogicBO(null, false);
        } else {
            // 1. MinIO上传结果日志
            log.info("【调试】MinIO上传成功 - eventId: {}, minioImageUrl: {}",
                eventMessage.getEventId(), minioImageUrl);

            // 2. 数据库更新前日志 - 记录对象当前状态
            log.info("【调试】准备更新数据库 - eventId: {}, 当前状态: disposeType={}, minioImageUrl={}, updateTime={}, id={}",
                eventMessage.getEventId(), hisInfo.getDisposeType(), hisInfo.getMinioImageUrl(),
                hisInfo.getUpdateTime(), hisInfo.getId());

            // 设置对象属性
            hisInfo.setDisposeType(DisposeTypeEnum.SUCCESS.getCode());
            hisInfo.setMinioImageUrl(minioImageUrl);
            hisInfo.setUpdateTime(LocalDateTime.now()); // 显式设置更新时间

            // 3. 数据库更新前日志 - 记录即将更新的值
            log.info("【调试】设置新值后准备更新 - eventId: {}, 新状态: disposeType={}, minioImageUrl={}, updateTime={}",
                eventMessage.getEventId(), hisInfo.getDisposeType(), hisInfo.getMinioImageUrl(), hisInfo.getUpdateTime());

            // 4. 执行数据库更新并记录结果
            boolean updateResult = hisService.saveOrUpdate(hisInfo);

            // 5. 数据库更新结果日志
            log.info("【调试】数据库更新结果 - eventId: {}, updateResult: {}, 对象状态: disposeType={}, minioImageUrl={}, updateTime={}",
                eventMessage.getEventId(), updateResult, hisInfo.getDisposeType(), hisInfo.getMinioImageUrl(), hisInfo.getUpdateTime());

            // 6. 数据库更新失败处理
            if (!updateResult) {
                log.error("【调试】数据库更新失败但没有异常 - eventId: {}, 将进行错误处理", eventMessage.getEventId());
                // 数据库更新失败时的处理
                hisService.errorAndAddRetryCount(hisInfo);

                if (isRetry) {
                    baseService.checkRetryCount(2, hisInfo.getRetryCount(), eventMessage, startTime, "数据库更新失败");
                }

                return new LogicBO(null, false);
            }

            // 7. 关键字段验证日志
            log.info("【调试】数据库更新成功，验证关键字段 - eventId: {}, id: {}, disposeType: {}, minioImageUrl: {}, updateTime: {}",
                eventMessage.getEventId(), hisInfo.getId(), hisInfo.getDisposeType(),
                hisInfo.getMinioImageUrl(), hisInfo.getUpdateTime());

            // 标记结束时间
            LocalDateTime endTime = LocalDateTime.now();
            // 向kafka中推送事件成功处理结果
            ResourceMessage resourceMessage = baseService.buildResourceInfo(eventMessage, startTime, endTime, minioImageUrl,
                    hisInfo.getRetryCount(), "completed", "保存成功");
            kafkaUtil.sendToResourceTopic(resourceMessage);

            log.info("落库并向kafka中推送事件成功处理结果 耗时 {}ms", System.currentTimeMillis() - t4);

            return new LogicBO(minioImageUrl, true, hisInfo);
        }
    }

    /**
     * 判断服务器地址、获取令牌和上传minio
     * @param eventMessage
     * @param startTime
     * @param hisInfo
     * @param isRetry
     * @return
     */
    public LogicBO checkExistAndUpload(EventMessage eventMessage,
                                       LocalDateTime startTime,
                                       FloodImageTransferHis hisInfo,
                                       boolean isRetry) {
        // 3.判断服务器地址
        LogicBO checkServerAddressBO = this.checkServerAddress(eventMessage, startTime, hisInfo);
        if (!checkServerAddressBO.isContinue()) {
            return checkServerAddressBO;
        }

        // 4.获取令牌
        LogicBO acquireTokenBO = this.acquireToken(eventMessage, startTime, hisInfo);
        if (!acquireTokenBO.isContinue()) {
            return acquireTokenBO;
        }

        // 5.获取图片流
        LogicBO downloadImageStreamBO = this.downloadImageStream(eventMessage, startTime, hisInfo, isRetry);
        if (!downloadImageStreamBO.isContinue()) {
            return downloadImageStreamBO;
        }

        // 6.上传到MinIO
        InputStream inputStream = downloadImageStreamBO.getInputStream();
        return this.uploadToMinio(eventMessage, startTime, hisInfo, isRetry, inputStream);
    }
}
