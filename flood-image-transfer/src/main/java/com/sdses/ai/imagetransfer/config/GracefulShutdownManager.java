package com.sdses.ai.imagetransfer.config;

import com.sdses.ai.imagetransfer.service.async.AsyncTaskTracker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.List;
import java.util.ArrayList;

/**
 * 优雅停止管理器
 * 负责在应用关闭时优雅地停止所有异步执行组件
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@Component
public class GracefulShutdownManager implements ApplicationListener<ContextClosedEvent> {

    @Value("${graceful-shutdown.timeout-seconds:10}")
    private int shutdownTimeoutSeconds;

    @Value("${graceful-shutdown.enabled:true}")
    private boolean gracefulShutdownEnabled;

    @Value("${graceful-shutdown.force-shutdown-after-timeout:true}")
    private boolean forceShutdownAfterTimeout;

    @Resource
    private KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;

    @Resource
    private AsyncTaskTracker asyncTaskTracker;

    // 存储所有需要优雅停止的执行器
    private final List<ExecutorService> executorServices = new ArrayList<>();

    // 存储所有需要优雅停止的调度器
    private final List<ScheduledExecutorService> scheduledExecutorServices = new ArrayList<>();

    // 停止状态标记
    private final AtomicBoolean shutdownInProgress = new AtomicBoolean(false);
    private final AtomicBoolean shutdownCompleted = new AtomicBoolean(false);

    /**
     * 注册需要优雅停止的执行器
     *
     * @param executorService 执行器服务
     */
    public void registerExecutorService(ExecutorService executorService) {
        if (executorService != null) {
            executorServices.add(executorService);
            log.debug("注册执行器服务用于优雅停止: {}", executorService.getClass().getSimpleName());
        }
    }

    /**
     * 注册需要优雅停止的调度执行器
     *
     * @param scheduledExecutorService 调度执行器服务
     */
    public void registerScheduledExecutorService(ScheduledExecutorService scheduledExecutorService) {
        if (scheduledExecutorService != null) {
            scheduledExecutorServices.add(scheduledExecutorService);
            log.debug("注册调度执行器服务用于优雅停止: {}", scheduledExecutorService.getClass().getSimpleName());
        }
    }

    /**
     * 监听应用关闭事件
     */
    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        if (!gracefulShutdownEnabled) {
            log.info("优雅停止功能已禁用，跳过优雅停止流程");
            return;
        }

        if (shutdownInProgress.compareAndSet(false, true)) {
            log.info("开始执行优雅停止流程，超时时间: {} 秒", shutdownTimeoutSeconds);
            performGracefulShutdown();
        }
    }

    /**
     * PreDestroy注解确保在Bean销毁前执行优雅停止
     */
    @PreDestroy
    public void preDestroy() {
        if (!shutdownCompleted.get() && gracefulShutdownEnabled) {
            log.info("通过@PreDestroy触发优雅停止流程");
            if (shutdownInProgress.compareAndSet(false, true)) {
                performGracefulShutdown();
            }
        }
    }

    /**
     * 执行优雅停止流程
     */
    private void performGracefulShutdown() {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("优雅停止流程开始，开始时间: {}", startTime);

        try {
            // 1. 停止Kafka消费者
            stopKafkaConsumers();

            // 2. 停止定时任务调度器
            stopScheduledExecutors();

            // 3. 等待当前正在处理的任务完成
            waitForTasksCompletion();

            // 4. 停止所有执行器
            shutdownExecutors();

            shutdownCompleted.set(true);
            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            log.info("优雅停止流程完成，耗时: {} 毫秒", duration.toMillis());

        } catch (Exception e) {
            log.error("优雅停止流程执行过程中发生异常", e);
            if (forceShutdownAfterTimeout) {
                log.warn("由于异常，将执行强制停止");
                forceShutdown();
            }
        }
    }

    /**
     * 停止Kafka消费者
     */
    private void stopKafkaConsumers() {
        log.info("开始停止Kafka消费者...");
        
        try {
            // 停止所有Kafka监听器容器
            for (MessageListenerContainer container : kafkaListenerEndpointRegistry.getListenerContainers()) {
                if (container.isRunning()) {
                    log.info("停止Kafka监听器容器: {}", container.getListenerId());
                    container.stop();
                }
            }

            // 等待所有容器停止
            long waitStart = System.currentTimeMillis();
            while (System.currentTimeMillis() - waitStart < TimeUnit.SECONDS.toMillis(5)) {
                boolean allStopped = kafkaListenerEndpointRegistry.getListenerContainers()
                        .stream()
                        .noneMatch(MessageListenerContainer::isRunning);
                
                if (allStopped) {
                    log.info("所有Kafka消费者已停止");
                    break;
                }
                
                Thread.sleep(100);
            }
            
        } catch (Exception e) {
            log.error("停止Kafka消费者时发生异常", e);
        }
    }

    /**
     * 停止定时任务调度器
     */
    private void stopScheduledExecutors() {
        log.info("开始停止 {} 个定时任务调度器...", scheduledExecutorServices.size());

        for (ScheduledExecutorService scheduler : scheduledExecutorServices) {
            try {
                log.info("停止定时任务调度器: {}", scheduler.getClass().getSimpleName());
                scheduler.shutdown();

                // 等待调度器停止，最多等待2秒
                boolean terminated = scheduler.awaitTermination(2, TimeUnit.SECONDS);
                if (terminated) {
                    log.info("定时任务调度器已停止: {}", scheduler.getClass().getSimpleName());
                } else {
                    log.warn("定时任务调度器未在指定时间内停止: {}", scheduler.getClass().getSimpleName());
                    scheduler.shutdownNow();
                }

            } catch (Exception e) {
                log.error("停止定时任务调度器时发生异常: {}", scheduler.getClass().getSimpleName(), e);
            }
        }
    }

    /**
     * 等待当前正在处理的任务完成
     */
    private void waitForTasksCompletion() {
        log.info("等待当前正在处理的任务完成...");

        // 获取当前活跃任务数量
        int activeTaskCount = asyncTaskTracker.getActiveTaskCount();
        if (activeTaskCount > 0) {
            log.info("当前有 {} 个活跃任务正在执行，等待完成...", activeTaskCount);

            // 等待任务完成，最多等待shutdownTimeoutSeconds的一半时间
            long waitTimeMillis = TimeUnit.SECONDS.toMillis(shutdownTimeoutSeconds / 2);
            boolean allCompleted = asyncTaskTracker.waitForAllTasksCompletion(waitTimeMillis);

            if (allCompleted) {
                log.info("所有活跃任务已完成");
            } else {
                int remainingTasks = asyncTaskTracker.getActiveTaskCount();
                log.warn("等待任务完成超时，仍有 {} 个任务正在执行", remainingTasks);

                // 打印剩余任务详情
                asyncTaskTracker.getActiveTasks().forEach((taskId, taskInfo) ->
                    log.warn("未完成任务: {} [{}] - {}", taskId, taskInfo.getTaskType(), taskInfo.getDescription()));
            }
        } else {
            log.info("当前没有活跃任务");
        }
    }

    /**
     * 停止所有执行器
     */
    private void shutdownExecutors() {
        log.info("开始停止 {} 个执行器服务...", executorServices.size());
        
        // 首先尝试优雅停止
        for (ExecutorService executor : executorServices) {
            try {
                log.info("优雅停止执行器: {}", executor.getClass().getSimpleName());
                executor.shutdown();
            } catch (Exception e) {
                log.error("停止执行器时发生异常: {}", executor.getClass().getSimpleName(), e);
            }
        }

        // 等待执行器停止
        boolean allTerminated = waitForExecutorsTermination();
        
        if (!allTerminated && forceShutdownAfterTimeout) {
            log.warn("部分执行器未在超时时间内停止，执行强制停止");
            forceShutdownExecutors();
        }
    }

    /**
     * 等待执行器终止
     */
    private boolean waitForExecutorsTermination() {
        long deadline = System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(shutdownTimeoutSeconds);
        
        for (ExecutorService executor : executorServices) {
            try {
                long remainingTime = deadline - System.currentTimeMillis();
                if (remainingTime <= 0) {
                    log.warn("等待执行器停止超时: {}", executor.getClass().getSimpleName());
                    return false;
                }
                
                boolean terminated = executor.awaitTermination(remainingTime, TimeUnit.MILLISECONDS);
                if (terminated) {
                    log.info("执行器已成功停止: {}", executor.getClass().getSimpleName());
                } else {
                    log.warn("执行器未在指定时间内停止: {}", executor.getClass().getSimpleName());
                    return false;
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待执行器停止时被中断: {}", executor.getClass().getSimpleName());
                return false;
            }
        }
        
        return true;
    }

    /**
     * 强制停止所有执行器
     */
    private void forceShutdownExecutors() {
        log.warn("开始强制停止所有执行器...");
        
        for (ExecutorService executor : executorServices) {
            try {
                List<Runnable> pendingTasks = executor.shutdownNow();
                if (!pendingTasks.isEmpty()) {
                    log.warn("执行器 {} 被强制停止，丢弃了 {} 个待执行任务", 
                            executor.getClass().getSimpleName(), pendingTasks.size());
                }
            } catch (Exception e) {
                log.error("强制停止执行器时发生异常: {}", executor.getClass().getSimpleName(), e);
            }
        }
    }

    /**
     * 强制停止（用于异常情况）
     */
    private void forceShutdown() {
        log.warn("执行强制停止流程");
        forceShutdownExecutors();
        shutdownCompleted.set(true);
    }

    /**
     * 检查是否正在停止
     */
    public boolean isShutdownInProgress() {
        return shutdownInProgress.get();
    }

    /**
     * 检查是否已完成停止
     */
    public boolean isShutdownCompleted() {
        return shutdownCompleted.get();
    }

    /**
     * 获取注册的执行器数量
     */
    public int getRegisteredExecutorCount() {
        return executorServices.size();
    }

    /**
     * 获取注册的调度执行器数量
     */
    public int getRegisteredScheduledExecutorCount() {
        return scheduledExecutorServices.size();
    }
}
