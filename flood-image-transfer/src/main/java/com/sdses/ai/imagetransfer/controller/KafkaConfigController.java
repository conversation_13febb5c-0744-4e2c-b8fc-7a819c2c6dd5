package com.sdses.ai.imagetransfer.controller;

import com.sdses.ai.imagetransfer.config.KafkaConsumerProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka配置管理控制器
 * 提供运行时查看和验证Kafka配置的接口
 *
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@RestController
@RequestMapping("/api/kafka/config")
public class KafkaConfigController {

    @Autowired
    private KafkaConsumerProperties consumerProperties;

    /**
     * 获取当前Kafka消费者配置
     * @return 配置信息
     */
    @GetMapping("/consumer")
    public Map<String, Object> getConsumerConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("groupId", consumerProperties.getGroupId());
        config.put("concurrency", consumerProperties.getConcurrency());
        config.put("retryConcurrency", consumerProperties.getRetryConcurrency());
        config.put("maxPollRecords", consumerProperties.getMaxPollRecords());
        config.put("sessionTimeoutMs", consumerProperties.getSessionTimeoutMs());
        config.put("heartbeatIntervalMs", consumerProperties.getHeartbeatIntervalMs());
        config.put("maxPollIntervalMs", consumerProperties.getMaxPollIntervalMs());
        config.put("fetchMinSize", consumerProperties.getFetchMinSize());
        config.put("fetchMaxWait", consumerProperties.getFetchMaxWait());
        config.put("autoOffsetReset", consumerProperties.getAutoOffsetReset());
        config.put("enableAutoCommit", consumerProperties.getEnableAutoCommit());
        config.put("description", consumerProperties.getConfigDescription());
        config.put("isValid", consumerProperties.isConfigValid());
        return config;
    }

    /**
     * 验证配置合理性
     * @return 验证结果
     */
    @GetMapping("/validate")
    public Map<String, Object> validateConfig() {
        Map<String, Object> result = new HashMap<>();
        result.put("isValid", consumerProperties.isConfigValid());
        result.put("description", consumerProperties.getConfigDescription());
        
        // 详细验证信息
        Map<String, Object> validationDetails = new HashMap<>();
        validationDetails.put("concurrencyValid", consumerProperties.getConcurrency() > 0);
        validationDetails.put("retryConcurrencyValid", consumerProperties.getRetryConcurrency() > 0);
        validationDetails.put("maxPollRecordsValid", consumerProperties.getMaxPollRecords() > 0);
        validationDetails.put("heartbeatValid", 
            consumerProperties.getSessionTimeoutMs() > consumerProperties.getHeartbeatIntervalMs() * 3);
        validationDetails.put("heartbeatIntervalValid", consumerProperties.getHeartbeatIntervalMs() > 0);
        
        result.put("validationDetails", validationDetails);
        
        // 配置建议
        Map<String, String> suggestions = new HashMap<>();
        if (consumerProperties.getConcurrency() > 50) {
            suggestions.put("concurrency", "并发数较高，请确保系统资源充足");
        }
        if (consumerProperties.getMaxPollRecords() > 1000) {
            suggestions.put("maxPollRecords", "批处理大小较大，注意内存使用");
        }
        if (consumerProperties.getSessionTimeoutMs() < 10000) {
            suggestions.put("sessionTimeout", "会话超时时间较短，可能导致频繁重平衡");
        }
        
        result.put("suggestions", suggestions);
        return result;
    }

    /**
     * 获取配置使用指南
     * @return 使用指南
     */
    @GetMapping("/guide")
    public Map<String, Object> getConfigGuide() {
        Map<String, Object> guide = new HashMap<>();
        
        Map<String, String> parameters = new HashMap<>();
        parameters.put("concurrency", "主消费者并发数，建议根据CPU核数和业务量设置");
        parameters.put("retryConcurrency", "重试消费者并发数，建议为主并发数的1/3");
        parameters.put("maxPollRecords", "每次拉取的最大记录数，影响批处理效率");
        parameters.put("sessionTimeoutMs", "会话超时时间，影响故障检测速度");
        parameters.put("heartbeatIntervalMs", "心跳间隔，应为会话超时时间的1/3");
        
        Map<String, Map<String, Integer>> environmentSuggestions = new HashMap<>();
        
        Map<String, Integer> dev = new HashMap<>();
        dev.put("concurrency", 20);
        dev.put("retryConcurrency", 5);
        dev.put("maxPollRecords", 300);
        environmentSuggestions.put("development", dev);
        
        Map<String, Integer> test = new HashMap<>();
        test.put("concurrency", 30);
        test.put("retryConcurrency", 10);
        test.put("maxPollRecords", 500);
        environmentSuggestions.put("test", test);
        
        Map<String, Integer> prod = new HashMap<>();
        prod.put("concurrency", 50);
        prod.put("retryConcurrency", 15);
        prod.put("maxPollRecords", 1000);
        environmentSuggestions.put("production", prod);
        
        guide.put("parameters", parameters);
        guide.put("environmentSuggestions", environmentSuggestions);
        guide.put("currentConfig", consumerProperties.getConfigDescription());
        
        return guide;
    }
}
