package com.sdses.ai.imagetransfer.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Configuration
public class JacksonConfig {

    // 定义目标日期时间格式
    private static final DateTimeFormatter ISO8601_WITH_TIMEZONE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

    @Bean
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper objectMapper = builder.build();

        // 关键配置：忽略 JSON 中不存在的字段
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 确保所有字段都包含在JSON中，即使为null或空值
        objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);

        // 可选的其他常用配置
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        // 创建自定义序列化器
        StdSerializer<LocalDateTime> localDateTimeSerializer = new StdSerializer<LocalDateTime>(LocalDateTime.class) {
            @Override
            public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider provider)
                    throws IOException {
                // 将 LocalDateTime 转换为指定时区的 ZonedDateTime 然后格式化
                gen.writeString(value.atZone(ZoneId.of("Asia/Shanghai"))
                        .format(ISO8601_WITH_TIMEZONE_FORMATTER));
            }
        };

        // 配置 JavaTimeModule
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, localDateTimeSerializer);

        objectMapper.registerModule(javaTimeModule);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return objectMapper;
    }
}
