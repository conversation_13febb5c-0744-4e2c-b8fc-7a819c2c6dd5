package com.sdses.ai.imagetransfer.config;

import com.sdses.ai.imagetransfer.entity.EventMessage;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.util.backoff.FixedBackOff;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka配置类
 * 优化后的Kafka消费者配置，支持通过YAML动态配置消费者数量和性能参数
 *
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@Slf4j
@EnableKafka
@Configuration
@EnableConfigurationProperties({KafkaConsumerProperties.class, KafkaTopicProperties.class})
public class KafkaConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    private final KafkaConsumerProperties consumerProperties;
    private final KafkaTopicProperties topicProperties;

    /**
     * 构造函数注入配置属性
     */
    public KafkaConfig(KafkaConsumerProperties consumerProperties, KafkaTopicProperties topicProperties) {
        this.consumerProperties = consumerProperties;
        this.topicProperties = topicProperties;
    }

    /**
     * 初始化后验证配置
     */
    @PostConstruct
    public void init() {
        log.info("Kafka配置初始化完成，bootstrapServers: {}", bootstrapServers);
        log.info("消费者配置: {}", consumerProperties.getConfigDescription());
        log.info("主题配置: {}", topicProperties.getTopicsDescription());

        // 验证配置有效性
        if (!consumerProperties.isConfigValid()) {
            log.warn("Kafka消费者配置可能存在问题，请检查配置参数");
        }
        if (!topicProperties.isConfigValid()) {
            log.warn("Kafka主题配置不完整，请检查必需的主题配置");
        }
    }

    /**
     * 消费者工厂Bean
     * 使用配置属性类中的参数创建消费者工厂，支持JSON反序列化为EventMessage对象
     */
    @Bean
    public ConsumerFactory<String, EventMessage> consumerFactory() {
        Map<String, Object> config = new HashMap<>();
        // 基础配置
        config.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        config.put(ConsumerConfig.GROUP_ID_CONFIG, consumerProperties.getGroupId());

        // 使用JsonDeserializer支持EventMessage对象反序列化
        config.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        config.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);

        // 性能优化配置
        config.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, consumerProperties.getAutoOffsetReset());
        config.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, consumerProperties.getEnableAutoCommit());
        config.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, consumerProperties.getMaxPollRecords());
        config.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, consumerProperties.getSessionTimeoutMs());
        config.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, consumerProperties.getHeartbeatIntervalMs());
        config.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, consumerProperties.getMaxPollIntervalMs());
        config.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, consumerProperties.getFetchMinSize());
        config.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, consumerProperties.getFetchMaxWait());

        // JSON反序列化配置
        config.put(JsonDeserializer.TRUSTED_PACKAGES, consumerProperties.getTrustedPackages());
        config.put(JsonDeserializer.VALUE_DEFAULT_TYPE, EventMessage.class.getName());
        config.put(JsonDeserializer.USE_TYPE_INFO_HEADERS, consumerProperties.getUseTypeInfoHeaders());

        log.info("创建Kafka消费者工厂，配置参数: maxPollRecords={}, sessionTimeout={}ms",
                consumerProperties.getMaxPollRecords(), consumerProperties.getSessionTimeoutMs());

        return new DefaultKafkaConsumerFactory<>(config);
    }

    /**
     * 字符串消费者工厂Bean（备用）
     * 用于处理原始字符串消息的场景
     */
    @Bean
    public ConsumerFactory<String, String> stringConsumerFactory() {
        Map<String, Object> config = new HashMap<>();
        // 基础配置
        config.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        config.put(ConsumerConfig.GROUP_ID_CONFIG, consumerProperties.getGroupId() + "-string");

        // 使用StringDeserializer处理原始字符串
        config.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        config.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        // 性能优化配置
        config.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, consumerProperties.getAutoOffsetReset());
        config.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, consumerProperties.getEnableAutoCommit());
        config.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, consumerProperties.getMaxPollRecords());
        config.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, consumerProperties.getSessionTimeoutMs());
        config.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, consumerProperties.getHeartbeatIntervalMs());
        config.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, consumerProperties.getMaxPollIntervalMs());
        config.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, consumerProperties.getFetchMinSize());
        config.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, consumerProperties.getFetchMaxWait());

        log.info("创建字符串Kafka消费者工厂（备用），配置参数: maxPollRecords={}, sessionTimeout={}ms",
                consumerProperties.getMaxPollRecords(), consumerProperties.getSessionTimeoutMs());

        return new DefaultKafkaConsumerFactory<>(config);
    }

    /**
     * 主要业务消息的监听器容器工厂
     * 使用配置的并发数处理主要业务消息，支持EventMessage对象反序列化
     */
    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, EventMessage>>
            kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, EventMessage> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.setConcurrency(consumerProperties.getConcurrency());
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.setBatchListener(true); // 保留批量消费能力

        // 设置容器属性
        factory.getContainerProperties().setPollTimeout(3000);
        factory.getContainerProperties().setMissingTopicsFatal(false);
        // 错误处理器
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
                (record, exception) -> {
                    log.error("处理消息失败, topic: {}, partition: {}, offset: {}, 异常: {}",
                            record.topic(), record.partition(), record.offset(), exception.getMessage());
                },
                new FixedBackOff(1000L, 2L)
        );
        factory.setCommonErrorHandler(errorHandler);

        log.info("创建主消费者监听器容器工厂，并发数: {}", consumerProperties.getConcurrency());

        return factory;
    }

    /**
     * 字符串监听器容器工厂（备用）
     * 用于处理原始字符串消息的场景
     */
    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>>
            stringKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(stringConsumerFactory());
        factory.setConcurrency(consumerProperties.getConcurrency());
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.setBatchListener(true);

        // 设置容器属性
        factory.getContainerProperties().setPollTimeout(3000);
        factory.getContainerProperties().setMissingTopicsFatal(false);
        // 错误处理器
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
                (record, exception) -> {
                    log.error("字符串消息处理失败, topic: {}, partition: {}, offset: {}, 异常: {}",
                            record.topic(), record.partition(), record.offset(), exception.getMessage());
                },
                new FixedBackOff(1000L, 2L)
        );
        factory.setCommonErrorHandler(errorHandler);

        log.info("创建字符串消费者监听器容器工厂（备用），并发数: {}", consumerProperties.getConcurrency());

        return factory;
    }

    /**
     * 重试消息的监听器容器工厂
     * 使用较低的并发数处理重试消息，避免对系统造成过大压力，支持EventMessage对象反序列化
     */
    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, EventMessage>>
            retryKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, EventMessage> factory =
                new ConcurrentKafkaListenerContainerFactory<>();

        // 使用相同的消费者工厂（已修改为EventMessage类型）
        factory.setConsumerFactory(consumerFactory());

        // 设置重试专用的并发数
        factory.setConcurrency(consumerProperties.getRetryConcurrency());

        // 确认模式
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);

        // 启用批量监听
        factory.setBatchListener(true);

        // 设置容器属性
        factory.getContainerProperties().setPollTimeout(3000);
        factory.getContainerProperties().setMissingTopicsFatal(false);
        // 重试专用的错误处理器（可自定义重试策略）
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
                (record, exception) -> {
                    log.error("重试队列处理消息最终失败, topic: {}, partition: {}, offset: {}, 异常: {}",
                            record.topic(), record.partition(), record.offset(), exception.getMessage());
                    // 这里可以添加将消息转移到死信队列的逻辑
                },
                new FixedBackOff(1000L, 2L)
        );
        factory.setCommonErrorHandler(errorHandler);
        log.info("创建重试消费者监听器容器工厂，并发数: {}", consumerProperties.getRetryConcurrency());
        return factory;
    }
}
