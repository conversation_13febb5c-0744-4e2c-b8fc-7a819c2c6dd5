package com.sdses.ai.imagetransfer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Kafka主题配置属性类
 * 用于统一管理Kafka主题相关的配置参数
 * 所有配置值从YAML文件中读取
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */
@Data
@ConfigurationProperties(prefix = "spring.kafka.topics")
public class KafkaTopicProperties {

    /**
     * 图片事件主题
     * 用于接收图片处理事件消息
     */
    private String imageEvents;

    /**
     * 重试主题
     * 用于处理失败需要重试的消息
     */
    private String retryTopic;

    /**
     * 资源主题
     * 用于发送处理结果消息
     */
    private String resourceTopic;

    /**
     * 测试事件主题
     * 用于测试EventMessage反序列化
     */
    private String testEvents;

    /**
     * 测试字符串主题
     * 用于测试字符串消息
     */
    private String testString;

    /**
     * 获取所有主题配置的描述
     * @return 主题配置描述
     */
    public String getTopicsDescription() {
        return String.format(
            "Kafka Topics - ImageEvents: %s, RetryTopic: %s, ResourceTopic: %s, TestEvents: %s, TestString: %s",
            imageEvents, retryTopic, resourceTopic, testEvents, testString
        );
    }

    /**
     * 验证主题配置的完整性
     * @return 是否配置完整
     */
    public boolean isConfigValid() {
        return imageEvents != null && !imageEvents.trim().isEmpty()
            && retryTopic != null && !retryTopic.trim().isEmpty()
            && resourceTopic != null && !resourceTopic.trim().isEmpty();
    }

    /**
     * 获取所有必需的主题列表
     * @return 主题列表
     */
    public String[] getRequiredTopics() {
        return new String[]{imageEvents, retryTopic, resourceTopic};
    }

    /**
     * 获取所有可选的主题列表
     * @return 可选主题列表
     */
    public String[] getOptionalTopics() {
        return new String[]{testEvents, testString};
    }
}
