package com.sdses.ai.imagetransfer.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Slf4j
@Configuration
public class RedisConfig {
    /**
     * 配置RedisTemplate
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        try {
            log.info("正在配置RedisTemplate");
            RedisTemplate<String, Object> template = new RedisTemplate<>();
            template.setConnectionFactory(connectionFactory);

            // 设置序列化器
            StringRedisSerializer stringSerializer = new StringRedisSerializer();
            GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();

            // key和hashKey使用String序列化
            template.setKeySerializer(stringSerializer);
            template.setHashKeySerializer(stringSerializer);

            // value和hashValue使用JSON序列化
            template.setValueSerializer(jsonSerializer);
            template.setHashValueSerializer(jsonSerializer);

            template.afterPropertiesSet();
            log.info("RedisTemplate配置完成");
            return template;
        } catch (Exception e) {
            log.error("配置RedisTemplate失败", e);
            throw new RuntimeException("RedisTemplate配置失败", e);
        }
    }
}
