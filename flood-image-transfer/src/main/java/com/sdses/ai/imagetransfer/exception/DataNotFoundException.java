package com.sdses.ai.imagetransfer.exception;

/**
 * 数据未找到异常类
 * 用于处理数据查询时未找到相关数据的情况
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
public class DataNotFoundException extends BusinessException {

    /**
     * 构造函数 - 仅包含错误消息
     * 
     * @param message 错误消息
     */
    public DataNotFoundException(String message) {
        super("DATA_NOT_FOUND", message);
    }

    /**
     * 构造函数 - 包含错误消息和原因异常
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public DataNotFoundException(String message, Throwable cause) {
        super("DATA_NOT_FOUND", message, cause);
    }

    /**
     * 构造函数 - 包含错误消息和上下文
     * 
     * @param message 错误消息
     * @param context 附加上下文信息
     */
    public DataNotFoundException(String message, Object context) {
        super("DATA_NOT_FOUND", message, context);
    }

    // 静态工厂方法

    /**
     * 创建数据未找到异常 - 仅消息
     */
    public static DataNotFoundException of(String message) {
        return new DataNotFoundException(message);
    }

    /**
     * 创建数据未找到异常 - 消息和原因
     */
    public static DataNotFoundException of(String message, Throwable cause) {
        return new DataNotFoundException(message, cause);
    }

    /**
     * 创建数据未找到异常 - 消息和上下文
     */
    public static DataNotFoundException of(String message, Object context) {
        return new DataNotFoundException(message, context);
    }

    /**
     * 根据实体ID创建异常
     */
    public static DataNotFoundException byId(String entityName, String id) {
        return new DataNotFoundException(String.format("%s with ID '%s' not found", entityName, id));
    }

    /**
     * 根据查询条件创建异常
     */
    public static DataNotFoundException byCondition(String entityName, String condition) {
        return new DataNotFoundException(String.format("%s not found with condition: %s", entityName, condition));
    }
}
