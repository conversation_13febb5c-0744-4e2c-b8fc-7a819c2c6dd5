package com.sdses.ai.imagetransfer.scheduled;

import com.sdses.ai.imagetransfer.common.constants.BaseConstant;
import com.sdses.ai.imagetransfer.service.logic.ScheduledService;
import com.sdses.ai.imagetransfer.utils.DistributedLockUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Description 定时任务 - 使用分布式锁控制多副本场景下的执行
 * 适配Spring Boot 3.5.0 + JDK 21
 * @create 2025-07-07 18:16
 */
@Slf4j
@Configuration
public class ScheduledTask {

    @Resource
    private ScheduledService scheduledService;

    @Autowired
    private DistributedLockUtil distributedLockUtil;

    /**
     * 定时任务 - 每15分钟执行一次 处理失败类型的数据
     * 使用分布式锁确保多副本场景下只有一个实例执行
     */
    @Scheduled(cron = "0 */15 * * * *")
    public void scheduledErrorTask() {
        String lockKey = DistributedLockUtil.generateScheduledTaskLockKey("scheduled-task-15min-errorType");

        log.info("补偿失败数据的定时任务开始执行，尝试获取分布式锁: {}", lockKey);

        boolean executed = distributedLockUtil.executeScheduledTask(lockKey, () -> {
            try {
                log.info("开始执行补偿失败数据的定时任务业务逻辑");
                scheduledService.taskErrorData();
                log.info("补偿失败数据定时任务业务逻辑执行完成");
            } catch (Exception e) {
                log.error("补偿失败数据的定时任务执行失败", e);
                throw new RuntimeException("补偿失败数据定时任务执行失败", e);
            }
        });

        if (executed) {
            log.info("定时任务执行成功");
        } else {
            log.info("定时任务跳过执行（其他实例正在执行或获取锁失败）");
        }
    }

    /**
     * 定时任务 - 每15分钟执行一次 处理失败类型的数据
     * 使用分布式锁确保多副本场景下只有一个实例执行
     */
    @Scheduled(cron = "0 */15 * * * *")
    public void scheduledStartTask() {
        String lockKey = DistributedLockUtil.generateScheduledTaskLockKey("scheduled-task-15min-startType");

        log.info("补偿Start状态定时任务开始执行，尝试获取分布式锁: {}", lockKey);

        boolean executed = distributedLockUtil.executeScheduledTask(lockKey, () -> {
            try {
                log.info("开始执行补偿Start状态定时任务业务逻辑");
                scheduledService.taskStartData();
                log.info("补偿Start状态定时任务业务逻辑执行完成");
            } catch (Exception e) {
                log.error("补偿Start状态定时任务执行失败", e);
                throw new RuntimeException("补偿Start状态定时任务执行失败", e);
            }
        });

        if (executed) {
            log.info("定时任务执行成功");
        } else {
            log.info("定时任务跳过执行（其他实例正在执行或获取锁失败）");
        }
    }


    /**
     * 定时任务 - 每1小时执行一次 补偿未处理的数据
     * 使用分布式锁确保多副本场景下只有一个实例执行
     */
    @Scheduled(cron = "0 0 * * * *")
    public void scheduledUntreatedTask() {
        String lockKey = DistributedLockUtil.generateScheduledTaskLockKey("scheduled-task-1hour-Untreated");

        log.info("补偿未处理数据 定时任务开始执行，尝试获取分布式锁: {}", lockKey);

        boolean executed = distributedLockUtil.executeScheduledTask(lockKey, () -> {
            try {
                log.info("开始执行补偿未处理数据 定时任务业务逻辑");
                LocalDateTime now = LocalDateTime.now();

                // startTime: 当前时间前48小时
                LocalDateTime startDateTime = now.minusHours(48);
                String startTime = startDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                // endTime: 当前时间前1小时
                LocalDateTime endDateTime = now.minusHours(1);
                String endTime = endDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                scheduledService.taskUntreatedData(startTime, endTime, BaseConstant.EVENT_TYPE);
                log.info("补偿未处理数据 定时任务业务逻辑执行完成");
            } catch (Exception e) {
                log.error("未处理数据 定时任务执行失败", e);
                throw new RuntimeException("未处理数据 定时任务执行失败", e);
            }
        });

        if (executed) {
            log.info("未处理数据 定时任务执行成功");
        } else {
            log.info("未处理数据 定时任务跳过执行（其他实例正在执行或获取锁失败）");
        }
    }

}
