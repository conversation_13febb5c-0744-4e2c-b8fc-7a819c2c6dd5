package com.sdses.ai.imagetransfer.service;

import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;

import java.util.List;

public interface FloodImageTransferHisService {

    /**
     * 构建info
     *
     * @param id
     * @param eventMessage
     * @param disposeType
     * @param retryCount
     * @param taskId
     * @param dataSource
     * @return
     */
    FloodImageTransferHis buildInfo(String id,
                                    EventMessage eventMessage,
                                    String disposeType,
                                    Integer retryCount, String taskId, String dataSource);

    /**
     * 根据主键判断 新增或修改一个对象
     *
     * @param info
     * @return
     */
    boolean saveOrUpdate(FloodImageTransferHis info);

    boolean saveOrUpdateRetry(FloodImageTransferHis info);

    /**
     * 根据主键查询一条对象
     * @param eventId
     * @return
     */
    FloodImageTransferHis getInfo(String eventId);

    /**
     * 标记指定对象为error 并将重试次数+1
     *
     * @param info
     * @return
     */
    boolean errorAndAddRetryCount(FloodImageTransferHis info);

    /**
     * 条件查询集合
     *
     * @param minRetryCount
     * @param maxRetryCount
     * @param time
     * @param disposeTypeList
     * @return
     */
    List<FloodImageTransferHis> list48HoursData(Integer minRetryCount, Integer maxRetryCount,
                                                String time, List<String> disposeTypeList);

    List<FloodImageTransferHis> listByParams(Integer minRetryCount, Integer maxRetryCount,
                                             String time, String disposeType, Integer hours);

    List<FloodImageTransferHis> listByIds(List<String> eventIds);

    List<FloodImageTransferHis> list15MinData(Integer minRetryCount,
                                              Integer maxRetryCount,
                                              String formattedDateTime,
                                              List<String> disposeTypeList);
}
