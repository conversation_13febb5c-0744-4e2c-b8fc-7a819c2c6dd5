package com.sdses.ai.imagetransfer.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-14 14:00
 */
@Data
public class UploadVO {

    /**
     * 事件id
     */
    private String eventId;
    /**
     * minio图片地址
     */
    private String minioImageUrl;
    /**
     * 处理类型 success error等
     */
    private String disposeType;
    /**
     * 重试次数
     */
    private int retryCount;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 事件时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 内码
     */
    private String cameraIndexCode;
    /**
     * 摄像头id
     */
    private String cameraChannelCode;
    /**
     * 外码
     */
    private String cameraForeignCode;
    /**
     * 原始图片地址
     */
    private String originImageUrl;
}
