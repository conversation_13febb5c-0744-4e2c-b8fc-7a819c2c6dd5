package com.sdses.ai.imagetransfer.controller;

import com.sdses.ai.imagetransfer.config.GracefulShutdownManager;
import com.sdses.ai.imagetransfer.model.ResVoT;
import com.sdses.ai.imagetransfer.service.async.AsyncTaskTracker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 优雅停止控制器
 * 提供优雅停止状态监控和手动控制接口
 *
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@RestController
@RequestMapping("/api/graceful-shutdown")
@RequiredArgsConstructor
public class GracefulShutdownController {

    private final GracefulShutdownManager gracefulShutdownManager;
    private final AsyncTaskTracker asyncTaskTracker;

    /**
     * 获取优雅停止状态
     */
    @GetMapping("/status")
    public ResVoT<Map<String, Object>> getShutdownStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 优雅停止管理器状态
            status.put("shutdownInProgress", gracefulShutdownManager.isShutdownInProgress());
            status.put("shutdownCompleted", gracefulShutdownManager.isShutdownCompleted());
            status.put("registeredExecutorCount", gracefulShutdownManager.getRegisteredExecutorCount());
            status.put("registeredScheduledExecutorCount", gracefulShutdownManager.getRegisteredScheduledExecutorCount());

            // 任务跟踪器状态
            AsyncTaskTracker.TaskStatistics taskStats = asyncTaskTracker.getStatistics();
            Map<String, Object> taskStatus = new HashMap<>();
            taskStatus.put("activeTaskCount", taskStats.getActiveTaskCount());
            taskStatus.put("totalTaskCount", taskStats.getTotalTaskCount());
            taskStatus.put("completedTaskCount", taskStats.getCompletedTaskCount());
            taskStatus.put("timestamp", taskStats.getTimestamp());
            status.put("taskStatistics", taskStatus);

            // 活跃任务详情
            if (taskStats.getActiveTaskCount() > 0) {
                status.put("activeTasks", asyncTaskTracker.getActiveTasks());
            }

            status.put("queryTime", LocalDateTime.now());

            return ResVoT.success("获取优雅停止状态", status);

        } catch (Exception e) {
            log.error("获取优雅停止状态失败", e);
            return ResVoT.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取活跃任务详情
     */
    @GetMapping("/active-tasks")
    public ResVoT<Map<String, Object>> getActiveTasks() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("activeTaskCount", asyncTaskTracker.getActiveTaskCount());
            result.put("activeTasks", asyncTaskTracker.getActiveTasks());
            result.put("activeTaskIds", asyncTaskTracker.getActiveTaskIds());
            result.put("queryTime", LocalDateTime.now());

            return ResVoT.success("获取活跃任务详情", result);

        } catch (Exception e) {
            log.error("获取活跃任务详情失败", e);
            return ResVoT.error("获取活跃任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/task-statistics")
    public ResVoT<AsyncTaskTracker.TaskStatistics> getTaskStatistics() {
        try {
            AsyncTaskTracker.TaskStatistics statistics = asyncTaskTracker.getStatistics();
            return ResVoT.success("获取任务统计信息", statistics);

        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            return ResVoT.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否可以安全停止
     * 判断当前是否有活跃任务
     */
    @GetMapping("/can-shutdown")
    public ResVoT<Map<String, Object>> canShutdown() {
        try {
            boolean hasActiveTasks = asyncTaskTracker.hasActiveTasks();
            int activeTaskCount = asyncTaskTracker.getActiveTaskCount();

            Map<String, Object> result = new HashMap<>();
            result.put("canShutdown", !hasActiveTasks);
            result.put("activeTaskCount", activeTaskCount);
            result.put("reason", hasActiveTasks ? "存在活跃任务" : "无活跃任务，可以安全停止");
            result.put("checkTime", LocalDateTime.now());

            return ResVoT.success("检查是否可以安全停止", result);

        } catch (Exception e) {
            log.error("检查停止条件失败", e);
            return ResVoT.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 等待任务完成
     *
     * @param timeoutSeconds 等待超时时间（秒）
     */
    @PostMapping("/wait-tasks-completion")
    public ResVoT<Map<String, Object>> waitTasksCompletion(
            @RequestParam(defaultValue = "30") int timeoutSeconds) {
        try {
            log.info("开始等待任务完成，超时时间: {} 秒", timeoutSeconds);

            LocalDateTime startTime = LocalDateTime.now();
            int initialActiveCount = asyncTaskTracker.getActiveTaskCount();

            boolean allCompleted = asyncTaskTracker.waitForAllTasksCompletion(timeoutSeconds * 1000L);

            LocalDateTime endTime = LocalDateTime.now();
            int finalActiveCount = asyncTaskTracker.getActiveTaskCount();

            Map<String, Object> result = new HashMap<>();
            result.put("allCompleted", allCompleted);
            result.put("initialActiveCount", initialActiveCount);
            result.put("finalActiveCount", finalActiveCount);
            result.put("startTime", startTime);
            result.put("endTime", endTime);
            result.put("timeoutSeconds", timeoutSeconds);

            if (allCompleted) {
                log.info("所有任务已完成");
                result.put("message", "所有任务已完成");
            } else {
                log.warn("等待任务完成超时，仍有 {} 个活跃任务", finalActiveCount);
                result.put("message", String.format("等待超时，仍有 %d 个活跃任务", finalActiveCount));
                result.put("remainingTasks", asyncTaskTracker.getActiveTasks());
            }

            return ResVoT.success("等待任务完成", result);

        } catch (Exception e) {
            log.error("等待任务完成失败", e);
            return ResVoT.error("等待失败: " + e.getMessage());
        }
    }

    /**
     * 重置任务统计信息
     */
    @PostMapping("/reset-statistics")
    public ResVoT<String> resetStatistics() {
        try {
            asyncTaskTracker.reset();
            log.info("任务统计信息已重置");
            return ResVoT.success("统计信息重置成功");

        } catch (Exception e) {
            log.error("重置统计信息失败", e);
            return ResVoT.error("重置失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    public ResVoT<Map<String, Object>> getHealth() {
        try {
            Map<String, Object> health = new HashMap<>();

            // 检查优雅停止状态
            boolean shutdownInProgress = gracefulShutdownManager.isShutdownInProgress();
            boolean shutdownCompleted = gracefulShutdownManager.isShutdownCompleted();

            // 检查任务状态
            boolean hasActiveTasks = asyncTaskTracker.hasActiveTasks();
            int activeTaskCount = asyncTaskTracker.getActiveTaskCount();

            // 综合健康状态
            boolean healthy = !shutdownInProgress && !shutdownCompleted;

            health.put("healthy", healthy);
            health.put("shutdownInProgress", shutdownInProgress);
            health.put("shutdownCompleted", shutdownCompleted);
            health.put("hasActiveTasks", hasActiveTasks);
            health.put("activeTaskCount", activeTaskCount);
            health.put("checkTime", LocalDateTime.now());

            if (!healthy) {
                if (shutdownInProgress) {
                    health.put("reason", "系统正在执行优雅停止");
                } else if (shutdownCompleted) {
                    health.put("reason", "系统已完成优雅停止");
                }
            } else {
                health.put("reason", "系统运行正常");
            }

            return ResVoT.success("获取系统健康状态", health);

        } catch (Exception e) {
            log.error("获取健康状态失败", e);
            return ResVoT.error("健康检查失败: " + e.getMessage());
        }
    }
}
