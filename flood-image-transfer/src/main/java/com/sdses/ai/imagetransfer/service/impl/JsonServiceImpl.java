package com.sdses.ai.imagetransfer.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.service.JsonService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class JsonServiceImpl implements JsonService {

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public EventMessage json2EventMessageBean(String msg) {
        try {
            EventMessage eventMessage = objectMapper.readValue(msg, EventMessage.class);

            return eventMessage;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
