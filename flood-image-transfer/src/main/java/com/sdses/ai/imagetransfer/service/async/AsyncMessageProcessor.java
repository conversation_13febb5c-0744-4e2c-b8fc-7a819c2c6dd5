package com.sdses.ai.imagetransfer.service.async;

import com.sdses.ai.imagetransfer.entity.EventMessage;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 异步消息处理器
 * 提供不同的异步处理策略和监控功能
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncMessageProcessor {

    private final AsyncImageProcessingService asyncImageProcessingService;
    private final AsyncProcessingMonitor asyncProcessingMonitor;

    @Resource
    private AsyncTaskTracker asyncTaskTracker;

    @Value("${async.processing.timeout-seconds:300}")
    private long processingTimeoutSeconds;

    @Value("${async.processing.batch-size:100}")
    private int batchSize;

    @Value("${async.processing.parallel-enabled:true}")
    private boolean parallelEnabled;

    /**
     * 异步处理消息列表
     * 支持批处理和超时控制
     * 
     * @param messages 消息列表
     * @return CompletableFuture<ProcessingResult> 处理结果
     */
    @Async("kafkaAsyncExecutor")
    public CompletableFuture<ProcessingResult> processMessagesAsync(List<EventMessage> messages) {
        String taskId = "batch-" + UUID.randomUUID().toString().substring(0, 8);
        LocalDateTime startTime = LocalDateTime.now();

        // 开始跟踪任务
        asyncTaskTracker.startTask(taskId, "MESSAGE_BATCH",
                String.format("处理消息批次，数量: %d", messages.size()));

        log.info("开始异步处理消息批次，任务ID: {}, 消息数量: {}, 并行处理: {}",
                taskId, messages.size(), parallelEnabled);

        try {
            CompletableFuture<Integer> processingFuture;

            // 并行处理模式
            processingFuture = processInParallel(messages);

            // 设置超时时间
            CompletableFuture<Integer> timeoutFuture = processingFuture
                    .orTimeout(processingTimeoutSeconds, TimeUnit.SECONDS);

            return timeoutFuture.thenApply(successCount -> {
                LocalDateTime endTime = LocalDateTime.now();
                Duration duration = Duration.between(startTime, endTime);

                ProcessingResult result = ProcessingResult.builder()
                        .totalMessages(messages.size())
                        .successCount(successCount)
                        .failedCount(messages.size() - successCount)
                        .processingTime(duration)
                        .startTime(startTime)
                        .endTime(endTime)
                        .parallelProcessing(parallelEnabled)
                        .build();

                // 记录处理结果到监控系统
                asyncProcessingMonitor.recordProcessingResult(result);

                // 完成任务跟踪
                asyncTaskTracker.completeTask(taskId, successCount > 0);

                log.info("异步处理完成，任务ID: {}, 结果: {}", taskId, result);
                return result;
            }).exceptionally(throwable -> {
                // 异常情况下也要完成任务跟踪
                asyncTaskTracker.completeTask(taskId, false);

                log.error("异步处理任务 {} 发生异常", taskId, throwable);
                return ProcessingResult.builder()
                        .totalMessages(messages.size())
                        .successCount(0)
                        .failedCount(messages.size())
                        .processingTime(Duration.between(startTime, LocalDateTime.now()))
                        .startTime(startTime)
                        .endTime(LocalDateTime.now())
                        .parallelProcessing(parallelEnabled)
                        .build();
            });

        } catch (Exception e) {
            // 异常情况下完成任务跟踪
            asyncTaskTracker.completeTask(taskId, false);

            log.error("异步处理消息时发生异常，任务ID: {}", taskId, e);
            return CompletableFuture.completedFuture(
                ProcessingResult.builder()
                    .totalMessages(messages.size())
                    .successCount(0)
                    .failedCount(messages.size())
                    .processingTime(Duration.between(startTime, LocalDateTime.now()))
                    .startTime(startTime)
                    .endTime(LocalDateTime.now())
                    .parallelProcessing(parallelEnabled)
                    .error(e.getMessage())
                    .build()
            );
        }
    }

    /**
     * 并行处理消息
     * 利用虚拟线程的优势进行高并发处理
     * 
     * @param messages 消息列表
     * @return CompletableFuture<Integer> 成功处理的消息数量
     */
    private CompletableFuture<Integer> processInParallel(List<EventMessage> messages) {
        log.debug("使用并行模式处理 {} 条消息", messages.size());
        
        // 如果消息数量较大，分批处理
        if (messages.size() > batchSize) {
            return processBatches(messages);
        } else {
            return asyncImageProcessingService.processBatchAsync(messages);
        }
    }

    /**
     * 分批处理大量消息
     * 避免一次性创建过多的异步任务
     * 
     * @param messages 消息列表
     * @return CompletableFuture<Integer> 成功处理的消息数量
     */
    private CompletableFuture<Integer> processBatches(List<EventMessage> messages) {
        log.debug("分批处理 {} 条消息，批次大小: {}", messages.size(), batchSize);

        List<CompletableFuture<Integer>> batchFutures = new ArrayList<>();
        
        for (int i = 0; i < messages.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, messages.size());
            List<EventMessage> batch = messages.subList(i, endIndex);
            
            CompletableFuture<Integer> batchFuture =
                    asyncImageProcessingService.processBatchAsync(batch);
            batchFutures.add(batchFuture);
        }
        
        // 等待所有批次完成并汇总结果
        CompletableFuture<Void> allBatches = CompletableFuture.allOf(
                batchFutures.toArray(new CompletableFuture[0])
        );
        
        return allBatches.thenApply(v -> 
            batchFutures.stream()
                .mapToInt(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("获取批次处理结果失败", e);
                        return 0;
                    }
                })
                .sum()
        );
    }

    /**
     * 处理结果数据类
     */
    @lombok.Builder
    @lombok.Data
    public static class ProcessingResult {
        private int totalMessages;
        private int successCount;
        private int failedCount;
        private Duration processingTime;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean parallelProcessing;
        private String error;

        public double getSuccessRate() {
            return totalMessages > 0 ? (double) successCount / totalMessages * 100 : 0;
        }

        public double getMessagesPerSecond() {
            // 使用精确的毫秒计算，避免getSeconds()的截断问题
            long totalMillis = processingTime.toMillis();
            if (totalMillis > 0) {
                // 转换为秒：毫秒 / 1000.0
                double totalSeconds = totalMillis / 1000.0;
                return totalMessages / totalSeconds;
            }
            return 0;
        }

        @Override
        public String toString() {
            return String.format(
                "ProcessingResult{total=%d, success=%d, failed=%d, rate=%.2f%%, time=%dms, throughput=%.2f msg/s, parallel=%s}",
                totalMessages, successCount, failedCount, getSuccessRate(), 
                processingTime.toMillis(), getMessagesPerSecond(), parallelProcessing
            );
        }
    }
}
