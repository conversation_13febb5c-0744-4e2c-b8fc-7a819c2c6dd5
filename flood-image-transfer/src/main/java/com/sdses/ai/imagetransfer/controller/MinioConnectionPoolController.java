package com.sdses.ai.imagetransfer.controller;

import com.sdses.ai.imagetransfer.service.minio.MinioConnectionPoolMonitor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * MinIO连接池监控控制器
 * 提供连接池状态查询和管理接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */
@Slf4j
@RestController
@RequestMapping("/api/minio/connection-pool")
@RequiredArgsConstructor
public class MinioConnectionPoolController {

    private final MinioConnectionPoolMonitor connectionPoolMonitor;

    /**
     * 获取连接池状态
     */
    @GetMapping("/status")
    public Map<String, Object> getConnectionPoolStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("status", connectionPoolMonitor.getConnectionPoolStatus());
            result.put("config", connectionPoolMonitor.getConnectionPoolConfig());
            result.put("performance", connectionPoolMonitor.getPerformanceStats());
            result.put("simple", connectionPoolMonitor.getSimpleStatus());
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("获取MinIO连接池状态失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取详细报告
     */
    @GetMapping("/report")
    public Map<String, Object> getDetailedReport() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("report", connectionPoolMonitor.getDetailedReport());
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("获取MinIO连接池详细报告失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取性能统计
     */
    @GetMapping("/performance")
    public Map<String, Object> getPerformanceStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("performance", connectionPoolMonitor.getPerformanceStats());
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("获取MinIO性能统计失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 重置统计数据
     */
    @PostMapping("/reset-stats")
    public Map<String, Object> resetStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            connectionPoolMonitor.resetStats();
            result.put("success", true);
            result.put("message", "MinIO连接池统计数据已重置");
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("重置MinIO连接池统计数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取简化状态（用于快速检查）
     */
    @GetMapping("/simple")
    public Map<String, Object> getSimpleStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("status", connectionPoolMonitor.getSimpleStatus());
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("获取MinIO连接池简化状态失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
