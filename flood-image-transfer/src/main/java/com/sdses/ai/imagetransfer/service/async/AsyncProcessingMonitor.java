package com.sdses.ai.imagetransfer.service.async;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异步处理监控服务
 * 监控异步处理的性能指标和健康状态
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@Slf4j
@Service
public class AsyncProcessingMonitor {

    @Value("${async.monitoring.enabled:true}")
    private boolean monitoringEnabled;

    @Value("${async.monitoring.report-interval-minutes:5}")
    private int reportIntervalMinutes;

    // 性能指标统计
    private final AtomicLong totalProcessedMessages = new AtomicLong(0);
    private final AtomicLong totalSuccessMessages = new AtomicLong(0);
    private final AtomicLong totalFailedMessages = new AtomicLong(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    
    // 线程池状态监控
    private final ConcurrentHashMap<String, ThreadPoolStats> threadPoolStats = new ConcurrentHashMap<>();
    
    // 最近处理的批次信息
    private volatile AsyncMessageProcessor.ProcessingResult lastProcessingResult;
    private volatile LocalDateTime lastProcessingTime;

    /**
     * 记录处理结果
     * 
     * @param result 处理结果
     */
    public void recordProcessingResult(AsyncMessageProcessor.ProcessingResult result) {
        if (!monitoringEnabled) {
            return;
        }

        totalProcessedMessages.addAndGet(result.getTotalMessages());
        totalSuccessMessages.addAndGet(result.getSuccessCount());
        totalFailedMessages.addAndGet(result.getFailedCount());
        totalProcessingTime.addAndGet(result.getProcessingTime().toMillis());
        
        lastProcessingResult = result;
        lastProcessingTime = LocalDateTime.now();
        
        log.debug("记录处理结果: {}", result);
    }

    /**
     * 记录线程池状态
     * 
     * @param poolName 线程池名称
     * @param activeThreads 活跃线程数
     * @param totalThreads 总线程数
     */
    public void recordThreadPoolStats(String poolName, int activeThreads, int totalThreads) {
        if (!monitoringEnabled) {
            return;
        }

        ThreadPoolStats stats = new ThreadPoolStats(activeThreads, totalThreads, LocalDateTime.now());
        threadPoolStats.put(poolName, stats);
    }

    /**
     * 获取性能统计信息
     * 
     * @return 性能统计
     */
    public PerformanceStats getPerformanceStats() {
        long totalMessages = totalProcessedMessages.get();
        long successMessages = totalSuccessMessages.get();
        long failedMessages = totalFailedMessages.get();
        long totalTime = totalProcessingTime.get();
        
        double successRate = totalMessages > 0 ? (double) successMessages / totalMessages * 100 : 0;
        double avgProcessingTime = totalMessages > 0 ? (double) totalTime / totalMessages : 0;
        
        return PerformanceStats.builder()
                .totalProcessedMessages(totalMessages)
                .totalSuccessMessages(successMessages)
                .totalFailedMessages(failedMessages)
                .successRate(successRate)
                .averageProcessingTimeMs(avgProcessingTime)
                .lastProcessingResult(lastProcessingResult)
                .lastProcessingTime(lastProcessingTime)
                .threadPoolStats(new ConcurrentHashMap<>(threadPoolStats))
                .build();
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalProcessedMessages.set(0);
        totalSuccessMessages.set(0);
        totalFailedMessages.set(0);
        totalProcessingTime.set(0);
        threadPoolStats.clear();
        lastProcessingResult = null;
        lastProcessingTime = null;
        
        log.info("异步处理统计信息已重置");
    }

    /**
     * 定期报告性能统计
     */
    @Scheduled(fixedRateString = "#{${async.monitoring.report-interval-minutes:5} * 60 * 1000}")
    public void reportPerformanceStats() {
        if (!monitoringEnabled) {
            return;
        }

        PerformanceStats stats = getPerformanceStats();
        
        if (stats.getTotalProcessedMessages() > 0) {
            log.info("异步处理性能报告: 总消息数={}, 成功率={}%, 平均处理时间={}ms, 线程池数量={}",
                    stats.getTotalProcessedMessages(),
                    stats.getSuccessRate(),
                    stats.getAverageProcessingTimeMs(),
                    stats.getThreadPoolStats().size());
            
            // 输出线程池状态
            stats.getThreadPoolStats().forEach((poolName, poolStats) -> 
                log.info("线程池 [{}]: 活跃线程={}, 总线程={}, 更新时间={}",
                        poolName, poolStats.getActiveThreads(), poolStats.getTotalThreads(), poolStats.getUpdateTime()));
        }
    }

    /**
     * 检查系统健康状态
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        PerformanceStats stats = getPerformanceStats();
        
        // 检查成功率
        if (stats.getTotalProcessedMessages() > 100 && stats.getSuccessRate() < 80) {
            log.warn("异步处理成功率过低: {}%", stats.getSuccessRate());
            return false;
        }
        
        // 检查最近是否有处理活动
        if (lastProcessingTime != null && 
            lastProcessingTime.isBefore(LocalDateTime.now().minusMinutes(reportIntervalMinutes * 2))) {
            log.warn("异步处理长时间无活动，最后处理时间: {}", lastProcessingTime);
            return false;
        }
        
        return true;
    }

    /**
     * 线程池统计信息
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class ThreadPoolStats {
        private int activeThreads;
        private int totalThreads;
        private LocalDateTime updateTime;
    }

    /**
     * 性能统计信息
     */
    @lombok.Builder
    @lombok.Data
    public static class PerformanceStats {
        private long totalProcessedMessages;
        private long totalSuccessMessages;
        private long totalFailedMessages;
        private double successRate;
        private double averageProcessingTimeMs;
        private AsyncMessageProcessor.ProcessingResult lastProcessingResult;
        private LocalDateTime lastProcessingTime;
        private ConcurrentHashMap<String, ThreadPoolStats> threadPoolStats;
    }
}
