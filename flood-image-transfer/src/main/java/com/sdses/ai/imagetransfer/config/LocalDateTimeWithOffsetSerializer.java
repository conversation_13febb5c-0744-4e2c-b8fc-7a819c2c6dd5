package com.sdses.ai.imagetransfer.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class LocalDateTimeWithOffsetSerializer extends JsonSerializer<LocalDateTime> {
    private static final DateTimeFormatter FORMATTER = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

    @Override
    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider provider) 
            throws IOException {
        // 手动指定时区（例如 Asia/Shanghai）
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        String formatted = value.atZone(zoneId).format(FORMATTER);
        gen.writeString(formatted);
    }
}
