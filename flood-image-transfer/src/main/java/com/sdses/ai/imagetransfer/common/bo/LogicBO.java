package com.sdses.ai.imagetransfer.common.bo;

import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import lombok.Data;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-07 18:27
 */
@Data
public class LogicBO {

    private String minioImageUrl;

    // 是否继续
    private boolean isContinue;

    private FloodImageTransferHis hisInfo;

    private InputStream inputStream;

    public LogicBO(String minioImageUrl, boolean isContinue) {
        this.minioImageUrl = minioImageUrl;
        this.isContinue = isContinue;
    }

    public LogicBO(String minioImageUrl, boolean isContinue, FloodImageTransferHis hisInfo) {
        this.minioImageUrl = minioImageUrl;
        this.isContinue = isContinue;
        this.hisInfo = hisInfo;
    }

    public LogicBO(String minioImageUrl, boolean isContinue, InputStream inputStream) {
        this.minioImageUrl = minioImageUrl;
        this.isContinue = isContinue;
        this.inputStream = inputStream;
    }
}
