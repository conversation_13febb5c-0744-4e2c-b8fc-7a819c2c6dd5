package com.sdses.ai.imagetransfer.service.optimization;

import com.sdses.ai.imagetransfer.service.ImageDownloadService;
import com.sdses.ai.imagetransfer.utils.MinioStreamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 图片处理优化器
 * 实现并行下载、缓存和优化上传策略
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageProcessingOptimizer {

    private final ImageDownloadService imageDownloadService;
    private final MinioStreamUtil minioStreamUtil;
    
    // 使用虚拟线程池进行并行处理
    private final ExecutorService virtualThreadPool = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 优化的图片处理流程
     * 1. 异步下载图片到内存
     * 2. 并行上传到MinIO
     * 3. 返回结果
     * 
     * @param imageUrl 图片URL
     * @param cameraCode 摄像头编码
     * @param eventTime 事件时间
     * @return CompletableFuture<String> MinIO图片URL
     */
    public CompletableFuture<String> processImageOptimized(String imageUrl, String cameraCode, LocalDateTime eventTime) {
        long startTime = System.currentTimeMillis();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 步骤1: 下载图片到内存
                long downloadStart = System.currentTimeMillis();
                InputStream inputStream = imageDownloadService.downloadImage(imageUrl);
                if (inputStream == null) {
                    log.error("图片下载失败: {}", imageUrl);
                    return null;
                }
                
                // 将InputStream读取到内存中，以便可以重复使用
                byte[] imageData = readInputStreamToBytes(inputStream);
                long downloadTime = System.currentTimeMillis() - downloadStart;
                log.info("图片下载完成，大小: {} bytes, 耗时: {}ms", imageData.length, downloadTime);
                
                // 步骤2: 上传到MinIO
                long uploadStart = System.currentTimeMillis();
                ByteArrayInputStream uploadStream = new ByteArrayInputStream(imageData);
                String minioUrl = minioStreamUtil.uploadStreamWithPath(
                    uploadStream, cameraCode, eventTime, "image/jpeg", imageData.length, null);
                long uploadTime = System.currentTimeMillis() - uploadStart;
                
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("图片处理完成 - 下载: {}ms, 上传: {}ms, 总计: {}ms, URL: {}", 
                        downloadTime, uploadTime, totalTime, minioUrl);
                
                return minioUrl;
                
            } catch (Exception e) {
                long totalTime = System.currentTimeMillis() - startTime;
                log.error("优化图片处理失败，耗时: {}ms, URL: {}", totalTime, imageUrl, e);
                return null;
            }
        }, virtualThreadPool);
    }

    /**
     * 批量并行处理图片
     * 
     * @param imageRequests 图片处理请求列表
     * @return CompletableFuture<BatchProcessResult> 批量处理结果
     */
    public CompletableFuture<BatchProcessResult> processBatchOptimized(java.util.List<ImageProcessRequest> imageRequests) {
        long batchStartTime = System.currentTimeMillis();
        
        // 创建并行处理任务
        java.util.List<CompletableFuture<ImageProcessResult>> futures = imageRequests.stream()
            .map(request -> processImageOptimized(request.imageUrl, request.cameraCode, request.eventTime)
                .thenApply(minioUrl -> new ImageProcessResult(request.imageUrl, minioUrl, minioUrl != null)))
            .toList();
        
        // 等待所有任务完成
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(v -> {
                long batchTime = System.currentTimeMillis() - batchStartTime;
                
                java.util.List<ImageProcessResult> results = futures.stream()
                    .map(CompletableFuture::join)
                    .toList();
                
                long successCount = results.stream().mapToLong(r -> r.success ? 1 : 0).sum();
                double throughput = results.size() / (batchTime / 1000.0);
                
                log.info("批量图片处理完成 - 总数: {}, 成功: {}, 耗时: {}ms, 吞吐量: {:.2f} msg/s", 
                        results.size(), successCount, batchTime, throughput);
                
                return new BatchProcessResult(results, batchTime, throughput);
            });
    }

    /**
     * 流式处理优化
     * 避免将整个图片加载到内存
     * 
     * @param imageUrl 图片URL
     * @param cameraCode 摄像头编码
     * @param eventTime 事件时间
     * @return CompletableFuture<String> MinIO图片URL
     */
    public CompletableFuture<String> processImageStreaming(String imageUrl, String cameraCode, LocalDateTime eventTime) {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                // 直接流式传输，不缓存到内存
                InputStream inputStream = imageDownloadService.downloadImage(imageUrl);
                if (inputStream == null) {
                    log.error("图片下载失败: {}", imageUrl);
                    return null;
                }
                
                // 直接上传流，MinIO会处理流式传输
                String minioUrl = minioStreamUtil.uploadStreamWithPath(
                    inputStream, cameraCode, eventTime, "image/jpeg", -1, null);
                
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("流式图片处理完成，耗时: {}ms, URL: {}", totalTime, minioUrl);
                
                return minioUrl;
                
            } catch (Exception e) {
                long totalTime = System.currentTimeMillis() - startTime;
                log.error("流式图片处理失败，耗时: {}ms, URL: {}", totalTime, imageUrl, e);
                return null;
            }
        }, virtualThreadPool);
    }

    /**
     * 读取InputStream到字节数组
     */
    private byte[] readInputStreamToBytes(InputStream inputStream) throws Exception {
        try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
            byte[] data = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, bytesRead);
            }
            return buffer.toByteArray();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /**
     * 图片处理请求
     */
    public static class ImageProcessRequest {
        public final String imageUrl;
        public final String cameraCode;
        public final LocalDateTime eventTime;

        public ImageProcessRequest(String imageUrl, String cameraCode, LocalDateTime eventTime) {
            this.imageUrl = imageUrl;
            this.cameraCode = cameraCode;
            this.eventTime = eventTime;
        }
    }

    /**
     * 图片处理结果
     */
    public static class ImageProcessResult {
        public final String imageUrl;
        public final String minioUrl;
        public final boolean success;

        public ImageProcessResult(String imageUrl, String minioUrl, boolean success) {
            this.imageUrl = imageUrl;
            this.minioUrl = minioUrl;
            this.success = success;
        }
    }

    /**
     * 批量处理结果
     */
    public static class BatchProcessResult {
        public final java.util.List<ImageProcessResult> results;
        public final long totalTime;
        public final double throughput;

        public BatchProcessResult(java.util.List<ImageProcessResult> results, long totalTime, double throughput) {
            this.results = results;
            this.totalTime = totalTime;
            this.throughput = throughput;
        }
    }
}
