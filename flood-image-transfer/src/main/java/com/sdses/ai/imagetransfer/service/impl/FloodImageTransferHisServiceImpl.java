package com.sdses.ai.imagetransfer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sdses.ai.imagetransfer.common.enums.DisposeTypeEnum;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.FloodImageTransferHis;
import com.sdses.ai.imagetransfer.mapper.FloodImageTransferHisMapper;
import com.sdses.ai.imagetransfer.service.FloodImageTransferHisService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class FloodImageTransferHisServiceImpl implements FloodImageTransferHisService {

    @Resource
    private FloodImageTransferHisMapper hisMapper;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public FloodImageTransferHis buildInfo(String id,
                                           EventMessage eventMessage,
                                           String disposeType,
                                           Integer retryCount, String taskId, String dataSource) {
        log.info("构建图片转存操作记录: {}", id);
        if (StrUtil.isEmpty(id) || ObjectUtil.isNull(eventMessage)) {
            log.error("构建图片转存操作记录失败，id或kafkaMsg为空");
            return null;
        }

        try {
            String kafkaMsg = objectMapper.writeValueAsString(eventMessage);

            return FloodImageTransferHis.builder()
                    .id(id)
                    .kafkaMsg(kafkaMsg)
                    .disposeType(disposeType)
                    .retryCount(ObjectUtil.isNull(retryCount) ? 0 : retryCount)
                    .createTime(LocalDateTime.now())
                    .eventTime(eventMessage.getEventTime() == null ? LocalDateTime.now() : eventMessage.getEventTime())
                    .taskId(taskId)
                    .dataSource(dataSource)
                    .build();
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean saveOrUpdate(FloodImageTransferHis info) {
        Integer i = hisMapper.saveOrUpdate(info);

        return i > 0;
    }

    @Override
    public boolean saveOrUpdateRetry(FloodImageTransferHis info) {
        info.setUpdateTime(LocalDateTime.now());
        Integer i = hisMapper.saveOrUpdateRetry(info);

        return i > 0;
    }

    @Override
    public FloodImageTransferHis getInfo(String eventId) {

        return hisMapper.selectById(eventId);
    }

    @Override
    public boolean errorAndAddRetryCount(FloodImageTransferHis info) {
        if (ObjectUtil.isNull(info)) {
            return false;
        }
        info.setRetryCount(info.getRetryCount() + 1);
        info.setDisposeType(DisposeTypeEnum.ERROR.getCode());
        info.setUpdateTime(LocalDateTime.now());

        return this.saveOrUpdateRetry(info);
    }

    @Override
    public List<FloodImageTransferHis> list48HoursData(Integer minRetryCount, Integer maxRetryCount,
                                                       String time, List<String> disposeTypeList) {
        return hisMapper.list48HoursData(minRetryCount, maxRetryCount, time, disposeTypeList);
    }

    @Override
    public List<FloodImageTransferHis> listByParams(Integer minRetryCount, Integer maxRetryCount,
                                                    String time, String disposeType, Integer hours) {
        return hisMapper.listByParams(minRetryCount, maxRetryCount, time,disposeType,hours);
    }

    @Override
    public List<FloodImageTransferHis> listByIds(List<String> eventIds) {
        if (CollUtil.isEmpty(eventIds)) {
            log.error("根据id集合查询记录 入参为空 直接返回空集合");
            return List.of();
        }

        try {
            return hisMapper.selectByIds(eventIds);
        } catch (Exception e) {
            log.error("根据id集合查询记录失败", e);
        }
        return List.of();
    }

    @Override
    public List<FloodImageTransferHis> list15MinData(Integer minRetryCount,
                                                     Integer maxRetryCount,
                                                     String formattedDateTime,
                                                     List<String> disposeTypeList) {
        return hisMapper.list15MinData(minRetryCount, maxRetryCount, formattedDateTime, disposeTypeList);

    }
}
