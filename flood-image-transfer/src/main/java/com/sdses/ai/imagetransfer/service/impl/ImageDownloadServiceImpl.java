package com.sdses.ai.imagetransfer.service.impl;

import com.sdses.ai.imagetransfer.service.ImageDownloadService;
import com.sdses.ai.imagetransfer.service.OKHttpService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class ImageDownloadServiceImpl implements ImageDownloadService {

    @Resource
    private OKHttpService okHttpService;

    public InputStream downloadImage(String imageUrl) {

        // 执行实际下载 如果连接超时or响应超时 抛出异常
        InputStream inputStream;
        try {
            inputStream = okHttpService.downloadImageStream(imageUrl);
        } catch (RuntimeException e) {
            throw new RuntimeException(e);
        }

        return inputStream;
    }


    public InputStream downloadImageFromRemote(String imageUrl) throws Exception {

        return null;
    }
}    