package com.sdses.ai.imagetransfer.controller;

import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.model.ResVoT;
import com.sdses.ai.imagetransfer.service.ImageDownloadService;
import com.sdses.ai.imagetransfer.service.TestService;
import com.sdses.ai.imagetransfer.service.impl.JsonServiceImpl;
import com.sdses.ai.imagetransfer.service.logic.ScheduledService;
import com.sdses.ai.imagetransfer.utils.KafkaUtil;
import com.sdses.ai.imagetransfer.utils.MinioStreamUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {

    @Resource
    private JsonServiceImpl jsonService;
    @Resource
    private MinioStreamUtil minioStreamUtil;
    @Resource
    private ImageDownloadService imageDownloadService;
    @Resource
    private ScheduledService scheduledService;
    @Resource
    private KafkaUtil kafkaUtil;
    @Resource
    private TestService testService;

    @PostMapping("/test1")
    public ResVoT<EventMessage> test1(@RequestBody Map<String, String> map) {
        EventMessage eventMessage = jsonService.json2EventMessageBean(map.get("msg"));
        return ResVoT.success("操作成功", eventMessage);
    }

    @PostMapping("/testMinio")
    public void testMinio(@RequestBody Map<String, String> map) {
        String msg = map.get("msg");

        EventMessage eventMessage = jsonService.json2EventMessageBean(msg);

        String imageUrl = eventMessage.getImageUrl();
        // 获取令牌桶 获取图片流
        InputStream inputStream = imageDownloadService.downloadImage(imageUrl);
        if (inputStream == null) {
            // log.error("获取图片流失败");
            // TODO (可选) 判断是否需要进行重试
            return;
        }

        String minioImageUrl = "";
        try {
            // 上传
            minioImageUrl = minioStreamUtil.uploadStreamWithPath(inputStream, eventMessage.getCameraIndexCode(),
                    eventMessage.getEventTime(), "image/jpeg", -1, null);
        } catch (Exception e) {
            // log.error("上传minIO发生异常");
        }
        System.out.println(minioImageUrl);
    }

    @GetMapping("/task")
    public void task() {
        scheduledService.taskErrorData();
    }

    /**
     * 压力测试：批量发送EventMessage消息
     *
     * @param request 压力测试请求参数
     * @return 压力测试结果
     */
    @PostMapping("/stress-test")
    public ResVoT<StressTestResult> stressTest(@RequestBody StressTestRequest request) {
        log.info("开始压力测试，参数: {}", request);

        // 参数验证
        if (request.getMessageCount() <= 0 || request.getMessageCount() > 100000) {
            return ResVoT.error("消息数量必须在1-100000之间");
        }
        if (request.getThreadCount() <= 0 || request.getThreadCount() > 100) {
            return ResVoT.error("线程数量必须在1-100之间");
        }

        try {
            StressTestResult result = executeStressTest(request);
            return ResVoT.success("压力测试完成", result);
        } catch (Exception e) {
            log.error("压力测试执行失败", e);
            return ResVoT.error("压力测试执行失败: " + e.getMessage());
        }
    }

    @GetMapping("/testDatasource")
    public void testDatasource() {
        testService.selectPG();
        testService.selectDoris();
    }

    @GetMapping("testDisposeType")
    public void testDisposeType() {
        testService.testDisposeType();
    }


    /**
     * 执行压力测试
     */
    private StressTestResult executeStressTest(StressTestRequest request) throws InterruptedException {
        long startTime = System.currentTimeMillis();

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(request.getThreadCount());

        // 统计变量
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);
        AtomicLong totalLatency = new AtomicLong(0);
        List<Long> latencies = Collections.synchronizedList(new ArrayList<>());

        // 计算每个线程需要发送的消息数量
        int messagesPerThread = request.getMessageCount() / request.getThreadCount();
        int remainingMessages = request.getMessageCount() % request.getThreadCount();

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 创建发送任务
        for (int i = 0; i < request.getThreadCount(); i++) {
            int threadMessages = messagesPerThread + (i < remainingMessages ? 1 : 0);
            int threadIndex = i;

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                sendMessagesInThread(threadIndex, threadMessages, request, successCount, failedCount, totalLatency, latencies);
            }, executor);

            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        // 关闭线程池
        executor.shutdown();

        // 计算统计信息
        return calculateResult(request, successCount.get(), failedCount.get(),
                totalTime, latencies);
    }

    /**
     * 在单个线程中发送消息
     */
    private void sendMessagesInThread(int threadIndex, int messageCount, StressTestRequest request,
                                      AtomicInteger successCount, AtomicInteger failedCount,
                                      AtomicLong totalLatency, List<Long> latencies) {
        log.info("线程 {} 开始发送 {} 条消息", threadIndex, messageCount);

        for (int i = 0; i < messageCount; i++) {
            long messageStartTime = System.currentTimeMillis();

            try {
                EventMessage message = createTestEventMessage(threadIndex, i, request.getTestType());
                kafkaUtil.sendToEventTopic(message);

                long messageEndTime = System.currentTimeMillis();
                long latency = messageEndTime - messageStartTime;

                successCount.incrementAndGet();
                totalLatency.addAndGet(latency);
                latencies.add(latency);

                // 发送间隔控制
                if (request.getSendIntervalMs() > 0) {
                    Thread.sleep(request.getSendIntervalMs());
                }

            } catch (Exception e) {
                log.error("线程 {} 发送第 {} 条消息失败", threadIndex, i, e);
                failedCount.incrementAndGet();
            }
        }

        log.info("线程 {} 完成消息发送", threadIndex);
    }

    /**
     * 创建测试用的EventMessage
     */
    private EventMessage createTestEventMessage(int threadIndex, int messageIndex, String testType) {
        EventMessage message = new EventMessage();

        String eventId = String.format("stress-test-1306-%d-%d-%d",
                System.currentTimeMillis(), threadIndex, messageIndex);
        message.setEventId(eventId);
        message.setCameraIndexCode("camera-" + (threadIndex % 10 + 1));
        message.setCameraChannelCode("channel-" + (messageIndex % 5 + 1));
        message.setCameraForeignCode("foreign-" + threadIndex);
        message.setEventTime(LocalDateTime.now());
        message.setSourceSystem("flood_prevention");
        message.setSourceModule("water_depth");
        message.setInfo("压力测试消息 - 线程" + threadIndex + "-消息" + messageIndex);
        message.setEventType("flood_detection");

        // 根据测试类型设置不同的数据
        switch (testType.toLowerCase()) {
            case "normal":
                // message.setImageUrl("https://100.192.2.82:18001/data/snapshot/20250707/16/stress-test/" + eventId + ".jpg");
                message.setImageUrl("https://100.192.5.35:18001/data/snapshot/20250716/09/37017724001310204835/f110b25618de83d08aa56d1dbaf156d0.jpg");
                message.setOnlyPicture(1);
                break;
            case "no-image":
                message.setImageUrl(null);
                message.setOnlyPicture(0);
                break;
            case "invalid-url":
                message.setImageUrl("https://invalid-server.com/invalid-path/" + eventId + ".jpg");
                message.setOnlyPicture(1);
                break;
            case "large-data":
                message.setImageUrl("https://100.192.2.82:18001/data/snapshot/20250707/16/stress-test/" + eventId + ".jpg");
                message.setImageBase64(generateLargeBase64Data());
                message.setOnlyPicture(1);
                break;
            default:
                message.setImageUrl("https://100.192.2.82:18001/data/snapshot/20250707/16/stress-test/" + eventId + ".jpg");
                message.setOnlyPicture(1);
        }

        return message;
    }

    /**
     * 生成大数据量的Base64字符串（模拟大图片）
     */
    private String generateLargeBase64Data() {
        StringBuilder sb = new StringBuilder();
        String baseData = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==";
        for (int i = 0; i < 100; i++) {
            sb.append(baseData);
        }
        return sb.toString();
    }

    /**
     * 计算压力测试结果
     */
    private StressTestResult calculateResult(StressTestRequest request, int successCount, int failedCount,
                                             long totalTimeMs, List<Long> latencies) {
        StressTestResult result = new StressTestResult();

        result.setTotalMessages(request.getMessageCount());
        result.setSuccessCount(successCount);
        result.setFailedCount(failedCount);
        result.setTotalTimeMs(totalTimeMs);
        result.setThroughputPerSecond(successCount * 1000.0 / totalTimeMs);

        if (!latencies.isEmpty()) {
            Collections.sort(latencies);
            result.setAvgLatencyMs(latencies.stream().mapToLong(Long::longValue).average().orElse(0.0));
            result.setMinLatencyMs(latencies.get(0));
            result.setMaxLatencyMs(latencies.get(latencies.size() - 1));
            result.setP50LatencyMs(latencies.get(latencies.size() / 2));
            result.setP95LatencyMs(latencies.get((int) (latencies.size() * 0.95)));
            result.setP99LatencyMs(latencies.get((int) (latencies.size() * 0.99)));
        }

        result.setTestType(request.getTestType());
        result.setThreadCount(request.getThreadCount());
        result.setSendIntervalMs(request.getSendIntervalMs());
        result.setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        log.info("压力测试结果: {}", result);
        return result;
    }

    /**
     * 压力测试请求参数
     */
    public static class StressTestRequest {
        private int messageCount = 1000;           // 发送消息总数
        private int threadCount = 10;              // 并发线程数
        private int sendIntervalMs = 0;            // 发送间隔（毫秒）
        private String testType = "normal";        // 测试类型：normal, no-image, invalid-url, large-data

        // Getters and Setters
        public int getMessageCount() {
            return messageCount;
        }

        public void setMessageCount(int messageCount) {
            this.messageCount = messageCount;
        }

        public int getThreadCount() {
            return threadCount;
        }

        public void setThreadCount(int threadCount) {
            this.threadCount = threadCount;
        }

        public int getSendIntervalMs() {
            return sendIntervalMs;
        }

        public void setSendIntervalMs(int sendIntervalMs) {
            this.sendIntervalMs = sendIntervalMs;
        }

        public String getTestType() {
            return testType;
        }

        public void setTestType(String testType) {
            this.testType = testType;
        }

        @Override
        public String toString() {
            return String.format("StressTestRequest{messageCount=%d, threadCount=%d, sendIntervalMs=%d, testType='%s'}",
                    messageCount, threadCount, sendIntervalMs, testType);
        }
    }

    /**
     * 压力测试结果
     */
    public static class StressTestResult {
        private int totalMessages;                 // 总消息数
        private int successCount;                  // 成功数量
        private int failedCount;                   // 失败数量
        private long totalTimeMs;                  // 总耗时（毫秒）
        private double throughputPerSecond;        // 吞吐量（消息/秒）
        private double avgLatencyMs;               // 平均延迟（毫秒）
        private long minLatencyMs;                 // 最小延迟（毫秒）
        private long maxLatencyMs;                 // 最大延迟（毫秒）
        private long p50LatencyMs;                 // P50延迟（毫秒）
        private long p95LatencyMs;                 // P95延迟（毫秒）
        private long p99LatencyMs;                 // P99延迟（毫秒）
        private String testType;                   // 测试类型
        private int threadCount;                   // 线程数
        private int sendIntervalMs;                // 发送间隔
        private String timestamp;                  // 测试时间戳

        // Getters and Setters
        public int getTotalMessages() {
            return totalMessages;
        }

        public void setTotalMessages(int totalMessages) {
            this.totalMessages = totalMessages;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailedCount() {
            return failedCount;
        }

        public void setFailedCount(int failedCount) {
            this.failedCount = failedCount;
        }

        public long getTotalTimeMs() {
            return totalTimeMs;
        }

        public void setTotalTimeMs(long totalTimeMs) {
            this.totalTimeMs = totalTimeMs;
        }

        public double getThroughputPerSecond() {
            return throughputPerSecond;
        }

        public void setThroughputPerSecond(double throughputPerSecond) {
            this.throughputPerSecond = throughputPerSecond;
        }

        public double getAvgLatencyMs() {
            return avgLatencyMs;
        }

        public void setAvgLatencyMs(double avgLatencyMs) {
            this.avgLatencyMs = avgLatencyMs;
        }

        public long getMinLatencyMs() {
            return minLatencyMs;
        }

        public void setMinLatencyMs(long minLatencyMs) {
            this.minLatencyMs = minLatencyMs;
        }

        public long getMaxLatencyMs() {
            return maxLatencyMs;
        }

        public void setMaxLatencyMs(long maxLatencyMs) {
            this.maxLatencyMs = maxLatencyMs;
        }

        public long getP50LatencyMs() {
            return p50LatencyMs;
        }

        public void setP50LatencyMs(long p50LatencyMs) {
            this.p50LatencyMs = p50LatencyMs;
        }

        public long getP95LatencyMs() {
            return p95LatencyMs;
        }

        public void setP95LatencyMs(long p95LatencyMs) {
            this.p95LatencyMs = p95LatencyMs;
        }

        public long getP99LatencyMs() {
            return p99LatencyMs;
        }

        public void setP99LatencyMs(long p99LatencyMs) {
            this.p99LatencyMs = p99LatencyMs;
        }

        public String getTestType() {
            return testType;
        }

        public void setTestType(String testType) {
            this.testType = testType;
        }

        public int getThreadCount() {
            return threadCount;
        }

        public void setThreadCount(int threadCount) {
            this.threadCount = threadCount;
        }

        public int getSendIntervalMs() {
            return sendIntervalMs;
        }

        public void setSendIntervalMs(int sendIntervalMs) {
            this.sendIntervalMs = sendIntervalMs;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        @Override
        public String toString() {
            return String.format("StressTestResult{totalMessages=%d, successCount=%d, failedCount=%d, " +
                            "totalTimeMs=%d, throughputPerSecond=%.2f, avgLatencyMs=%.2f, " +
                            "p50LatencyMs=%d, p95LatencyMs=%d, p99LatencyMs=%d, testType='%s'}",
                    totalMessages, successCount, failedCount, totalTimeMs, throughputPerSecond,
                    avgLatencyMs, p50LatencyMs, p95LatencyMs, p99LatencyMs, testType);
        }
    }

}