package com.sdses.ai.imagetransfer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName(value = "event_processing_record")
public class EventRecord {
    @TableId(value = "eventId", type = IdType.ASSIGN_ID)
    private String eventId;

    private String cameraIndexCode;

    private String imageUrl;

    private Integer retryCount = 0;

    private String status; // PROCESSING, SUCCESS, FAILED, MANUAL

    private String errorMessage;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Boolean manualProcessingRequired = false;
}
