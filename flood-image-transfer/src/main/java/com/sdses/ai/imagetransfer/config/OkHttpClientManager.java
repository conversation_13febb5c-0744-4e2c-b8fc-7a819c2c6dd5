package com.sdses.ai.imagetransfer.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import javax.net.ssl.*;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp客户端管理器
 * 创建和管理优化的OkHttpClient实例
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class OkHttpClientManager {

    private final OkHttpConfig okHttpConfig;
    private OkHttpClient okHttpClient;

    @PostConstruct
    public void init() {
        // 验证配置
        okHttpConfig.validate();
        
        // 创建优化的OkHttpClient
        this.okHttpClient = createOptimizedOkHttpClient();
        
        log.info("OkHttp客户端管理器初始化完成");
        log.info(okHttpConfig.getConfigSummary());
    }

    /**
     * 创建优化的OkHttpClient Bean
     */
    @Bean
    public OkHttpClient okHttpClient() {
        return okHttpClient;
    }

    /**
     * 创建优化的OkHttpClient
     */
    private OkHttpClient createOptimizedOkHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();

        // 基本超时配置
        builder.connectTimeout(okHttpConfig.getConnectTimeout(), TimeUnit.SECONDS)
               .readTimeout(okHttpConfig.getReadTimeout(), TimeUnit.SECONDS)
               .writeTimeout(okHttpConfig.getWriteTimeout(), TimeUnit.SECONDS)
               .callTimeout(okHttpConfig.getCallTimeout(), TimeUnit.SECONDS);

        // 连接池配置
        ConnectionPool connectionPool = new ConnectionPool(
            okHttpConfig.getConnectionPool().getMaxIdleConnections(),
            okHttpConfig.getKeepAliveDurationMillis(),
            TimeUnit.MILLISECONDS
        );
        builder.connectionPool(connectionPool);

        // 并发控制
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(okHttpConfig.getConnectionPool().getMaxRequests());
        dispatcher.setMaxRequestsPerHost(okHttpConfig.getConnectionPool().getMaxRequestsPerHost());
        builder.dispatcher(dispatcher);

        // 重试配置
        builder.retryOnConnectionFailure(okHttpConfig.getRetry().isRetryOnConnectionFailure());

        // 性能优化
        if (okHttpConfig.getPerformance().isEnableHttp2()) {
            // HTTP/2 默认启用，无需额外配置
            log.debug("HTTP/2 支持已启用");
        }

        // SSL配置
        if (okHttpConfig.isTrustAllSsl()) {
            configureTrustAllSsl(builder);
            log.warn("已配置信任所有SSL证书，仅建议在开发环境使用");
        }

        // 添加性能监控拦截器
        if (okHttpConfig.getPerformance().isEnableMetrics()) {
            builder.addInterceptor(new PerformanceInterceptor());
        }

        // 添加重试拦截器
        builder.addInterceptor(new RetryInterceptor(okHttpConfig.getRetry()));

        // 添加请求大小限制拦截器
        builder.addInterceptor(new SizeLimitInterceptor(okHttpConfig.getPerformance().getMaxImageSize()));

        return builder.build();
    }

    /**
     * 配置信任所有SSL证书（仅用于开发环境）
     */
    private void configureTrustAllSsl(OkHttpClient.Builder builder) {
        try {
            final TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {}

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {}

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
            };

            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
            builder.hostnameVerifier((hostname, session) -> true);

        } catch (Exception e) {
            log.error("配置SSL信任失败", e);
            throw new RuntimeException("SSL配置失败", e);
        }
    }

    /**
     * 性能监控拦截器
     */
    private static class PerformanceInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws java.io.IOException {
            Request request = chain.request();
            long startTime = System.currentTimeMillis();

            try {
                Response response = chain.proceed(request);
                long duration = System.currentTimeMillis() - startTime;

                log.debug("HTTP请求完成: {} -> {} ({}ms, {}bytes)", 
                         request.url(), response.code(), duration, 
                         response.body() != null ? response.body().contentLength() : 0);

                return response;
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.warn("HTTP请求失败: {} ({}ms) - {}", request.url(), duration, e.getMessage());
                throw e;
            }
        }
    }

    /**
     * 重试拦截器
     */
    private static class RetryInterceptor implements Interceptor {
        private final OkHttpConfig.Retry retryConfig;

        public RetryInterceptor(OkHttpConfig.Retry retryConfig) {
            this.retryConfig = retryConfig;
        }

        @Override
        public Response intercept(Chain chain) throws java.io.IOException {
            Request request = chain.request();
            Response response = null;
            Exception lastException = null;

            for (int attempt = 0; attempt <= retryConfig.getMaxRetries(); attempt++) {
                try {
                    if (attempt > 0) {
                        log.debug("重试请求 {}/{}: {}", attempt, retryConfig.getMaxRetries(), request.url());
                        try {
                            Thread.sleep(retryConfig.getRetryInterval());
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new java.io.IOException("重试被中断", ie);
                        }
                    }

                    response = chain.proceed(request);
                    
                    if (response.isSuccessful()) {
                        return response;
                    } else if (attempt == retryConfig.getMaxRetries()) {
                        return response; // 最后一次尝试，返回失败响应
                    } else {
                        response.close(); // 关闭失败的响应
                    }

                } catch (Exception e) {
                    lastException = e;
                    if (attempt == retryConfig.getMaxRetries()) {
                        throw new java.io.IOException("请求失败，已重试" + retryConfig.getMaxRetries() + "次", e);
                    }
                }
            }

            if (lastException != null) {
                throw new java.io.IOException("请求重试失败", lastException);
            }

            return response;
        }
    }

    /**
     * 请求大小限制拦截器
     */
    private static class SizeLimitInterceptor implements Interceptor {
        private final long maxSize;

        public SizeLimitInterceptor(long maxSize) {
            this.maxSize = maxSize;
        }

        @Override
        public Response intercept(Chain chain) throws java.io.IOException {
            Response response = chain.proceed(chain.request());
            
            if (response.body() != null) {
                long contentLength = response.body().contentLength();
                if (contentLength > maxSize) {
                    response.close();
                    throw new java.io.IOException(
                        String.format("响应内容过大: %d bytes > %d bytes", contentLength, maxSize));
                }
            }
            
            return response;
        }
    }

    /**
     * 获取连接池状态
     */
    public String getConnectionPoolStatus() {
        if (okHttpClient != null && okHttpClient.connectionPool() != null) {
            ConnectionPool pool = okHttpClient.connectionPool();
            return String.format("连接池状态: 空闲连接=%d, 总连接=%d", 
                               pool.idleConnectionCount(), pool.connectionCount());
        }
        return "连接池状态: 未初始化";
    }

    /**
     * 清理资源
     */
    @PreDestroy
    public void cleanup() {
        if (okHttpClient != null) {
            log.info("开始清理OkHttp客户端资源");
            
            // 关闭连接池
            if (okHttpClient.connectionPool() != null) {
                okHttpClient.connectionPool().evictAll();
            }
            
            // 关闭调度器
            if (okHttpClient.dispatcher() != null) {
                okHttpClient.dispatcher().executorService().shutdown();
            }
            
            log.info("OkHttp客户端资源清理完成");
        }
    }
}
