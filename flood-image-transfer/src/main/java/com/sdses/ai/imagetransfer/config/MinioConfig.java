package com.sdses.ai.imagetransfer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {
    private String endpoint;
    private String accessKey;
    private String secretKey;
    private String bucketName;

    // 连接池配置
    private ConnectionPool connectionPool = new ConnectionPool();

    // Getters and Setters
    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public ConnectionPool getConnectionPool() {
        return connectionPool;
    }

    public void setConnectionPool(ConnectionPool connectionPool) {
        this.connectionPool = connectionPool;
    }

    /**
     * MinIO连接池配置类
     */
    public static class ConnectionPool {
        private boolean enabled = true;                    // 是否启用连接池
        private int maxConnections = 100;                 // 最大连接数
        private int maxConnectionsPerRoute = 50;          // 每个路由的最大连接数
        private int connectionTimeout = 10000;            // 连接超时时间(ms)
        private int socketTimeout = 30000;                // Socket超时时间(ms)
        private int connectionRequestTimeout = 5000;      // 连接请求超时时间(ms)
        private long keepAliveTime = 60000;              // 连接保活时间(ms)
        private int maxIdleTime = 300000;                // 最大空闲时间(ms)
        private boolean validateAfterInactivity = true;   // 空闲后验证连接
        private int retryCount = 3;                       // 重试次数

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public int getMaxConnections() { return maxConnections; }
        public void setMaxConnections(int maxConnections) { this.maxConnections = maxConnections; }

        public int getMaxConnectionsPerRoute() { return maxConnectionsPerRoute; }
        public void setMaxConnectionsPerRoute(int maxConnectionsPerRoute) { this.maxConnectionsPerRoute = maxConnectionsPerRoute; }

        public int getConnectionTimeout() { return connectionTimeout; }
        public void setConnectionTimeout(int connectionTimeout) { this.connectionTimeout = connectionTimeout; }

        public int getSocketTimeout() { return socketTimeout; }
        public void setSocketTimeout(int socketTimeout) { this.socketTimeout = socketTimeout; }

        public int getConnectionRequestTimeout() { return connectionRequestTimeout; }
        public void setConnectionRequestTimeout(int connectionRequestTimeout) { this.connectionRequestTimeout = connectionRequestTimeout; }

        public long getKeepAliveTime() { return keepAliveTime; }
        public void setKeepAliveTime(long keepAliveTime) { this.keepAliveTime = keepAliveTime; }

        public int getMaxIdleTime() { return maxIdleTime; }
        public void setMaxIdleTime(int maxIdleTime) { this.maxIdleTime = maxIdleTime; }

        public boolean isValidateAfterInactivity() { return validateAfterInactivity; }
        public void setValidateAfterInactivity(boolean validateAfterInactivity) { this.validateAfterInactivity = validateAfterInactivity; }

        public int getRetryCount() { return retryCount; }
        public void setRetryCount(int retryCount) { this.retryCount = retryCount; }
    }
}
