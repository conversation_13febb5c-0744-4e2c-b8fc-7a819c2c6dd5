package com.sdses.ai.imagetransfer.service.minio;

import com.sdses.ai.imagetransfer.config.MinioConnectionPoolManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicLong;

/**
 * MinIO连接池监控服务
 * 监控连接池状态和性能指标
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MinioConnectionPoolMonitor implements HealthIndicator {

    private final MinioConnectionPoolManager connectionPoolManager;

    // 监控指标
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong totalUploadTime = new AtomicLong(0);

    /**
     * 记录上传请求
     */
    public void recordUploadRequest(boolean success, long uploadTimeMs) {
        totalRequests.incrementAndGet();
        totalUploadTime.addAndGet(uploadTimeMs);
        
        if (success) {
            successfulRequests.incrementAndGet();
        } else {
            failedRequests.incrementAndGet();
        }
        
        log.debug("MinIO上传请求记录: 成功={}, 耗时={}ms", success, uploadTimeMs);
    }

    /**
     * 获取连接池性能统计
     */
    public String getPerformanceStats() {
        long total = totalRequests.get();
        long successful = successfulRequests.get();
        long failed = failedRequests.get();
        long avgTime = total > 0 ? totalUploadTime.get() / total : 0;
        double successRate = total > 0 ? (successful * 100.0 / total) : 0;

        return String.format(
                "MinIO连接池性能统计 - 总请求: %d, 成功: %d, 失败: %d, 成功率: %.2f%%, 平均耗时: %dms",
                total, successful, failed, successRate, avgTime
        );
    }

    /**
     * 获取连接池状态
     */
    public String getConnectionPoolStatus() {
        return connectionPoolManager.getConnectionPoolStats();
    }

    /**
     * 获取连接池配置
     */
    public String getConnectionPoolConfig() {
        return connectionPoolManager.getConnectionPoolConfig();
    }

    /**
     * 重置统计数据
     */
    public void resetStats() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalUploadTime.set(0);
        log.info("MinIO连接池统计数据已重置");
    }

    /**
     * 定期监控连接池状态
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void monitorConnectionPool() {
        try {
            String stats = getConnectionPoolStatus();
            String performance = getPerformanceStats();
            
            log.info("MinIO连接池监控 - {}", stats);
            log.info("MinIO性能监控 - {}", performance);
            
            // 检查连接池健康状态
            if (!connectionPoolManager.isConnectionPoolHealthy()) {
                log.warn("MinIO连接池健康检查失败，可能需要清理空闲连接");
                connectionPoolManager.evictIdleConnections();
            }
            
        } catch (Exception e) {
            log.error("MinIO连接池监控异常", e);
        }
    }

    /**
     * 定期清理空闲连接
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void cleanupIdleConnections() {
        try {
            log.debug("开始清理MinIO连接池空闲连接");
            connectionPoolManager.evictIdleConnections();
        } catch (Exception e) {
            log.warn("MinIO连接池空闲连接清理失败", e);
        }
    }

    /**
     * Spring Boot Actuator健康检查
     */
    @Override
    public Health health() {
        try {
            boolean healthy = connectionPoolManager.isConnectionPoolHealthy();
            
            Health.Builder builder = healthy ? Health.up() : Health.down();
            
            // 添加详细信息
            long total = totalRequests.get();
            long successful = successfulRequests.get();
            long failed = failedRequests.get();
            double successRate = total > 0 ? (successful * 100.0 / total) : 0;
            
            builder.withDetail("connectionPool", connectionPoolManager.getConnectionPoolStats())
                   .withDetail("totalRequests", total)
                   .withDetail("successfulRequests", successful)
                   .withDetail("failedRequests", failed)
                   .withDetail("successRate", String.format("%.2f%%", successRate))
                   .withDetail("config", connectionPoolManager.getConnectionPoolConfig());
            
            return builder.build();
            
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }

    /**
     * 获取详细的监控报告
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== MinIO连接池详细报告 ===\n");
        report.append("配置信息: ").append(getConnectionPoolConfig()).append("\n");
        report.append("连接池状态: ").append(getConnectionPoolStatus()).append("\n");
        report.append("性能统计: ").append(getPerformanceStats()).append("\n");
        report.append("健康状态: ").append(connectionPoolManager.isConnectionPoolHealthy() ? "健康" : "异常").append("\n");
        report.append("报告时间: ").append(java.time.LocalDateTime.now()).append("\n");
        return report.toString();
    }

    /**
     * 获取简化的状态信息
     */
    public String getSimpleStatus() {
        long total = totalRequests.get();
        long successful = successfulRequests.get();
        double successRate = total > 0 ? (successful * 100.0 / total) : 0;
        
        return String.format("MinIO连接池 - 请求: %d, 成功率: %.1f%%, 状态: %s",
                total, successRate, 
                connectionPoolManager.isConnectionPoolHealthy() ? "健康" : "异常");
    }
}
