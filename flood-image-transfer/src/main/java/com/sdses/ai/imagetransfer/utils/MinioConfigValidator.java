package com.sdses.ai.imagetransfer.utils;

import com.sdses.ai.imagetransfer.config.MinioConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * MinIO配置验证工具
 * <AUTHOR>
 * @create 2025-07-04
 */
@Slf4j
@Component
public class MinioConfigValidator {

    @Autowired
    private MinioConfig minioConfig;

    @PostConstruct
    public void validateConfig() {
        log.info("开始验证MinIO配置...");
        
        boolean isValid = true;
        StringBuilder errors = new StringBuilder();
        
        // 验证端点
        if (minioConfig.getEndpoint() == null || minioConfig.getEndpoint().trim().isEmpty()) {
            errors.append("MinIO端点不能为空; ");
            isValid = false;
        } else {
            log.info("MinIO端点: {}", minioConfig.getEndpoint());
        }
        
        // 验证访问密钥
        if (minioConfig.getAccessKey() == null || minioConfig.getAccessKey().trim().isEmpty()) {
            errors.append("MinIO访问密钥不能为空; ");
            isValid = false;
        } else {
            log.info("MinIO访问密钥: {}***", minioConfig.getAccessKey().substring(0, Math.min(4, minioConfig.getAccessKey().length())));
        }
        
        // 验证秘密密钥
        if (minioConfig.getSecretKey() == null || minioConfig.getSecretKey().trim().isEmpty()) {
            errors.append("MinIO秘密密钥不能为空; ");
            isValid = false;
        } else {
            log.info("MinIO秘密密钥: {}***", minioConfig.getSecretKey().substring(0, Math.min(4, minioConfig.getSecretKey().length())));
        }
        
        // 验证桶名
        if (minioConfig.getBucketName() == null || minioConfig.getBucketName().trim().isEmpty()) {
            errors.append("MinIO桶名不能为空; ");
            isValid = false;
        } else {
            log.info("MinIO桶名: {}", minioConfig.getBucketName());
        }
        
        if (isValid) {
            log.info("MinIO配置验证通过");
        } else {
            log.error("MinIO配置验证失败: {}", errors.toString());
            throw new RuntimeException("MinIO配置验证失败: " + errors.toString());
        }
    }
    
    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format("MinIO配置 - 端点: %s, 桶名: %s", 
                minioConfig.getEndpoint(), 
                minioConfig.getBucketName());
    }
}
