package com.sdses.ai.imagetransfer.utils;

import cn.hutool.core.util.StrUtil;
import com.sdses.ai.imagetransfer.common.constants.BaseConstant;
import com.sdses.ai.imagetransfer.config.MinioConfig;
import com.sdses.ai.imagetransfer.service.minio.MinioConnectionPoolMonitor;
import io.minio.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MinioStreamUtil {
    private final MinioClient minioClient;
    private final MinioConfig minioConfig;
    private final MinioConnectionPoolMonitor connectionPoolMonitor;

    @Autowired
    public MinioStreamUtil(MinioClient minioClient, MinioConfig minioConfig,
                          MinioConnectionPoolMonitor connectionPoolMonitor) throws Exception {
        this.minioClient = minioClient;
        this.minioConfig = minioConfig;
        this.connectionPoolMonitor = connectionPoolMonitor;

        log.info("初始化MinIO工具类，端点: {}, 桶名: {}, 连接池: {}",
                minioConfig.getEndpoint(),
                minioConfig.getBucketName(),
                minioConfig.getConnectionPool().isEnabled() ? "启用" : "禁用");

        try {
            initializeBucket();
            log.info("MinIO工具类初始化成功");
        } catch (Exception e) {
            log.error("MinIO工具类初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("MinIO工具类初始化失败", e);
        }
    }

    private void initializeBucket() throws Exception {
        try {
            String bucketName = minioConfig.getBucketName();
            log.info("检查存储桶是否存在: {}", bucketName);

            boolean found = minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build());

            if (!found) {
                log.info("存储桶不存在，正在创建: {}", bucketName);
                minioClient.makeBucket(MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build());
                log.info("存储桶创建成功: {}", bucketName);
            } else {
                log.info("存储桶已存在: {}", bucketName);
            }

        } catch (Exception e) {
            log.error("初始化存储桶失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化存储桶失败", e);
        }
    }

    /**
     * 生成MinIO存储路径
     *
     * @param cameraCode   摄像头编码
     * @param eventTime    事件时间
     * @param sourceModule
     * @return 完整存储路径
     */
    public String generatePath(String cameraCode, LocalDateTime eventTime, String sourceModule) {
        DateTimeFormatter dirFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        DateTimeFormatter fileFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        
        String dirTime = eventTime.format(dirFormatter);
        String imgTime = eventTime.format(fileFormatter);

        // 判断是否是蒙版图 如果是 在上传路径上追加指定后缀
        if (StrUtil.isNotEmpty(sourceModule) && StrUtil.equals(sourceModule, BaseConstant.SOURCE_MODULE_MASK)) {
            log.info("是蒙版图，追加后缀: {}", BaseConstant.SUFFIX_MASK);
            imgTime = imgTime + BaseConstant.SUFFIX_MASK;
        }

        String outputDir = String.format("images/%s/%s", dirTime, cameraCode);
        String outputFilename = String.format("%s_%s.jpg", cameraCode, imgTime);
        
        return outputDir + "/" + outputFilename;
    }

    /**
     * 流式上传文件到MinIO
     * @param inputStream 输入流
     * @param objectName 存储路径
     * @param contentType 文件类型
     * @param size 文件大小(未知大小传-1)
     * @return 文件访问URL
     */
    public String uploadStream(InputStream inputStream, String objectName,
                             String contentType, long size) throws Exception {

        if (inputStream == null) {
            throw new IllegalArgumentException("输入流不能为空");
        }
        if (objectName == null || objectName.trim().isEmpty()) {
            throw new IllegalArgumentException("对象名不能为空");
        }
        if (contentType == null || contentType.trim().isEmpty()) {
            contentType = "application/octet-stream"; // 默认类型
        }

        try {
            log.info("开始上传文件到MinIO，对象名: {}, 内容类型: {}, 大小: {}",
                    objectName, contentType, size == -1 ? "未知" : size + " bytes");

            // 处理未知大小的情况
            PutObjectArgs.Builder argsBuilder = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .contentType(contentType);

            if (size == -1) {
                // 未知大小，使用默认的分片大小
                argsBuilder.stream(inputStream, -1, 10485760); // 10MB 分片
            } else {
                // 已知大小
                argsBuilder.stream(inputStream, size, -1);
            }

            minioClient.putObject(argsBuilder.build());

            log.info("文件上传成功: {}", objectName);
            return getFileUrl(objectName);

        } catch (Exception e) {
            log.error("文件上传失败，对象名: {}, 错误: {}", objectName, e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 流式上传并自动生成路径
     *
     * @param inputStream  输入流
     * @param cameraCode   摄像头编码
     * @param eventTime    事件时间
     * @param contentType  文件类型
     * @param size         文件大小(未知传-1)
     * @param sourceModule
     * @return MinIO永久图片地址
     */
    public String uploadStreamWithPath(InputStream inputStream, String cameraCode,
                                       LocalDateTime eventTime, String contentType,
                                       long size, String sourceModule) throws Exception {

        if (cameraCode == null || cameraCode.trim().isEmpty()) {
            throw new IllegalArgumentException("摄像头编码不能为空");
        }
        if (eventTime == null) {
            throw new IllegalArgumentException("事件时间不能为空");
        }

        long startTime = System.currentTimeMillis();
        boolean success = false;

        try {
            String objectName = generatePath(cameraCode, eventTime, sourceModule);

            log.debug("MinIO上传开始，路径: {}, 连接池: {}", objectName,
                     minioConfig.getConnectionPool().isEnabled() ? "启用" : "禁用");

            // 上传文件
            uploadStreamOnly(inputStream, objectName, contentType, size);

            // 返回永久图片地址
            String permanentUrl = getPermanentFileUrl(objectName);

            success = true;
            long uploadTime = System.currentTimeMillis() - startTime;
            log.debug("MinIO上传成功，耗时: {}ms, 地址: {}", uploadTime, permanentUrl);

            // 记录监控指标
            connectionPoolMonitor.recordUploadRequest(true, uploadTime);

            return permanentUrl;

        } catch (Exception e) {
            long uploadTime = System.currentTimeMillis() - startTime;
            log.error("MinIO上传失败，摄像头: {}, 时间: {}, 耗时: {}ms, 错误: {}",
                     cameraCode, eventTime, uploadTime, e.getMessage(), e);

            // 记录监控指标
            connectionPoolMonitor.recordUploadRequest(false, uploadTime);

            throw e;
        }
    }

    /**
     * 仅上传文件到MinIO，不返回URL
     * @param inputStream 输入流
     * @param objectName 存储路径
     * @param contentType 文件类型
     * @param size 文件大小(未知大小传-1)
     */
    private void uploadStreamOnly(InputStream inputStream, String objectName,
                                String contentType, long size) throws Exception {

        if (inputStream == null) {
            throw new IllegalArgumentException("输入流不能为空");
        }
        if (objectName == null || objectName.trim().isEmpty()) {
            throw new IllegalArgumentException("对象名不能为空");
        }
        if (contentType == null || contentType.trim().isEmpty()) {
            contentType = "application/octet-stream"; // 默认类型
        }

        try {
            log.info("开始上传文件到MinIO，对象名: {}, 内容类型: {}, 大小: {}",
                    objectName, contentType, size == -1 ? "未知" : size + " bytes");

            // 处理未知大小的情况
            PutObjectArgs.Builder argsBuilder = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .contentType(contentType);

            if (size == -1) {
                // 未知大小，使用默认的分片大小
                argsBuilder.stream(inputStream, -1, 10485760); // 10MB 分片
            } else {
                // 已知大小
                argsBuilder.stream(inputStream, size, -1);
            }

            minioClient.putObject(argsBuilder.build());
            log.info("文件上传成功: {}", objectName);

        } catch (Exception e) {
            log.error("文件上传失败，对象名: {}, 错误: {}", objectName, e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取MinIO永久图片地址
     * @param objectName 文件路径
     * @return 永久访问URL，格式如: http://192.168.102.72:39000/workflow-test/images/20250707/5e18d86759f84324a8763fabf62e4fc8/5e18d86759f84324a8763fabf62e4fc8_20250707085609.jpg
     */
    public String getPermanentFileUrl(String objectName) {
        if (objectName == null || objectName.trim().isEmpty()) {
            throw new IllegalArgumentException("对象名不能为空");
        }

        try {
            // 构建永久URL: endpoint/bucketName/objectName
            String endpoint = minioConfig.getEndpoint();
            String bucketName = minioConfig.getBucketName();

            // 确保endpoint不以/结尾
            if (endpoint.endsWith("/")) {
                endpoint = endpoint.substring(0, endpoint.length() - 1);
            }

            // 确保objectName不以/开头
            if (objectName.startsWith("/")) {
                objectName = objectName.substring(1);
            }

            String permanentUrl = String.format("%s/%s/%s", endpoint, bucketName, objectName);
            log.debug("生成永久图片地址: {}", permanentUrl);
            return permanentUrl;

        } catch (Exception e) {
            log.error("生成永久图片地址失败，对象名: {}, 错误: {}", objectName, e.getMessage(), e);
            throw new RuntimeException("生成永久图片地址失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件临时访问URL
     * @param objectName 文件路径
     * @param expiry 有效期
     * @param unit 时间单位
     * @return 访问URL
     */
    public String getFileUrl(String objectName, int expiry, TimeUnit unit) throws Exception {
        if (objectName == null || objectName.trim().isEmpty()) {
            throw new IllegalArgumentException("对象名不能为空");
        }

        try {
            log.debug("生成文件访问URL，对象名: {}, 有效期: {} {}", objectName, expiry, unit);

            String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .expiry(expiry, unit)
                    .build());

            log.debug("文件访问URL生成成功: {}", url);
            return url;

        } catch (Exception e) {
            log.error("生成文件访问URL失败，对象名: {}, 错误: {}", objectName, e.getMessage(), e);
            throw new RuntimeException("生成文件访问URL失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件临时访问URL(默认7天)
     */
    public String getFileUrl(String objectName) throws Exception {
        return getFileUrl(objectName, 7, TimeUnit.DAYS);
    }

    /**
     * 检查MinIO连接状态
     * @return 连接是否正常
     */
    public boolean checkConnection() {
        try {
            minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .build());
            log.info("MinIO连接检查成功");
            return true;
        } catch (Exception e) {
            log.error("MinIO连接检查失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
