package com.sdses.ai.imagetransfer.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis Plus 配置类
 * 配置分页插件等功能
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-15
 */
@Slf4j
@Configuration
public class MybatisPlusConfig {

    /**
     * 配置MyBatis Plus拦截器
     * 主要用于分页功能
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        log.info("配置MyBatis Plus拦截器，启用分页功能");
        
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 添加分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
        
        // 设置数据库类型为PostgreSQL（主数据源）
        paginationInterceptor.setDbType(DbType.POSTGRE_SQL);
        
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInterceptor.setMaxLimit(1000L);
        
        // 溢出总页数后是否进行处理
        paginationInterceptor.setOverflow(false);
        
        // 生成 count 查询的优化开关
        paginationInterceptor.setOptimizeJoin(true);
        
        interceptor.addInnerInterceptor(paginationInterceptor);
        
        log.info("MyBatis Plus分页插件配置完成，最大单页限制: 1000条");
        
        return interceptor;
    }
}
