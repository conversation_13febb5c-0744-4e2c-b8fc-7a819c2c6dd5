package com.sdses.ai.imagetransfer.common.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 查询结果列表DTO
 * 支持分页、内码外码查询、时间范围查询
 *
 * <AUTHOR>
 * @Description
 * @create 2025-07-15 10:04
 */
@Data
public class QueryResultListDTO {

    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    @Max(value = 10000, message = "页码不能超过10000")
    private Integer page;

    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小必须大于0")
    @Max(value = 1000, message = "页大小不能超过1000")
    private Integer pageSize;

    /**
     * 摄像头外码（支持模糊匹配）
     */
    @Size(max = 100, message = "外码长度不能超过100字符")
    private String cameraForeignCode;

    /**
     * 摄像头内码（支持模糊匹配）
     */
    @Size(max = 100, message = "内码长度不能超过100字符")
    private String cameraIndexCode;

    /**
     * 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String startTime;

    /**
     * 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String endTime;

    private String orderBy;

    /**
     * 时间格式常量
     */
    public static final String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(TIME_FORMAT);

    /**
     * 验证时间格式和逻辑
     *
     * @throws IllegalArgumentException 时间格式或逻辑错误时抛出异常
     */
    public void validateTimeParameters() {
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        // 验证开始时间格式
        if (startTime != null && !startTime.trim().isEmpty()) {
            try {
                startDateTime = LocalDateTime.parse(startTime.trim(), TIME_FORMATTER);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("开始时间格式错误，请使用 " + TIME_FORMAT + " 格式");
            }
        }

        // 验证结束时间格式
        if (endTime != null && !endTime.trim().isEmpty()) {
            try {
                endDateTime = LocalDateTime.parse(endTime.trim(), TIME_FORMATTER);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("结束时间格式错误，请使用 " + TIME_FORMAT + " 格式");
            }
        }

        // 验证时间逻辑：开始时间必须早于结束时间
        if (startDateTime != null && endDateTime != null) {
            if (!startDateTime.isBefore(endDateTime)) {
                throw new IllegalArgumentException("开始时间必须早于结束时间");
            }
        }
    }

    /**
     * 获取解析后的开始时间
     *
     * @return LocalDateTime 或 null
     */
    public LocalDateTime getParsedStartTime() {
        if (startTime == null || startTime.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(startTime.trim(), TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * 获取解析后的结束时间
     *
     * @return LocalDateTime 或 null
     */
    public LocalDateTime getParsedEndTime() {
        if (endTime == null || endTime.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(endTime.trim(), TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * 检查是否有时间查询条件
     *
     * @return true 如果有时间条件
     */
    public boolean hasTimeCondition() {
        return (startTime != null && !startTime.trim().isEmpty()) ||
                (endTime != null && !endTime.trim().isEmpty());
    }

    @Override
    public String toString() {
        return "QueryResultListDTO{" +
                "page=" + page +
                ", pageSize=" + pageSize +
                ", cameraForeignCode='" + cameraForeignCode + '\'' +
                ", cameraIndexCode='" + cameraIndexCode + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                '}';
    }
}
