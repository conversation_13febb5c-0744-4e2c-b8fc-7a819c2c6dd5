package com.sdses.ai.imagetransfer.controller;

import com.sdses.ai.imagetransfer.model.ResVoT;
import com.sdses.ai.imagetransfer.monitor.TokenBucketMonitor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 监控数据接口
 * 提供令牌桶监控数据的HTTP访问接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/api/monitor")
@Tag(name = "监控接口", description = "令牌桶监控数据接口")
public class MonitorController {

    @Resource
    private TokenBucketMonitor tokenBucketMonitor;

    /**
     * 获取监控报告
     */
    @GetMapping("/report")
    @Operation(summary = "获取令牌桶监控报告", description = "获取所有令牌桶的详细监控报告")
    public ResVoT<String> getMonitorReport() {
        try {
            String report = tokenBucketMonitor.generateMonitorReport();
            return ResVoT.success(report);
        } catch (Exception e) {
            log.error("获取监控报告失败", e);
            return ResVoT.error("获取监控报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控统计数据
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取监控统计数据", description = "获取令牌桶的统计数据")
    public ResVoT<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalAcquisitions", tokenBucketMonitor.getTotalAcquisitions());
            statistics.put("totalFailures", tokenBucketMonitor.getTotalFailures());
            statistics.put("totalNegativeResets", tokenBucketMonitor.getTotalNegativeResets());
            statistics.put("monitoredBucketCount", tokenBucketMonitor.getMonitoredBucketCount());
            statistics.put("overallFailureRate", tokenBucketMonitor.getOverallFailureRate());
            
            return ResVoT.success(statistics);
        } catch (Exception e) {
            log.error("获取监控统计数据失败", e);
            return ResVoT.error("获取监控统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定令牌桶详情
     */
    @GetMapping("/bucket/{bucketKey}")
    @Operation(summary = "获取指定令牌桶详情", description = "获取指定令牌桶的详细监控信息")
    public ResVoT<String> getBucketDetails(
            @Parameter(description = "令牌桶键名", required = true)
            @PathVariable String bucketKey) {
        try {
            String details = tokenBucketMonitor.getBucketDetails(bucketKey);
            return ResVoT.success(details);
        } catch (Exception e) {
            log.error("获取令牌桶 {} 详情失败", bucketKey, e);
            return ResVoT.error("获取令牌桶详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取热点令牌桶
     */
    @GetMapping("/hotspots")
    @Operation(summary = "获取热点令牌桶", description = "获取当前的热点令牌桶列表")
    public ResVoT<String> getHotspotBuckets() {
        try {
            String hotspots = tokenBucketMonitor.getHotspotBuckets();
            return ResVoT.success(hotspots);
        } catch (Exception e) {
            log.error("获取热点令牌桶失败", e);
            return ResVoT.error("获取热点令牌桶失败: " + e.getMessage());
        }
    }

    /**
     * 重置监控统计数据
     */
    @PostMapping("/reset")
    @Operation(summary = "重置监控统计数据", description = "重置所有监控统计数据")
    public ResVoT<String> resetStatistics() {
        try {
            tokenBucketMonitor.resetStatistics();
            return ResVoT.success("监控统计数据已重置");
        } catch (Exception e) {
            log.error("重置监控统计数据失败", e);
            return ResVoT.error("重置监控统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "监控系统健康检查", description = "检查监控系统的健康状态")
    public ResVoT<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("monitoredBuckets", tokenBucketMonitor.getMonitoredBucketCount());
            health.put("totalAcquisitions", tokenBucketMonitor.getTotalAcquisitions());
            health.put("overallFailureRate", tokenBucketMonitor.getOverallFailureRate());
            health.put("timestamp", System.currentTimeMillis());
            
            return ResVoT.success(health);
        } catch (Exception e) {
            log.error("监控系统健康检查失败", e);
            Map<String, Object> health = new HashMap<>();
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            return ResVoT.error(health);
        }
    }
}
