package com.sdses.ai.imagetransfer.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sdses.ai.imagetransfer.common.dto.QueryResultListDTO;
import com.sdses.ai.imagetransfer.common.vo.UploadVO;
import com.sdses.ai.imagetransfer.entity.CommonEventResourceLast;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.model.ResVoT;
import com.sdses.ai.imagetransfer.service.ApiService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-07-14 13:48
 */
@Slf4j
@RestController
@RequestMapping("/api/openAPI")
@Validated
public class ApiController {

    @Resource
    private ApiService apiService;

    /**
     * 同步上传(十条) 返回结果
     *
     * @param messageList
     * @return
     */
    @PostMapping("/sync/upload")
    public ResVoT<List<UploadVO>> syncUpload(@RequestBody List<EventMessage> messageList) {
        List<UploadVO> voList = apiService.syncUpload(messageList);
        return ResVoT.success("success", voList);
    }

    /**
     * 异步上传(1000条) 返回任务id
     *
     * @param messageList
     * @return
     */
    @PostMapping("/async/upload")
    public ResVoT<String> asyncUpload(@RequestBody List<EventMessage> messageList) {
        String taskId = apiService.asyncUpload(messageList);
        return ResVoT.success("success", taskId);
    }


    /**
     * 异步任务状态查询接口
     *
     * @param taskId
     * @return
     */
    @GetMapping("/async/status/{taskId}")
    public ResVoT<List<UploadVO>> asyncStatus(@PathVariable String taskId) {
        List<UploadVO> voList = apiService.asyncStatus(taskId);
        return ResVoT.success("success", voList);
    }

    /**
     * 根据eventId查询结果(Doris)
     * @param eventId
     * @return
     */
    @GetMapping("/queryResult/{eventId}")
    public ResVoT<CommonEventResourceLast> queryResult(@PathVariable String eventId) {
        CommonEventResourceLast info = apiService.queryResult(eventId);

        return ResVoT.success("success", info);
    }

    /**
     * 根据内码、外码、时间范围分页查询结果集合(Doris)
     * @return 分页查询结果
     */
    @GetMapping("/queryResultList")
    public ResVoT<IPage<CommonEventResourceLast>> queryResultList(@Valid QueryResultListDTO dto) {

        try {
            log.info("接收分页查询请求 - {}", JSONUtil.toJsonStr(dto));

            // 验证时间参数
            try {
                dto.validateTimeParameters();
            } catch (IllegalArgumentException e) {
                log.warn("时间参数验证失败: {}", e.getMessage());
                return ResVoT.error(e.getMessage());
            }

            // 执行查询
            IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

            log.info("分页查询完成 - 总记录数: {}, 总页数: {}, 当前页记录数: {}",
                    result.getTotal(), result.getPages(), result.getRecords().size());

            return ResVoT.success("查询成功", result);

        } catch (IllegalArgumentException e) {
            log.warn("参数验证失败: {}", e.getMessage());
            return ResVoT.error(e.getMessage());
        } catch (Exception e) {
            log.error("分页查询失败", e);
            return ResVoT.error("查询失败: " + e.getMessage());
        }
    }

}
