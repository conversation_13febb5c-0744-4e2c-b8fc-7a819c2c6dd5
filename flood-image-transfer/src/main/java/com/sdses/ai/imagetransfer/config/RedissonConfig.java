package com.sdses.ai.imagetransfer.config;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson配置类
 * 用于分布式锁和其他分布式功能
 * 适配Spring Boot 3.5.0 + JDK 21
 *
 * 注意：此配置类通过代码方式配置Redisson客户端，
 * 使用spring.data.redis.*配置项，无需额外的redisson.yml文件
 *
 * <AUTHOR>
 * @create 2025-07-07
 */
@Slf4j
@Configuration
public class RedissonConfig {

    @Value("${spring.data.redis.host:localhost}")
    private String host;

    @Value("${spring.data.redis.port:6379}")
    private String port;

    @Value("${spring.data.redis.password:}")
    private String password;

    @Value("${spring.data.redis.database:0}")
    private int database;

    /**
     * 创建Redisson客户端Bean
     * 使用spring.data.redis.*配置项进行配置
     *
     * @return RedissonClient实例
     */
    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient() {
        try {
            log.info("正在配置Redisson客户端 - Spring Boot 3.5.0 + JDK 21");
            log.info("Redis连接信息: host={}, port={}, database={}", host, port, database);

            Config config = new Config();

            // 单机模式配置
            String redisUrl = "redis://" + host + ":" + port;
            config.useSingleServer()
                  .setAddress(redisUrl)
                  .setDatabase(database)
                  .setConnectionPoolSize(64)
                  .setConnectionMinimumIdleSize(24)
                  .setIdleConnectionTimeout(10000)
                  .setConnectTimeout(3000)
                  .setTimeout(3000)
                  .setRetryAttempts(3)
                  .setRetryInterval(1500)
                  .setPingConnectionInterval(30000);

            // 如果有密码则设置密码
            if (password != null && !password.trim().isEmpty()) {
                config.useSingleServer().setPassword(password);
                log.info("Redis密码已配置");
            }

            // 设置编码 - 使用Jackson编码器
            config.setCodec(new org.redisson.codec.JsonJacksonCodec());

            // 设置线程池大小（适配JDK 21）
            config.setThreads(16);
            config.setNettyThreads(32);

            RedissonClient redissonClient = Redisson.create(config);
            log.info("Redisson客户端配置完成");

            return redissonClient;

        } catch (Exception e) {
            log.error("配置Redisson客户端失败", e);
            throw new RuntimeException("Redisson配置失败", e);
        }
    }
}
