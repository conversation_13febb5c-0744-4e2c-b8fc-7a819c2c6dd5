package com.sdses.ai.imagetransfer.consumer;

import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.service.async.AsyncMessageProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 重试事件消费者
 * 处理重试消息，支持异步处理模式
 *
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RetryEventConsumer {

    private final AsyncMessageProcessor asyncMessageProcessor;

    @Value("${kafka.consumer.retry.async-processing.enabled:true}")
    private boolean asyncProcessingEnabled;

    @Value("${kafka.consumer.retry.async-processing.timeout-seconds:120}")
    private long asyncTimeoutSeconds;

    /**
     * 重试消息监听器
     * 重试消息通常处理时间更长，适合使用异步处理
     */
    @KafkaListener(topics = "${spring.kafka.topics.retry-topic}",
            containerFactory = "retryKafkaListenerContainerFactory")
    public void listen(List<EventMessage> messages, Acknowledgment ack) {
        log.info("接收到 {} 条重试消息，异步处理模式: {}", messages.size(), asyncProcessingEnabled);

        try {
            processRetryMessagesAsync(messages, ack);
        } catch (Exception e) {
            log.error("处理重试消息批次失败，消息数量: {}", messages.size(), e);
            // 发生异常时仍然确认消息，避免重复消费
            ack.acknowledge();
        }
    }

    /**
     * 异步处理重试消息
     * 重试消息通常需要更长的处理时间，异步处理可以提高效率
     */
    private void processRetryMessagesAsync(List<EventMessage> messages, Acknowledgment ack) {
        try {
            CompletableFuture<AsyncMessageProcessor.ProcessingResult> future =
                    asyncMessageProcessor.processMessagesAsync(messages);

            // 重试消息设置更长的超时时间
            AsyncMessageProcessor.ProcessingResult result = future.get(asyncTimeoutSeconds, TimeUnit.SECONDS);

            log.info("重试消息异步处理完成: {}", result);

            // 异步处理完成后确认消息
            ack.acknowledge();

        } catch (Exception e) {
            log.error("异步处理重试消息失败", e);
            // 即使异步处理失败，也要确认消息避免重复消费
            ack.acknowledge();
        }
    }
}
