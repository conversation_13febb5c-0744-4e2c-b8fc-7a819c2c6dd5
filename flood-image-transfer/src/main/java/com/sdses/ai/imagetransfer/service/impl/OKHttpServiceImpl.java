package com.sdses.ai.imagetransfer.service.impl;

import com.sdses.ai.imagetransfer.service.OKHttpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * OkHttp图片下载服务 - 优化版本
 * 使用统一配置的OkHttpClient，提供高性能的图片下载服务
 *
 * <AUTHOR>
 * @Description 用于下载图片并保存到本地
 * @create 2025-07-04 11:16
 * @update 2025-07-10 性能优化
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OKHttpServiceImpl implements OKHttpService {

    private final OkHttpClient okHttpClient;

    // 构造函数注入已由@RequiredArgsConstructor自动生成

    // 资源清理由OkHttpClientManager统一管理，这里不需要重复清理

    /**
     * 下载图片并返回输入流（不保存到本地）
     *
     * @param imageUrl 图片URL
     * @return 图片输入流
     */
    @Override
    public InputStream downloadImageStream(String imageUrl) {
        try {
            log.info("开始下载图片流: {}", imageUrl);

            Request request = new Request.Builder()
                    .url(imageUrl)
                    .get()
                    .addHeader("User-Agent", "ImageDownloader/1.0")
                    .build();

            Response response = okHttpClient.newCall(request).execute();

            if (!response.isSuccessful()) {
                response.close();
                throw new IOException("下载失败，HTTP状态码: " + response.code());
            }

            ResponseBody body = response.body();
            if (body == null) {
                response.close();
                throw new IOException("响应体为空");
            }

            log.info("图片流下载成功: {}", imageUrl);
            return body.byteStream();

        } catch (Exception e) {
            log.error("下载图片流失败: {}", imageUrl, e);
            throw new RuntimeException("下载图片流失败: " + e.getMessage(), e);
        }
    }

    // OkHttpClient的创建和配置已移至OkHttpClientManager统一管理

    // SSL配置已移至OkHttpClientManager统一管理

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        try {
            String path = url.substring(url.lastIndexOf('/') + 1);
            if (path.contains("?")) {
                path = path.substring(0, path.indexOf('?'));
            }
            if (path.contains(".")) {
                return path;
            }
        } catch (Exception e) {
            log.debug("无法从URL提取文件名: {}", url);
        }
        return null;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName != null && fileName.contains(".")) {
            return fileName.substring(fileName.lastIndexOf('.'));
        }
        return ".jpg"; // 默认扩展名
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }

    /**
     * 删除本地文件
     */
    public boolean deleteLocalFile(String filePath) {
        try {
            return Files.deleteIfExists(Paths.get(filePath));
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            return false;
        }
    }
}
