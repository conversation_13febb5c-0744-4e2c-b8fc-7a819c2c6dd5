package com.sdses.ai.imagetransfer.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.ResourceMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JacksonConfig测试类
 * 验证JSON序列化时所有字段都包含，即使为null或空值
 * <AUTHOR>
 * @create 2025-07-07
 */
@Slf4j
@SpringBootTest
public class JacksonConfigTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testEventMessageWithNullFields() throws Exception {
        log.info("测试EventMessage包含null字段的序列化");
        
        // 创建只设置部分字段的EventMessage
        EventMessage message = new EventMessage();
        message.setEventId("test-event-001");
        message.setCameraIndexCode("camera-001");
        message.setEventType("flood_detection");
        // 其他字段保持null
        
        String json = objectMapper.writeValueAsString(message);
        log.info("EventMessage序列化结果: {}", json);
        
        // 验证所有@JsonProperty字段都存在
        String[] expectedFields = {
            "camera_index_code", "camera_channel_code", "camera_foreign_code",
            "event_time", "source_system", "source_module", "info",
            "event_type", "event_id", "image_url", "video_url",
            "image_base64", "only_picture"
        };
        
        for (String field : expectedFields) {
            assertTrue(json.contains("\"" + field + "\""), 
                      "JSON应该包含字段: " + field);
        }
        
        // 验证null字段的格式
        assertTrue(json.contains("\"camera_channel_code\":null") || 
                  json.contains("\"camera_channel_code\" : null"), 
                  "null字段应该显示为 field:null");
        
        // 验证非null字段的值
        assertTrue(json.contains("\"event_id\":\"test-event-001\""), 
                  "非null字段应该有正确的值");
    }

    @Test
    public void testResourceMessageWithNullFields() throws Exception {
        log.info("测试ResourceMessage包含null字段的序列化");
        
        // 创建只设置部分字段的ResourceMessage
        ResourceMessage message = ResourceMessage.builder()
                .eventId("test-resource-001")
                .cameraIndexCode("camera-001")
                .status("processing")
                .imageRetry(0)
                .videoRetry(0)
                .duration(1500L)
                // 其他字段保持null
                .build();
        
        String json = objectMapper.writeValueAsString(message);
        log.info("ResourceMessage序列化结果: {}", json);
        
        // 验证所有@JsonProperty字段都存在
        String[] expectedFields = {
            "camera_index_code", "camera_channel_code", "camera_foreign_code",
            "start_time", "end_time", "event_time", "event_id",
            "minio_video_url", "minio_image_url", "status",
            "image_retry", "video_retry", "image_msg", "video_msg",
            "duration", "source_system", "source_module", "info"
        };
        
        for (String field : expectedFields) {
            assertTrue(json.contains("\"" + field + "\""), 
                      "JSON应该包含字段: " + field);
        }
        
        // 验证null字段的格式
        assertTrue(json.contains("\"minio_image_url\":null") || 
                  json.contains("\"minio_image_url\" : null"), 
                  "null字段应该显示为 field:null");
        
        // 验证非null字段的值
        assertTrue(json.contains("\"status\":\"processing\""), 
                  "非null字段应该有正确的值");
        assertTrue(json.contains("\"duration\":1500"), 
                  "数字字段应该有正确的值");
    }

    @Test
    public void testEmptyStringFields() throws Exception {
        log.info("测试空字符串字段的序列化");
        
        EventMessage message = new EventMessage();
        message.setEventId("test-empty");
        message.setCameraIndexCode(""); // 空字符串
        message.setEventType(null); // null值
        
        String json = objectMapper.writeValueAsString(message);
        log.info("空字符串测试结果: {}", json);
        
        // 验证空字符串和null的区别
        assertTrue(json.contains("\"camera_index_code\":\"\""), 
                  "空字符串应该显示为空引号");
        assertTrue(json.contains("\"event_type\":null") || 
                  json.contains("\"event_type\" : null"), 
                  "null值应该显示为null");
    }

    @Test
    public void testAllNullFields() throws Exception {
        log.info("测试所有字段都为null的序列化");
        
        EventMessage message = new EventMessage();
        // 所有字段都保持null
        
        String json = objectMapper.writeValueAsString(message);
        log.info("全null字段测试结果: {}", json);
        
        // 验证即使所有字段都是null，JSON中也要包含所有字段
        String[] expectedFields = {
            "camera_index_code", "event_id", "event_type", "image_url"
        };
        
        for (String field : expectedFields) {
            assertTrue(json.contains("\"" + field + "\":null") || 
                      json.contains("\"" + field + "\" : null"), 
                      "即使为null，字段也应该存在: " + field);
        }
        
        // 验证JSON不为空
        assertFalse(json.equals("{}"), "JSON不应该是空对象");
    }

    @Test
    public void testJsonIncludeAlwaysConfiguration() throws Exception {
        log.info("测试JsonInclude.Include.ALWAYS配置");
        
        // 创建一个混合了null、空字符串、0值的对象
        ResourceMessage message = ResourceMessage.builder()
                .eventId("test-mixed")
                .cameraIndexCode(null) // null
                .cameraChannelCode("") // 空字符串
                .imageRetry(0) // 0值
                .videoRetry(null) // null Integer
                .duration(0L) // 0值 long
                .build();
        
        String json = objectMapper.writeValueAsString(message);
        log.info("混合值测试结果: {}", json);
        
        // 验证不同类型的"空"值都被包含
        assertTrue(json.contains("\"camera_index_code\":null"), 
                  "null字符串应该包含");
        assertTrue(json.contains("\"camera_channel_code\":\"\""), 
                  "空字符串应该包含");
        assertTrue(json.contains("\"image_retry\":0"), 
                  "0值应该包含");
        assertTrue(json.contains("\"video_retry\":null"), 
                  "null Integer应该包含");
        assertTrue(json.contains("\"duration\":0"), 
                  "0值long应该包含");
        
        log.info("✅ JsonInclude.Include.ALWAYS配置测试通过");
    }
}
