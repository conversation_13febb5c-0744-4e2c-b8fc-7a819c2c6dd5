package com.sdses.ai.imagetransfer.config;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis配置测试类
 * <AUTHOR>
 * @create 2025-07-04
 */
@Slf4j
@SpringBootTest
public class RedisConfigTest {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 测试RedissonClient连接
     */
    @Test
    public void testRedissonClient() {
        try {
            // 测试基本操作
            redissonClient.getBucket("test:key").set("test-value");
            String value = (String) redissonClient.getBucket("test:key").get();
            
            log.info("RedissonClient测试成功，值: {}", value);
            assert "test-value".equals(value) : "值不匹配";
            
            // 清理测试数据
            redissonClient.getBucket("test:key").delete();
            
        } catch (Exception e) {
            log.error("RedissonClient测试失败", e);
            throw e;
        }
    }

    /**
     * 测试RedisTemplate连接
     */
    @Test
    public void testRedisTemplate() {
        try {
            // 测试基本操作
            redisTemplate.opsForValue().set("test:template:key", "template-value");
            String value = (String) redisTemplate.opsForValue().get("test:template:key");
            
            log.info("RedisTemplate测试成功，值: {}", value);
            assert "template-value".equals(value) : "值不匹配";
            
            // 清理测试数据
            redisTemplate.delete("test:template:key");
            
        } catch (Exception e) {
            log.error("RedisTemplate测试失败", e);
            throw e;
        }
    }

    /**
     * 测试Redis连接状态
     */
    @Test
    public void testRedisConnection() {
        try {
            // 测试ping命令
            String ping = redisTemplate.getConnectionFactory().getConnection().ping();
            log.info("Redis连接状态: {}", ping);
            assert "PONG".equals(ping) : "Redis连接失败";
            
        } catch (Exception e) {
            log.error("Redis连接测试失败", e);
            throw e;
        }
    }
}
