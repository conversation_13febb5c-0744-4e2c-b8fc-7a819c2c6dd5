package com.sdses.ai.imagetransfer.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Kafka配置测试类
 * 验证Kafka消费者配置是否正确加载和生效
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@SpringBootTest
@ActiveProfiles("test")
class KafkaConfigTest {

    @Autowired
    private KafkaConsumerProperties consumerProperties;

    @Test
    void testConsumerPropertiesLoaded() {
        // 验证配置属性是否正确加载
        assertNotNull(consumerProperties);
        assertNotNull(consumerProperties.getGroupId());
        assertTrue(consumerProperties.getConcurrency() > 0);
        assertTrue(consumerProperties.getRetryConcurrency() > 0);
        assertTrue(consumerProperties.getMaxPollRecords() > 0);
    }

    @Test
    void testConfigValidation() {
        // 验证配置的合理性
        assertTrue(consumerProperties.isConfigValid(), "配置应该是有效的");
        
        // 验证心跳间隔配置
        assertTrue(consumerProperties.getSessionTimeoutMs() > consumerProperties.getHeartbeatIntervalMs() * 3,
                "会话超时时间应该大于心跳间隔的3倍");
        
        // 验证并发数配置
        assertTrue(consumerProperties.getConcurrency() > 0, "主消费者并发数应该大于0");
        assertTrue(consumerProperties.getRetryConcurrency() > 0, "重试消费者并发数应该大于0");
    }

    @Test
    void testTestEnvironmentConfig() {
        // 验证测试环境的特定配置
        assertEquals("submerge-tracking-water-group", consumerProperties.getGroupId());
        assertEquals(30, consumerProperties.getConcurrency());
        assertEquals(10, consumerProperties.getRetryConcurrency());
        assertEquals(500, consumerProperties.getMaxPollRecords());
        assertEquals(30000, consumerProperties.getSessionTimeoutMs());
        assertEquals(3000, consumerProperties.getHeartbeatIntervalMs());
    }

    @Test
    void testConfigDescription() {
        // 验证配置描述信息
        String description = consumerProperties.getConfigDescription();
        assertNotNull(description);
        assertTrue(description.contains("Concurrency: 30"));
        assertTrue(description.contains("Retry: 10"));
        assertTrue(description.contains("MaxPoll: 500"));
    }

    @Test
    void testPerformanceConfig() {
        // 验证性能相关配置
        assertEquals(300000, consumerProperties.getMaxPollIntervalMs());
        assertEquals(1, consumerProperties.getFetchMinSize());
        assertEquals(500, consumerProperties.getFetchMaxWait());
        assertEquals("latest", consumerProperties.getAutoOffsetReset());
        assertFalse(consumerProperties.getEnableAutoCommit());
    }
}
