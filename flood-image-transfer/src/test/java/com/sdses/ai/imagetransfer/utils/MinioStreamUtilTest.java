package com.sdses.ai.imagetransfer.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;

/**
 * MinIO流式上传测试类
 * <AUTHOR>
 * @create 2025-07-04
 */
@Slf4j
@SpringBootTest
public class MinioStreamUtilTest {

    @Autowired
    private MinioStreamUtil minioStreamUtil;

    /**
     * 测试MinIO连接
     */
    @Test
    public void testConnection() {
        try {
            boolean connected = minioStreamUtil.checkConnection();
            log.info("MinIO连接状态: {}", connected ? "正常" : "异常");
            assert connected : "MinIO连接应该正常";

        } catch (Exception e) {
            log.error("连接测试失败", e);
            throw new RuntimeException("连接测试失败", e);
        }
    }

    /**
     * 测试基本的流式上传功能
     */
    @Test
    public void testUploadStream() {
        try {
            // 创建测试数据
            String testData = "这是一个测试文件内容，用于验证MinIO上传功能";
            byte[] testBytes = testData.getBytes("UTF-8");
            InputStream inputStream = new ByteArrayInputStream(testBytes);
            
            String objectName = "test/upload_test_" + System.currentTimeMillis() + ".txt";
            String contentType = "text/plain";
            long size = testBytes.length;
            
            log.info("开始测试上传，对象名: {}, 大小: {} bytes", objectName, size);
            
            // 执行上传
            String fileUrl = minioStreamUtil.uploadStream(inputStream, objectName, contentType, size);
            
            log.info("上传成功，文件URL: {}", fileUrl);
            assert fileUrl != null && !fileUrl.isEmpty() : "文件URL不能为空";
            
        } catch (Exception e) {
            log.error("上传测试失败", e);
            throw new RuntimeException("上传测试失败", e);
        }
    }

    /**
     * 测试自动生成路径的上传功能
     */
    @Test
    public void testUploadStreamWithPath() {
        try {
            // 创建测试数据
            String testData = "测试图片数据内容";
            byte[] testBytes = testData.getBytes("UTF-8");
            InputStream inputStream = new ByteArrayInputStream(testBytes);
            
            String cameraCode = "CAM001";
            LocalDateTime eventTime = LocalDateTime.now();
            String contentType = "image/jpeg";
            long size = testBytes.length;
            
            log.info("开始测试自动路径上传，摄像头: {}, 时间: {}", cameraCode, eventTime);
            
            // 执行上传
            String fileUrl = minioStreamUtil.uploadStreamWithPath(
                    inputStream, cameraCode, eventTime, contentType, size, null);
            
            log.info("自动路径上传成功，文件URL: {}", fileUrl);
            assert fileUrl != null && !fileUrl.isEmpty() : "文件URL不能为空";
            
        } catch (Exception e) {
            log.error("自动路径上传测试失败", e);
            throw new RuntimeException("自动路径上传测试失败", e);
        }
    }

    /**
     * 测试未知大小的流上传
     */
    @Test
    public void testUploadStreamUnknownSize() {
        try {
            // 创建测试数据
            String testData = "未知大小的流测试数据";
            byte[] testBytes = testData.getBytes("UTF-8");
            InputStream inputStream = new ByteArrayInputStream(testBytes);
            
            String objectName = "test/unknown_size_test_" + System.currentTimeMillis() + ".txt";
            String contentType = "text/plain";
            long size = -1; // 未知大小
            
            log.info("开始测试未知大小上传，对象名: {}", objectName);
            
            // 执行上传
            String fileUrl = minioStreamUtil.uploadStream(inputStream, objectName, contentType, size);
            
            log.info("未知大小上传成功，文件URL: {}", fileUrl);
            assert fileUrl != null && !fileUrl.isEmpty() : "文件URL不能为空";
            
        } catch (Exception e) {
            log.error("未知大小上传测试失败", e);
            throw new RuntimeException("未知大小上传测试失败", e);
        }
    }

    /**
     * 测试路径生成功能
     */
    @Test
    public void testGeneratePath() {
        try {
            String cameraCode = "CAM001";
            LocalDateTime eventTime = LocalDateTime.of(2025, 7, 4, 15, 30, 45);

            String path = minioStreamUtil.generatePath(cameraCode, eventTime, null);

            log.info("生成的路径: {}", path);
            assert path != null && !path.isEmpty() : "生成的路径不能为空";
            assert path.contains(cameraCode) : "路径应包含摄像头编码";
            assert path.contains("20250704") : "路径应包含日期";

        } catch (Exception e) {
            log.error("路径生成测试失败", e);
            throw new RuntimeException("路径生成测试失败", e);
        }
    }

    /**
     * 测试永久URL生成功能
     */
    @Test
    public void testGetPermanentFileUrl() {
        try {
            String objectName = "images/20250707/5e18d86759f84324a8763fabf62e4fc8/5e18d86759f84324a8763fabf62e4fc8_20250707085609.jpg";

            String permanentUrl = minioStreamUtil.getPermanentFileUrl(objectName);

            log.info("生成的永久URL: {}", permanentUrl);
            assert permanentUrl != null && !permanentUrl.isEmpty() : "永久URL不能为空";
            assert permanentUrl.startsWith("http") : "URL应该以http开头";
            assert permanentUrl.contains(objectName) : "URL应该包含对象名";

            // 验证URL格式符合预期：http://host:port/bucket/objectName
            assert permanentUrl.matches("http://[^/]+/[^/]+/.*") : "URL格式应该正确";

        } catch (Exception e) {
            log.error("永久URL生成测试失败", e);
            throw new RuntimeException("永久URL生成测试失败", e);
        }
    }

    /**
     * 测试uploadStreamWithPath返回永久URL
     */
    @Test
    public void testUploadStreamWithPathReturnsPermanentUrl() {
        try {
            // 创建测试数据
            String testData = "测试永久URL返回功能";
            byte[] testBytes = testData.getBytes("UTF-8");
            InputStream inputStream = new ByteArrayInputStream(testBytes);

            String cameraCode = "5e18d86759f84324a8763fabf62e4fc8";
            LocalDateTime eventTime = LocalDateTime.of(2025, 7, 7, 8, 56, 9);
            String contentType = "image/jpeg";
            long size = testBytes.length;

            log.info("开始测试uploadStreamWithPath永久URL返回，摄像头: {}, 时间: {}", cameraCode, eventTime);

            // 执行上传
            String fileUrl = minioStreamUtil.uploadStreamWithPath(
                    inputStream, cameraCode, eventTime, contentType, size, null);

            log.info("uploadStreamWithPath返回的URL: {}", fileUrl);

            // 验证返回的是永久URL而不是临时URL
            assert fileUrl != null && !fileUrl.isEmpty() : "文件URL不能为空";
            assert !fileUrl.contains("X-Amz-") : "不应该包含临时URL的签名参数";
            assert fileUrl.startsWith("http") : "URL应该以http开头";

            // 验证URL格式：http://host:port/bucket/images/date/camera/filename
            String expectedPattern = ".*/images/20250707/" + cameraCode + "/" + cameraCode + "_20250707085609\\.jpg$";
            assert fileUrl.matches(".*" + expectedPattern) : "URL应该符合预期的路径格式";

            log.info("✅ uploadStreamWithPath永久URL测试通过");

        } catch (Exception e) {
            log.error("uploadStreamWithPath永久URL测试失败", e);
            throw new RuntimeException("uploadStreamWithPath永久URL测试失败", e);
        }
    }
}
