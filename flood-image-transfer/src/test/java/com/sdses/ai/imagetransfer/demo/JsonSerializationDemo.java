package com.sdses.ai.imagetransfer.demo;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sdses.ai.imagetransfer.entity.CommonEventResourceLast;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.entity.ResourceMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

/**
 * JSON序列化演示
 * 展示不同实体类的LocalDateTime字段序列化格式差异
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@SpringBootTest
public class JsonSerializationDemo {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void demonstrateJsonSerialization() throws Exception {
        log.info("=== JSON序列化格式演示 ===");
        
        LocalDateTime testDateTime = LocalDateTime.of(2025, 7, 14, 15, 30, 45);
        
        // 1. CommonEventResourceLast - 使用 "yyyy-MM-dd HH:mm:ss" 格式
        log.info("\n1. CommonEventResourceLast实体 (目标格式: yyyy-MM-dd HH:mm:ss):");
        CommonEventResourceLast commonEvent = new CommonEventResourceLast();
        commonEvent.setEventId("demo-common-001");
        commonEvent.setEventTime(testDateTime);
        commonEvent.setStartTime(testDateTime);
        commonEvent.setEndTime(testDateTime);
        commonEvent.setAcceptTime(testDateTime);
        commonEvent.setSinkTime(testDateTime);
        
        String commonEventJson = objectMapper.writeValueAsString(commonEvent);
        log.info("序列化结果: {}", commonEventJson);
        log.info("✅ LocalDateTime格式: 2025-07-14 15:30:45 (无时区信息)");
        
        // 2. EventMessage - 保持原有 ISO8601 格式
        log.info("\n2. EventMessage实体 (保持原格式: ISO8601 with timezone):");
        EventMessage eventMessage = new EventMessage();
        eventMessage.setEventId("demo-event-001");
        eventMessage.setEventTime(testDateTime);
        
        String eventMessageJson = objectMapper.writeValueAsString(eventMessage);
        log.info("序列化结果: {}", eventMessageJson);
        log.info("✅ LocalDateTime格式: 2025-07-14T15:30:45.000+08:00 (带时区信息)");
        
        // 3. ResourceMessage - 保持原有 ISO8601 格式
        log.info("\n3. ResourceMessage实体 (保持原格式: ISO8601 with timezone):");
        ResourceMessage resourceMessage = ResourceMessage.builder()
                .eventId("demo-resource-001")
                .eventTime(testDateTime)
                .startTime(testDateTime)
                .endTime(testDateTime)
                .build();
        
        String resourceMessageJson = objectMapper.writeValueAsString(resourceMessage);
        log.info("序列化结果: {}", resourceMessageJson);
        log.info("✅ LocalDateTime格式: 2025-07-14T15:30:45.000+08:00 (带时区信息)");
        
        log.info("\n=== 总结 ===");
        log.info("✅ CommonEventResourceLast: 使用简单日期时间格式 (yyyy-MM-dd HH:mm:ss)");
        log.info("✅ EventMessage: 保持ISO8601格式 (yyyy-MM-dd'T'HH:mm:ss.SSSXXX)");
        log.info("✅ ResourceMessage: 保持ISO8601格式 (yyyy-MM-dd'T'HH:mm:ss.SSSXXX)");
        log.info("✅ 配置成功：只影响CommonEventResourceLast，其他实体类不受影响");
    }
}
