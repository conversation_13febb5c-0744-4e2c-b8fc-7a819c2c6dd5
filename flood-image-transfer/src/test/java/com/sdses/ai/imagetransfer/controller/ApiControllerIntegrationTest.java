package com.sdses.ai.imagetransfer.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ApiController集成测试类
 * 测试完整的请求-响应流程，包括数据源切换
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-15
 */
@Slf4j
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@DisplayName("ApiController集成测试")
class ApiControllerIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @DisplayName("测试分页查询接口完整流程")
    void testQueryResultListFullFlow() throws Exception {
        setUp();
        
        log.info("开始测试分页查询接口完整流程");

        // 执行请求
        MvcResult result = mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("cameraIndexCode", "test")
                        .param("pageNum", "1")
                        .param("pageSize", "5")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.data").exists())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        log.info("响应内容: {}", responseContent);

        // 验证响应结构
        assertTrue(responseContent.contains("\"code\""));
        assertTrue(responseContent.contains("\"message\""));
        assertTrue(responseContent.contains("\"data\""));

        log.info("分页查询接口完整流程测试完成");
    }

    @Test
    @DisplayName("测试数据源切换功能")
    void testDataSourceSwitching() throws Exception {
        setUp();
        
        log.info("开始测试数据源切换功能");

        // 这个测试主要验证@DS注解是否正确工作
        // 实际的数据源切换逻辑在Mapper层
        MvcResult result = mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        log.info("数据源切换测试响应: {}", responseContent);

        // 验证请求成功处理（说明数据源切换正常）
        assertTrue(responseContent.contains("\"code\""));

        log.info("数据源切换功能测试完成");
    }

    @Test
    @DisplayName("测试分页参数验证")
    void testPaginationParameterValidation() throws Exception {
        setUp();
        
        log.info("开始测试分页参数验证");

        // 测试无效的分页参数
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "0")
                        .param("pageSize", "-1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        // 测试超大的分页参数
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "999999")
                        .param("pageSize", "2000")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        log.info("分页参数验证测试完成");
    }

    @Test
    @DisplayName("测试查询条件特殊字符处理")
    void testSpecialCharacterHandling() throws Exception {
        setUp();
        
        log.info("开始测试查询条件特殊字符处理");

        // 测试包含特殊字符的查询条件
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("cameraForeignCode", "test%_'\"")
                        .param("cameraIndexCode", "test\\n\\t")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        log.info("查询条件特殊字符处理测试完成");
    }

    @Test
    @DisplayName("测试并发请求处理")
    void testConcurrentRequestHandling() throws Exception {
        setUp();
        
        log.info("开始测试并发请求处理");

        // 模拟并发请求
        Thread[] threads = new Thread[5];
        for (int i = 0; i < 5; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    mockMvc.perform(get("/api/openAPI/queryResultList")
                                    .param("cameraIndexCode", "thread-" + threadIndex)
                                    .param("pageNum", "1")
                                    .param("pageSize", "10")
                                    .contentType(MediaType.APPLICATION_JSON))
                            .andExpect(status().isOk());
                    
                    log.info("线程 {} 请求完成", threadIndex);
                } catch (Exception e) {
                    log.error("线程 {} 请求失败", threadIndex, e);
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join(5000); // 最多等待5秒
        }

        log.info("并发请求处理测试完成");
    }

    @Test
    @DisplayName("测试响应时间性能")
    void testResponseTimePerformance() throws Exception {
        setUp();
        
        log.info("开始测试响应时间性能");

        long startTime = System.currentTimeMillis();

        // 执行查询请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "1")
                        .param("pageSize", "100")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;

        log.info("查询响应时间: {} ms", responseTime);

        // 验证响应时间在合理范围内（这里设置为5秒，实际项目中可以根据需求调整）
        assertTrue(responseTime < 5000, "响应时间应该小于5秒，实际: " + responseTime + "ms");

        log.info("响应时间性能测试完成");
    }

    @Test
    @DisplayName("测试大数据量分页查询")
    void testLargeDataPagination() throws Exception {
        setUp();
        
        log.info("开始测试大数据量分页查询");

        // 测试大页码查询
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "1000")
                        .param("pageSize", "50")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        // 测试最大页大小查询
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "1")
                        .param("pageSize", "1000")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        log.info("大数据量分页查询测试完成");
    }

    @Test
    @DisplayName("测试JSON序列化格式")
    void testJsonSerializationFormat() throws Exception {
        setUp();
        
        log.info("开始测试JSON序列化格式");

        MvcResult result = mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "1")
                        .param("pageSize", "5")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        log.info("JSON响应格式: {}", responseContent);

        // 验证JSON结构
        assertTrue(responseContent.contains("\"total\""));
        assertTrue(responseContent.contains("\"current\""));
        assertTrue(responseContent.contains("\"size\""));
        assertTrue(responseContent.contains("\"pages\""));
        assertTrue(responseContent.contains("\"records\""));

        // 如果有数据，验证时间字段格式
        if (responseContent.contains("\"event_time\"")) {
            // 验证时间格式是否符合预期（yyyy-MM-dd HH:mm:ss）
            assertTrue(responseContent.matches(".*\"event_time\"\\s*:\\s*\"\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\".*") ||
                      responseContent.contains("\"event_time\":null"));
        }

        log.info("JSON序列化格式测试完成");
    }

    @Test
    @DisplayName("测试时间范围查询集成")
    void testTimeRangeQueryIntegration() throws Exception {
        setUp();

        log.info("开始测试时间范围查询集成");

        // 测试仅开始时间查询
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "2025-07-15 10:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        // 测试仅结束时间查询
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("endTime", "2025-07-15 18:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        // 测试时间范围查询
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "2025-07-15 10:00:00")
                        .param("endTime", "2025-07-15 18:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        log.info("时间范围查询集成测试完成");
    }

    @Test
    @DisplayName("测试时间格式错误集成处理")
    void testTimeFormatErrorIntegration() throws Exception {
        setUp();

        log.info("开始测试时间格式错误集成处理");

        // 测试无效时间格式
        MvcResult result = mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "invalid-time")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        assertTrue(responseContent.contains("时间格式错误"));

        log.info("时间格式错误集成处理测试完成");
    }

    @Test
    @DisplayName("测试时间逻辑错误集成处理")
    void testTimeLogicErrorIntegration() throws Exception {
        setUp();

        log.info("开始测试时间逻辑错误集成处理");

        // 测试开始时间晚于结束时间
        MvcResult result = mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "2025-07-15 18:00:00")
                        .param("endTime", "2025-07-15 10:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        assertTrue(responseContent.contains("开始时间必须早于结束时间"));

        log.info("时间逻辑错误集成处理测试完成");
    }

    @Test
    @DisplayName("测试复合查询条件集成")
    void testComplexQueryConditionsIntegration() throws Exception {
        setUp();

        log.info("开始测试复合查询条件集成");

        // 测试所有查询条件组合
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("cameraForeignCode", "outdoor")
                        .param("cameraIndexCode", "cam001")
                        .param("startTime", "2025-07-15 10:00:00")
                        .param("endTime", "2025-07-15 18:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "20")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        log.info("复合查询条件集成测试完成");
    }

    @Test
    @DisplayName("测试时间边界值集成")
    void testTimeBoundaryValuesIntegration() throws Exception {
        setUp();

        log.info("开始测试时间边界值集成");

        // 测试最小时间值
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "1900-01-01 00:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        // 测试最大时间值
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("endTime", "2099-12-31 23:59:59")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());

        log.info("时间边界值集成测试完成");
    }

    private void assertTrue(boolean condition, String message) {
        if (!condition) {
            throw new AssertionError(message);
        }
    }

    private void assertTrue(boolean condition) {
        assertTrue(condition, "Assertion failed");
    }
}
