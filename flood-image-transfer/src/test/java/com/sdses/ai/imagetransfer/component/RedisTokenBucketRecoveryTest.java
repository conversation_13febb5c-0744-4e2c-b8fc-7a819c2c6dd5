package com.sdses.ai.imagetransfer.component;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis令牌桶自动恢复测试
 * 验证令牌桶在空闲状态下是否能正确自动恢复到满容量
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */
@Slf4j
@SpringBootTest
public class RedisTokenBucketRecoveryTest {

    @Autowired
    private RedisTokenBucket redisTokenBucket;

    /**
     * 测试令牌桶自动恢复机制
     */
    @Test
    public void testTokenBucketAutoRecovery() throws InterruptedException {
        String bucketKey = "test-recovery-bucket";
        double rate = 10.0; // 每秒10个令牌
        int capacity = 50;  // 桶容量50
        
        log.info("=== 令牌桶自动恢复测试开始 ===");
        log.info("配置: rate={} 令牌/秒, capacity={}", rate, capacity);
        
        // 1. 初始化令牌桶
        redisTokenBucket.initBucket(bucketKey, capacity, 1);
        Long initialTokens = redisTokenBucket.getCurrentTokens(bucketKey);
        log.info("1. 初始化后令牌数: {}", initialTokens);
        assertEquals(capacity, initialTokens.intValue(), "初始化后应该有满容量的令牌");
        
        // 2. 消耗所有令牌
        log.info("2. 开始消耗所有令牌...");
        int consumedTokens = 0;
        while (redisTokenBucket.tryAcquire(bucketKey, rate, capacity)) {
            consumedTokens++;
        }
        log.info("   消耗了 {} 个令牌", consumedTokens);
        
        Long tokensAfterConsumption = redisTokenBucket.getCurrentTokens(bucketKey);
        log.info("   消耗后剩余令牌数: {}", tokensAfterConsumption);
        assertEquals(0, tokensAfterConsumption.intValue(), "消耗后应该没有剩余令牌");
        
        // 3. 等待2秒，验证令牌自动补充
        log.info("3. 等待2秒，验证令牌自动补充...");
        Thread.sleep(2000);
        
        // 尝试获取一个令牌，这会触发令牌补充计算
        boolean canAcquire = redisTokenBucket.tryAcquire(bucketKey, rate, capacity);
        Long tokensAfter2Seconds = redisTokenBucket.getCurrentTokens(bucketKey);
        
        log.info("   2秒后能否获取令牌: {}", canAcquire);
        log.info("   2秒后令牌数: {}", tokensAfter2Seconds);
        
        // 2秒 * 10令牌/秒 = 20个令牌，减去刚获取的1个 = 19个
        int expectedTokens = (int)(2 * rate) - (canAcquire ? 1 : 0);
        assertTrue(canAcquire, "2秒后应该能获取到令牌");
        assertEquals(expectedTokens, tokensAfter2Seconds.intValue(), 
                    "2秒后应该有约" + expectedTokens + "个令牌");
        
        // 4. 等待足够长时间，验证令牌恢复到满容量
        log.info("4. 等待6秒，验证令牌恢复到满容量...");
        Thread.sleep(6000);
        
        // 触发令牌补充计算
        redisTokenBucket.tryAcquire(bucketKey, rate, capacity);
        Long tokensAfterFullRecovery = redisTokenBucket.getCurrentTokens(bucketKey);
        
        log.info("   6秒后令牌数: {}", tokensAfterFullRecovery);
        
        // 6秒足够恢复到满容量（6秒 * 10令牌/秒 = 60 > 50容量）
        assertTrue(tokensAfterFullRecovery >= capacity - 1, 
                  "足够长时间后应该恢复到接近满容量");
        
        // 5. 验证容量上限
        log.info("5. 验证容量上限控制...");
        Thread.sleep(2000); // 再等待2秒
        redisTokenBucket.tryAcquire(bucketKey, rate, capacity);
        Long finalTokens = redisTokenBucket.getCurrentTokens(bucketKey);
        
        log.info("   最终令牌数: {}", finalTokens);
        assertTrue(finalTokens <= capacity, "令牌数不应该超过容量上限");
        
        log.info("=== 令牌桶自动恢复测试完成 ===");
        
        // 清理
        redisTokenBucket.deleteBucket(bucketKey);
    }

    /**
     * 测试不同速率下的令牌补充
     */
    @Test
    public void testDifferentRateRecovery() throws InterruptedException {
        log.info("=== 不同速率令牌补充测试开始 ===");
        
        // 测试不同的速率配置
        double[] rates = {1.0, 5.0, 30.0}; // 每秒1个、5个、30个令牌
        int capacity = 20;
        
        for (double rate : rates) {
            String bucketKey = "test-rate-" + (int)rate;
            log.info("测试速率: {} 令牌/秒", rate);
            
            // 初始化并消耗所有令牌
            redisTokenBucket.initBucket(bucketKey, capacity, 1);
            while (redisTokenBucket.tryAcquire(bucketKey, rate, capacity)) {
                // 消耗所有令牌
            }
            
            Long tokensBeforeWait = redisTokenBucket.getCurrentTokens(bucketKey);
            log.info("  消耗后令牌数: {}", tokensBeforeWait);
            
            // 等待2秒
            Thread.sleep(2000);
            
            // 触发补充并检查
            redisTokenBucket.tryAcquire(bucketKey, rate, capacity);
            Long tokensAfterWait = redisTokenBucket.getCurrentTokens(bucketKey);
            
            log.info("  2秒后令牌数: {}", tokensAfterWait);
            
            // 验证补充的令牌数是否符合预期
            int expectedTokens = Math.min((int)(2 * rate), capacity);
            assertTrue(tokensAfterWait >= expectedTokens - 1, 
                      "速率" + rate + "下，2秒后应该有约" + expectedTokens + "个令牌");
            
            // 清理
            redisTokenBucket.deleteBucket(bucketKey);
        }
        
        log.info("=== 不同速率令牌补充测试完成 ===");
    }

    /**
     * 测试长时间空闲后的恢复
     */
    @Test
    public void testLongIdleRecovery() throws InterruptedException {
        String bucketKey = "test-long-idle";
        double rate = 5.0;
        int capacity = 30;
        
        log.info("=== 长时间空闲恢复测试开始 ===");
        log.info("配置: rate={}, capacity={}", rate, capacity);
        
        // 初始化并消耗部分令牌
        redisTokenBucket.initBucket(bucketKey, capacity, 1);
        
        // 消耗20个令牌
        int consumedCount = 0;
        for (int i = 0; i < 20 && redisTokenBucket.tryAcquire(bucketKey, rate, capacity); i++) {
            consumedCount++;
        }
        
        Long tokensAfterConsumption = redisTokenBucket.getCurrentTokens(bucketKey);
        log.info("消耗{}个令牌后剩余: {}", consumedCount, tokensAfterConsumption);
        
        // 长时间空闲（10秒）
        log.info("长时间空闲10秒...");
        Thread.sleep(10000);
        
        // 检查恢复情况
        redisTokenBucket.tryAcquire(bucketKey, rate, capacity);
        Long tokensAfterIdle = redisTokenBucket.getCurrentTokens(bucketKey);
        
        log.info("10秒空闲后令牌数: {}", tokensAfterIdle);
        
        // 10秒 * 5令牌/秒 = 50个令牌，但容量限制为30
        assertEquals(capacity - 1, tokensAfterIdle.intValue(), 
                    "长时间空闲后应该恢复到满容量（减去刚获取的1个）");
        
        log.info("=== 长时间空闲恢复测试完成 ===");
        
        // 清理
        redisTokenBucket.deleteBucket(bucketKey);
    }

    /**
     * 测试令牌桶的精确时间计算
     */
    @Test
    public void testPreciseTimeCalculation() throws InterruptedException {
        String bucketKey = "test-precise-time";
        double rate = 2.0; // 每秒2个令牌
        int capacity = 10;
        
        log.info("=== 精确时间计算测试开始 ===");
        
        redisTokenBucket.initBucket(bucketKey, capacity, 1);
        
        // 消耗所有令牌
        while (redisTokenBucket.tryAcquire(bucketKey, rate, capacity)) {
            // 消耗
        }
        
        // 精确等待1.5秒
        Thread.sleep(1500);
        
        // 检查令牌数
        redisTokenBucket.tryAcquire(bucketKey, rate, capacity);
        Long tokens = redisTokenBucket.getCurrentTokens(bucketKey);
        
        log.info("1.5秒后令牌数: {}", tokens);
        
        // 1.5秒 * 2令牌/秒 = 3个令牌，减去刚获取的1个 = 2个
        assertEquals(2, tokens.intValue(), "1.5秒后应该有2个令牌");
        
        log.info("=== 精确时间计算测试完成 ===");
        
        // 清理
        redisTokenBucket.deleteBucket(bucketKey);
    }
}
