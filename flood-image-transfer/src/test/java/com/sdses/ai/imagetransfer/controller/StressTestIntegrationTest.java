package com.sdses.ai.imagetransfer.controller;

import com.sdses.ai.imagetransfer.controller.TestController.StressTestRequest;
import com.sdses.ai.imagetransfer.controller.TestController.StressTestResult;
import com.sdses.ai.imagetransfer.entity.EventMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 压力测试集成测试
 * 测试压力测试的核心逻辑和性能计算
 *
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */
public class StressTestIntegrationTest {

    private TestController testController;

    @BeforeEach
    void setUp() {
        testController = new TestController();
    }

    @Test
    public void testCreateTestEventMessage() {
        // 测试创建测试消息的方法
        TestController controller = new TestController();
        
        // 使用反射或者创建一个测试用的方法来验证消息创建逻辑
        // 这里我们直接测试消息创建的逻辑
        EventMessage message = createTestEventMessage(0, 0, "normal");
        
        assertNotNull(message);
        assertNotNull(message.getEventId());
        assertTrue(message.getEventId().contains("stress-test"));
        assertEquals("camera-1", message.getCameraIndexCode());
        assertEquals("channel-1", message.getCameraChannelCode());
        assertEquals("foreign-0", message.getCameraForeignCode());
        assertNotNull(message.getEventTime());
        assertEquals("flood_prevention", message.getSourceSystem());
        assertEquals("stress-test", message.getSourceModule());
        assertEquals("flood_detection", message.getEventType());
        assertNotNull(message.getImageUrl());
        assertEquals(Integer.valueOf(1), message.getOnlyPicture());
    }

    @Test
    public void testCreateTestEventMessageDifferentTypes() {
        // 测试不同类型的消息创建
        
        // normal类型
        EventMessage normalMessage = createTestEventMessage(0, 0, "normal");
        assertNotNull(normalMessage.getImageUrl());
        assertEquals(Integer.valueOf(1), normalMessage.getOnlyPicture());
        assertNull(normalMessage.getImageBase64());
        
        // no-image类型
        EventMessage noImageMessage = createTestEventMessage(0, 0, "no-image");
        assertNull(noImageMessage.getImageUrl());
        assertEquals(Integer.valueOf(0), noImageMessage.getOnlyPicture());
        
        // invalid-url类型
        EventMessage invalidUrlMessage = createTestEventMessage(0, 0, "invalid-url");
        assertNotNull(invalidUrlMessage.getImageUrl());
        assertTrue(invalidUrlMessage.getImageUrl().contains("invalid-server.com"));
        assertEquals(Integer.valueOf(1), invalidUrlMessage.getOnlyPicture());
        
        // large-data类型
        EventMessage largeDataMessage = createTestEventMessage(0, 0, "large-data");
        assertNotNull(largeDataMessage.getImageUrl());
        assertNotNull(largeDataMessage.getImageBase64());
        assertTrue(largeDataMessage.getImageBase64().length() > 1000); // 应该是大数据
        assertEquals(Integer.valueOf(1), largeDataMessage.getOnlyPicture());
    }

    @Test
    public void testCalculateResult() {
        // 测试结果计算逻辑
        StressTestRequest request = new StressTestRequest();
        request.setMessageCount(1000);
        request.setThreadCount(10);
        request.setSendIntervalMs(0);
        request.setTestType("normal");
        
        int successCount = 950;
        int failedCount = 50;
        long totalTimeMs = 5000;
        
        // 模拟延迟数据
        List<Long> latencies = new ArrayList<>();
        for (int i = 0; i < successCount; i++) {
            latencies.add((long) (10 + Math.random() * 100)); // 10-110ms的随机延迟
        }
        Collections.sort(latencies);
        
        StressTestResult result = calculateResult(request, successCount, failedCount, totalTimeMs, latencies);
        
        assertNotNull(result);
        assertEquals(1000, result.getTotalMessages());
        assertEquals(950, result.getSuccessCount());
        assertEquals(50, result.getFailedCount());
        assertEquals(5000, result.getTotalTimeMs());
        assertEquals(190.0, result.getThroughputPerSecond(), 1.0); // 950 * 1000 / 5000 = 190
        assertTrue(result.getAvgLatencyMs() > 0);
        assertTrue(result.getMinLatencyMs() >= 10);
        assertTrue(result.getMaxLatencyMs() <= 110);
        assertTrue(result.getP50LatencyMs() > 0);
        assertTrue(result.getP95LatencyMs() > 0);
        assertTrue(result.getP99LatencyMs() > 0);
        assertEquals("normal", result.getTestType());
        assertEquals(10, result.getThreadCount());
        assertEquals(0, result.getSendIntervalMs());
        assertNotNull(result.getTimestamp());
    }

    @Test
    public void testPerformanceCalculations() {
        // 测试性能指标计算
        List<Long> latencies = List.of(10L, 20L, 30L, 40L, 50L, 60L, 70L, 80L, 90L, 100L);
        
        // 计算平均值
        double avgLatency = latencies.stream().mapToLong(Long::longValue).average().orElse(0.0);
        assertEquals(55.0, avgLatency, 0.1);
        
        // 计算百分位数
        long p50 = latencies.get(latencies.size() / 2);
        long p95 = latencies.get((int) (latencies.size() * 0.95));
        long p99 = latencies.get((int) (latencies.size() * 0.99));
        
        assertEquals(60L, p50); // 第5个元素（索引4）
        assertEquals(100L, p95); // 第9个元素（索引8）
        assertEquals(100L, p99); // 第9个元素（索引8）
    }

    @Test
    public void testThroughputCalculation() {
        // 测试吞吐量计算
        int successCount = 1000;
        long totalTimeMs = 5000;
        
        double throughput = successCount * 1000.0 / totalTimeMs;
        assertEquals(200.0, throughput, 0.1);
        
        // 边界情况测试
        throughput = 1 * 1000.0 / 1000;
        assertEquals(1.0, throughput, 0.1);
        
        throughput = 10000 * 1000.0 / 1000;
        assertEquals(10000.0, throughput, 0.1);
    }

    @Test
    public void testMessageDistribution() {
        // 测试消息分发逻辑
        int totalMessages = 1000;
        int threadCount = 7;
        
        int messagesPerThread = totalMessages / threadCount;
        int remainingMessages = totalMessages % threadCount;
        
        assertEquals(142, messagesPerThread); // 1000 / 7 = 142
        assertEquals(6, remainingMessages);   // 1000 % 7 = 6
        
        // 验证分发结果
        int totalDistributed = 0;
        for (int i = 0; i < threadCount; i++) {
            int threadMessages = messagesPerThread + (i < remainingMessages ? 1 : 0);
            totalDistributed += threadMessages;
        }
        assertEquals(totalMessages, totalDistributed);
    }

    @Test
    public void testParameterValidation() {
        // 测试参数验证逻辑
        StressTestRequest request = new StressTestRequest();
        
        // 测试有效参数
        request.setMessageCount(1000);
        request.setThreadCount(10);
        assertTrue(isValidRequest(request));
        
        // 测试无效参数 - 消息数量
        request.setMessageCount(0);
        assertFalse(isValidRequest(request));
        
        request.setMessageCount(200000);
        assertFalse(isValidRequest(request));
        
        // 测试无效参数 - 线程数量
        request.setMessageCount(1000);
        request.setThreadCount(0);
        assertFalse(isValidRequest(request));
        
        request.setThreadCount(200);
        assertFalse(isValidRequest(request));
    }

    // 辅助方法 - 模拟TestController中的私有方法
    private EventMessage createTestEventMessage(int threadIndex, int messageIndex, String testType) {
        EventMessage message = new EventMessage();
        
        String eventId = String.format("stress-test-%d-%d-%d", 
                System.currentTimeMillis(), threadIndex, messageIndex);
        message.setEventId(eventId);
        message.setCameraIndexCode("camera-" + (threadIndex % 10 + 1));
        message.setCameraChannelCode("channel-" + (messageIndex % 5 + 1));
        message.setCameraForeignCode("foreign-" + threadIndex);
        message.setEventTime(LocalDateTime.now());
        message.setSourceSystem("flood_prevention");
        message.setSourceModule("stress-test");
        message.setInfo("压力测试消息 - 线程" + threadIndex + "-消息" + messageIndex);
        message.setEventType("flood_detection");
        
        // 根据测试类型设置不同的数据
        switch (testType.toLowerCase()) {
            case "normal":
                message.setImageUrl("https://100.192.2.82:18001/data/snapshot/20250707/16/stress-test/" + eventId + ".jpg");
                message.setOnlyPicture(1);
                break;
            case "no-image":
                message.setImageUrl(null);
                message.setOnlyPicture(0);
                break;
            case "invalid-url":
                message.setImageUrl("https://invalid-server.com/invalid-path/" + eventId + ".jpg");
                message.setOnlyPicture(1);
                break;
            case "large-data":
                message.setImageUrl("https://100.192.2.82:18001/data/snapshot/20250707/16/stress-test/" + eventId + ".jpg");
                message.setImageBase64(generateLargeBase64Data());
                message.setOnlyPicture(1);
                break;
            default:
                message.setImageUrl("https://100.192.2.82:18001/data/snapshot/20250707/16/stress-test/" + eventId + ".jpg");
                message.setOnlyPicture(1);
        }
        
        return message;
    }

    private String generateLargeBase64Data() {
        StringBuilder sb = new StringBuilder();
        String baseData = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==";
        for (int i = 0; i < 100; i++) {
            sb.append(baseData);
        }
        return sb.toString();
    }

    private StressTestResult calculateResult(StressTestRequest request, int successCount, int failedCount,
                                           long totalTimeMs, List<Long> latencies) {
        StressTestResult result = new StressTestResult();
        
        result.setTotalMessages(request.getMessageCount());
        result.setSuccessCount(successCount);
        result.setFailedCount(failedCount);
        result.setTotalTimeMs(totalTimeMs);
        result.setThroughputPerSecond(successCount * 1000.0 / totalTimeMs);
        
        if (!latencies.isEmpty()) {
            result.setAvgLatencyMs(latencies.stream().mapToLong(Long::longValue).average().orElse(0.0));
            result.setMinLatencyMs(latencies.get(0));
            result.setMaxLatencyMs(latencies.get(latencies.size() - 1));
            result.setP50LatencyMs(latencies.get(latencies.size() / 2));
            result.setP95LatencyMs(latencies.get((int) (latencies.size() * 0.95)));
            result.setP99LatencyMs(latencies.get((int) (latencies.size() * 0.99)));
        }
        
        result.setTestType(request.getTestType());
        result.setThreadCount(request.getThreadCount());
        result.setSendIntervalMs(request.getSendIntervalMs());
        result.setTimestamp(LocalDateTime.now().toString());
        
        return result;
    }

    private boolean isValidRequest(StressTestRequest request) {
        return request.getMessageCount() > 0 && request.getMessageCount() <= 100000 &&
               request.getThreadCount() > 0 && request.getThreadCount() <= 100;
    }
}
