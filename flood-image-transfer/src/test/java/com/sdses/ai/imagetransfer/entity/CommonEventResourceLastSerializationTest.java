package com.sdses.ai.imagetransfer.entity;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试CommonEventResourceLast实体类的JSON序列化配置
 * 验证LocalDateTime字段使用"yyyy-MM-dd HH:mm:ss"格式进行序列化
 * 确保不影响其他实体类的序列化行为
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@SpringBootTest
public class CommonEventResourceLastSerializationTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testCommonEventResourceLastDateTimeFormatting() throws Exception {
        log.info("测试CommonEventResourceLast实体类LocalDateTime字段序列化格式");
        
        // 创建测试数据
        CommonEventResourceLast entity = new CommonEventResourceLast();
        entity.setEventId("test-event-001");
        entity.setCameraForeignCode("camera-001");
        entity.setCameraIndexCode("index-001");
        entity.setStatus("processing");
        
        // 设置LocalDateTime字段
        LocalDateTime testDateTime = LocalDateTime.of(2025, 7, 14, 15, 30, 45);
        entity.setEventTime(testDateTime);
        entity.setStartTime(testDateTime);
        entity.setEndTime(testDateTime);
        entity.setAcceptTime(testDateTime);
        entity.setSinkTime(testDateTime);
        
        // 序列化为JSON
        String json = objectMapper.writeValueAsString(entity);
        log.info("CommonEventResourceLast序列化结果: {}", json);
        
        // 验证LocalDateTime字段格式为"yyyy-MM-dd HH:mm:ss"
        String expectedDateTimeFormat = "2025-07-14 15:30:45";
        
        assertTrue(json.contains("\"event_time\":\"" + expectedDateTimeFormat + "\""), 
                  "eventTime字段应该使用yyyy-MM-dd HH:mm:ss格式");
        assertTrue(json.contains("\"start_time\":\"" + expectedDateTimeFormat + "\""), 
                  "startTime字段应该使用yyyy-MM-dd HH:mm:ss格式");
        assertTrue(json.contains("\"end_time\":\"" + expectedDateTimeFormat + "\""), 
                  "endTime字段应该使用yyyy-MM-dd HH:mm:ss格式");
        assertTrue(json.contains("\"accept_time\":\"" + expectedDateTimeFormat + "\""), 
                  "acceptTime字段应该使用yyyy-MM-dd HH:mm:ss格式");
        assertTrue(json.contains("\"sink_time\":\"" + expectedDateTimeFormat + "\""), 
                  "sinkTime字段应该使用yyyy-MM-dd HH:mm:ss格式");
        
        // 验证不包含时区信息
        assertFalse(json.contains("+08:00"), "不应该包含时区信息");
        assertFalse(json.contains("T"), "不应该包含ISO8601的T分隔符");
        assertFalse(json.contains(".SSS"), "不应该包含毫秒信息");
        
        log.info("✅ CommonEventResourceLast LocalDateTime格式化测试通过");
    }

    @Test
    public void testOtherEntitiesNotAffected() throws Exception {
        log.info("测试其他实体类的LocalDateTime序列化不受影响");
        
        // 测试EventMessage实体（应该保持原有格式）
        EventMessage eventMessage = new EventMessage();
        eventMessage.setEventId("test-event-002");
        eventMessage.setCameraIndexCode("camera-002");
        
        LocalDateTime testDateTime = LocalDateTime.of(2025, 7, 14, 15, 30, 45);
        eventMessage.setEventTime(testDateTime);
        
        String eventMessageJson = objectMapper.writeValueAsString(eventMessage);
        log.info("EventMessage序列化结果: {}", eventMessageJson);
        
        // EventMessage应该使用ISO8601格式（带时区）
        assertTrue(eventMessageJson.contains("2025-07-14T15:30:45.000+08:00"), 
                  "EventMessage应该保持ISO8601格式");
        
        // 测试ResourceMessage实体（应该保持原有格式）
        ResourceMessage resourceMessage = ResourceMessage.builder()
                .eventId("test-resource-002")
                .cameraIndexCode("camera-002")
                .startTime(testDateTime)
                .endTime(testDateTime)
                .eventTime(testDateTime)
                .build();
        
        String resourceMessageJson = objectMapper.writeValueAsString(resourceMessage);
        log.info("ResourceMessage序列化结果: {}", resourceMessageJson);
        
        // ResourceMessage应该使用ISO8601格式（带时区）
        assertTrue(resourceMessageJson.contains("2025-07-14T15:30:45.000+08:00"), 
                  "ResourceMessage应该保持ISO8601格式");
        
        log.info("✅ 其他实体类序列化格式未受影响测试通过");
    }

    @Test
    public void testNullDateTimeFields() throws Exception {
        log.info("测试CommonEventResourceLast中null的LocalDateTime字段");
        
        CommonEventResourceLast entity = new CommonEventResourceLast();
        entity.setEventId("test-null-datetime");
        entity.setCameraForeignCode("camera-null");
        // 所有LocalDateTime字段保持null
        
        String json = objectMapper.writeValueAsString(entity);
        log.info("包含null LocalDateTime字段的序列化结果: {}", json);
        
        // 验证null字段正确序列化
        assertTrue(json.contains("\"event_time\":null"), "null eventTime应该序列化为null");
        assertTrue(json.contains("\"start_time\":null"), "null startTime应该序列化为null");
        assertTrue(json.contains("\"end_time\":null"), "null endTime应该序列化为null");
        assertTrue(json.contains("\"accept_time\":null"), "null acceptTime应该序列化为null");
        assertTrue(json.contains("\"sink_time\":null"), "null sinkTime应该序列化为null");
        
        log.info("✅ null LocalDateTime字段序列化测试通过");
    }

    @Test
    public void testAllFieldsPresent() throws Exception {
        log.info("测试CommonEventResourceLast所有字段都包含在JSON中");
        
        CommonEventResourceLast entity = new CommonEventResourceLast();
        entity.setEventId("test-all-fields");
        
        String json = objectMapper.writeValueAsString(entity);
        log.info("完整字段序列化结果: {}", json);
        
        // 验证所有@JsonProperty字段都存在
        String[] expectedFields = {
            "camera_foreign_code", "event_time", "start_time", "end_time", "event_id",
            "camera_index_code", "camera_channel_code", "image_msg", "video_msg",
            "image_retry", "video_retry", "status", "duration", "info",
            "source_system", "system_module", "minio_video_url", "minio_image_url",
            "accept_time", "partition", "offset", "sink_time"
        };
        
        for (String field : expectedFields) {
            assertTrue(json.contains("\"" + field + "\""), 
                      "JSON应该包含字段: " + field);
        }
        
        log.info("✅ 所有字段包含测试通过");
    }
}
