package com.sdses.ai.imagetransfer.component;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.List;

/**
 * 简单的Redis测试类
 * <AUTHOR>
 * @create 2025-07-04
 */
@Slf4j
@SpringBootTest
public class SimpleRedisTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 测试Redis基本连接
     */
    @Test
    public void testRedisConnection() {
        try {
            // 测试基本的set/get操作
            String key = "test:simple:" + System.currentTimeMillis();
            String value = "test-value";
            
            redisTemplate.opsForValue().set(key, value);
            String result = (String) redisTemplate.opsForValue().get(key);
            
            log.info("Redis连接测试 - 设置值: {}, 获取值: {}", value, result);
            assert value.equals(result) : "值不匹配";
            
            // 清理测试数据
            redisTemplate.delete(key);
            
            log.info("Redis基本连接测试通过！");
            
        } catch (Exception e) {
            log.error("Redis连接测试失败", e);
            throw e;
        }
    }

    /**
     * 测试简单的Lua脚本执行
     */
    @Test
    public void testSimpleLuaScript() {
        try {
            // 简单的Lua脚本：返回传入的参数
            String luaScript = """
                local param1 = ARGV[1]
                local param2 = ARGV[2]
                return param1 .. ":" .. param2
                """;
            
            DefaultRedisScript<String> script = new DefaultRedisScript<>(luaScript, String.class);
            
            String result = redisTemplate.execute(
                script,
                List.of(), // 空的keys列表
                "hello", "world" // 参数
            );
            
            log.info("简单Lua脚本执行结果: {}", result);
            assert "hello:world".equals(result) : "Lua脚本结果不正确";
            
            log.info("简单Lua脚本测试通过！");
            
        } catch (Exception e) {
            log.error("简单Lua脚本测试失败", e);
            throw e;
        }
    }

    /**
     * 测试数值参数的Lua脚本
     */
    @Test
    public void testNumericLuaScript() {
        try {
            // 测试数值运算的Lua脚本
            String luaScript = """
                local num1 = tonumber(ARGV[1])
                local num2 = tonumber(ARGV[2])
                
                if not num1 or not num2 then
                    return -1
                end
                
                return num1 + num2
                """;
            
            DefaultRedisScript<Long> script = new DefaultRedisScript<>(luaScript, Long.class);
            
            // 测试传递数值类型
            Long result1 = redisTemplate.execute(
                script,
                List.of(),
                10, 20 // 直接传递数值
            );
            
            log.info("数值Lua脚本执行结果1: {}", result1);
            assert result1 != null && result1 == 30 : "数值运算结果不正确";
            
            // 测试传递字符串类型
            Long result2 = redisTemplate.execute(
                script,
                List.of(),
                "15", "25" // 传递字符串
            );
            
            log.info("数值Lua脚本执行结果2: {}", result2);
            assert result2 != null && result2 == 40 : "字符串数值运算结果不正确";
            
            log.info("数值Lua脚本测试通过！");
            
        } catch (Exception e) {
            log.error("数值Lua脚本测试失败", e);
            throw e;
        }
    }

    /**
     * 测试令牌桶相关的Lua脚本逻辑
     */
    @Test
    public void testTokenBucketLuaLogic() {
        try {
            // 简化的令牌桶逻辑测试
            String luaScript = """
                local tokenKey = KEYS[1]
                local timeKey = KEYS[2]
                
                local rate = tonumber(ARGV[1])
                local capacity = tonumber(ARGV[2])
                local now = tonumber(ARGV[3])
                local requested = tonumber(ARGV[4])
                
                -- 参数验证
                if not rate or not capacity or not now or not requested then
                    return -1
                end
                
                -- 获取当前令牌数
                local currentTokens = tonumber(redis.call('get', tokenKey) or capacity)
                
                -- 简单逻辑：如果有足够令牌就扣除
                if currentTokens >= requested then
                    redis.call('set', tokenKey, currentTokens - requested)
                    redis.call('set', timeKey, now)
                    return 1
                else
                    return 0
                end
                """;
            
            DefaultRedisScript<Long> script = new DefaultRedisScript<>(luaScript, Long.class);
            
            String tokenKey = "test:token:" + System.currentTimeMillis();
            String timeKey = "test:time:" + System.currentTimeMillis();
            
            Long result = redisTemplate.execute(
                script,
                List.of(tokenKey, timeKey),
                10.0, 100, System.currentTimeMillis(), 5 // rate, capacity, now, requested
            );
            
            log.info("令牌桶Lua脚本执行结果: {}", result);
            assert result != null && result == 1 : "令牌桶逻辑执行失败";
            
            // 清理测试数据
            redisTemplate.delete(tokenKey);
            redisTemplate.delete(timeKey);
            
            log.info("令牌桶Lua脚本测试通过！");
            
        } catch (Exception e) {
            log.error("令牌桶Lua脚本测试失败", e);
            throw e;
        }
    }
}
