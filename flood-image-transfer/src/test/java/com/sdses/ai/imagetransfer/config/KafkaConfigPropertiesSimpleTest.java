package com.sdses.ai.imagetransfer.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Kafka配置属性简单测试
 * 不依赖Spring上下文，直接测试配置类的功能
 *
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */
public class KafkaConfigPropertiesSimpleTest {

    private KafkaConsumerProperties consumerProperties;
    private KafkaTopicProperties topicProperties;

    @BeforeEach
    void setUp() {
        // 手动创建配置对象并设置测试数据
        consumerProperties = new KafkaConsumerProperties();
        consumerProperties.setGroupId("test-group");
        consumerProperties.setConcurrency(20);
        consumerProperties.setRetryConcurrency(5);
        consumerProperties.setMaxPollRecords(300);
        consumerProperties.setSessionTimeoutMs(30000);
        consumerProperties.setHeartbeatIntervalMs(3000);
        consumerProperties.setMaxPollIntervalMs(300000);
        consumerProperties.setFetchMinSize(1);
        consumerProperties.setFetchMaxWait(500);
        consumerProperties.setAutoOffsetReset("latest");
        consumerProperties.setEnableAutoCommit(false);
        consumerProperties.setTrustedPackages("com.sdses.ai.imagetransfer.entity");
        consumerProperties.setUseTypeInfoHeaders(false);

        // 设置异步处理配置
        KafkaConsumerProperties.AsyncProcessing asyncProcessing = new KafkaConsumerProperties.AsyncProcessing();
        asyncProcessing.setEnabled(true);
        asyncProcessing.setTimeoutSeconds(60);
        consumerProperties.setAsyncProcessing(asyncProcessing);

        // 设置重试配置
        KafkaConsumerProperties.Retry retry = new KafkaConsumerProperties.Retry();
        KafkaConsumerProperties.AsyncProcessing retryAsyncProcessing = new KafkaConsumerProperties.AsyncProcessing();
        retryAsyncProcessing.setEnabled(true);
        retryAsyncProcessing.setTimeoutSeconds(120);
        retry.setAsyncProcessing(retryAsyncProcessing);
        consumerProperties.setRetry(retry);

        // 创建主题配置
        topicProperties = new KafkaTopicProperties();
        topicProperties.setImageEvents("common_event_alarm");
        topicProperties.setRetryTopic("image_retry_topic");
        topicProperties.setResourceTopic("common_event_resource");
        topicProperties.setTestEvents("test-events");
        topicProperties.setTestString("test-string");
    }

    @Test
    public void testConsumerPropertiesBasicConfig() {
        // 验证基础配置
        assertEquals("test-group", consumerProperties.getGroupId());
        assertEquals(20, consumerProperties.getConcurrency());
        assertEquals(5, consumerProperties.getRetryConcurrency());
        assertEquals(300, consumerProperties.getMaxPollRecords());
        assertEquals(30000, consumerProperties.getSessionTimeoutMs());
        assertEquals(3000, consumerProperties.getHeartbeatIntervalMs());
        assertEquals(300000, consumerProperties.getMaxPollIntervalMs());
        assertEquals(1, consumerProperties.getFetchMinSize());
        assertEquals(500, consumerProperties.getFetchMaxWait());
        assertEquals("latest", consumerProperties.getAutoOffsetReset());
        assertFalse(consumerProperties.getEnableAutoCommit());
        assertEquals("com.sdses.ai.imagetransfer.entity", consumerProperties.getTrustedPackages());
        assertFalse(consumerProperties.getUseTypeInfoHeaders());
    }

    @Test
    public void testConsumerPropertiesAsyncConfig() {
        // 验证异步处理配置
        assertTrue(consumerProperties.isAsyncProcessingEnabled());
        assertEquals(60, consumerProperties.getAsyncTimeoutSeconds());
        
        // 验证重试异步处理配置
        assertTrue(consumerProperties.isRetryAsyncProcessingEnabled());
        assertEquals(120, consumerProperties.getRetryAsyncTimeoutSeconds());
    }

    @Test
    public void testTopicPropertiesConfig() {
        // 验证主题配置
        assertEquals("common_event_alarm", topicProperties.getImageEvents());
        assertEquals("image_retry_topic", topicProperties.getRetryTopic());
        assertEquals("common_event_resource", topicProperties.getResourceTopic());
        assertEquals("test-events", topicProperties.getTestEvents());
        assertEquals("test-string", topicProperties.getTestString());
    }

    @Test
    public void testConfigValidation() {
        // 验证消费者配置有效性
        assertTrue(consumerProperties.isConfigValid(), 
                "消费者配置应该是有效的");
        
        // 验证主题配置有效性
        assertTrue(topicProperties.isConfigValid(), 
                "主题配置应该是有效的");
    }

    @Test
    public void testInvalidConsumerConfig() {
        // 测试无效的消费者配置
        KafkaConsumerProperties invalidConfig = new KafkaConsumerProperties();
        invalidConfig.setConcurrency(0); // 无效值
        invalidConfig.setRetryConcurrency(5);
        invalidConfig.setMaxPollRecords(300);
        invalidConfig.setSessionTimeoutMs(30000);
        invalidConfig.setHeartbeatIntervalMs(3000);
        
        assertFalse(invalidConfig.isConfigValid(), "配置应该是无效的");
    }

    @Test
    public void testInvalidTopicConfig() {
        // 测试无效的主题配置
        KafkaTopicProperties invalidConfig = new KafkaTopicProperties();
        invalidConfig.setImageEvents(""); // 空值
        invalidConfig.setRetryTopic("retry-topic");
        invalidConfig.setResourceTopic("resource-topic");
        
        assertFalse(invalidConfig.isConfigValid(), "主题配置应该是无效的");
    }

    @Test
    public void testConfigDescription() {
        // 验证配置描述方法
        String consumerDesc = consumerProperties.getConfigDescription();
        assertNotNull(consumerDesc);
        assertTrue(consumerDesc.contains("Concurrency: 20"));
        assertTrue(consumerDesc.contains("Retry: 5"));
        assertTrue(consumerDesc.contains("MaxPoll: 300"));
        assertTrue(consumerDesc.contains("AsyncEnabled: true"));
        
        String topicDesc = topicProperties.getTopicsDescription();
        assertNotNull(topicDesc);
        assertTrue(topicDesc.contains("common_event_alarm"));
        assertTrue(topicDesc.contains("image_retry_topic"));
        assertTrue(topicDesc.contains("common_event_resource"));
    }

    @Test
    public void testRequiredAndOptionalTopics() {
        String[] requiredTopics = topicProperties.getRequiredTopics();
        assertNotNull(requiredTopics);
        assertEquals(3, requiredTopics.length);
        assertEquals("common_event_alarm", requiredTopics[0]);
        assertEquals("image_retry_topic", requiredTopics[1]);
        assertEquals("common_event_resource", requiredTopics[2]);

        String[] optionalTopics = topicProperties.getOptionalTopics();
        assertNotNull(optionalTopics);
        assertEquals(2, optionalTopics.length);
        assertEquals("test-events", optionalTopics[0]);
        assertEquals("test-string", optionalTopics[1]);
    }

    @Test
    public void testAsyncProcessingDefaults() {
        // 测试没有异步配置时的默认值
        KafkaConsumerProperties defaultConfig = new KafkaConsumerProperties();
        
        assertFalse(defaultConfig.isAsyncProcessingEnabled());
        assertEquals(60, defaultConfig.getAsyncTimeoutSeconds()); // 默认值
        
        assertFalse(defaultConfig.isRetryAsyncProcessingEnabled());
        assertEquals(120, defaultConfig.getRetryAsyncTimeoutSeconds()); // 默认值
    }

    @Test
    public void testSessionTimeoutValidation() {
        // 测试会话超时时间验证
        KafkaConsumerProperties config = new KafkaConsumerProperties();
        config.setConcurrency(10);
        config.setRetryConcurrency(5);
        config.setMaxPollRecords(300);
        config.setHeartbeatIntervalMs(3000);
        
        // 有效的会话超时时间（大于心跳间隔的3倍）
        config.setSessionTimeoutMs(10000);
        assertTrue(config.isConfigValid());
        
        // 无效的会话超时时间（小于心跳间隔的3倍）
        config.setSessionTimeoutMs(5000);
        assertFalse(config.isConfigValid());
    }

    @Test
    public void testConfigurationOutput() {
        // 输出配置信息用于验证
        System.out.println("=== Kafka Consumer Configuration Test ===");
        System.out.println(consumerProperties.getConfigDescription());
        
        System.out.println("\n=== Kafka Topic Configuration Test ===");
        System.out.println(topicProperties.getTopicsDescription());
        
        System.out.println("\n=== Configuration Validation Test ===");
        System.out.println("Consumer Config Valid: " + consumerProperties.isConfigValid());
        System.out.println("Topic Config Valid: " + topicProperties.isConfigValid());
        
        System.out.println("\n=== Async Processing Configuration Test ===");
        System.out.println("Async Processing Enabled: " + consumerProperties.isAsyncProcessingEnabled());
        System.out.println("Async Timeout: " + consumerProperties.getAsyncTimeoutSeconds() + "s");
        System.out.println("Retry Async Enabled: " + consumerProperties.isRetryAsyncProcessingEnabled());
        System.out.println("Retry Async Timeout: " + consumerProperties.getRetryAsyncTimeoutSeconds() + "s");
        
        // 验证输出不为空
        assertNotNull(consumerProperties.getConfigDescription());
        assertNotNull(topicProperties.getTopicsDescription());
    }
}
