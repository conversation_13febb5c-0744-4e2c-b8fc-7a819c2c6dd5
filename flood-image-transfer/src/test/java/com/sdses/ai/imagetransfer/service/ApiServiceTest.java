package com.sdses.ai.imagetransfer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdses.ai.imagetransfer.common.dto.QueryResultListDTO;
import com.sdses.ai.imagetransfer.entity.CommonEventResourceLast;
import com.sdses.ai.imagetransfer.exception.BusinessException;
import com.sdses.ai.imagetransfer.mapper.CommonEventResourceLastMapper;
import com.sdses.ai.imagetransfer.service.impl.ApiServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

/**
 * ApiService单元测试类
 * 测试分页查询业务逻辑的各种场景
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-15
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayName("ApiService单元测试")
class ApiServiceTest {

    @Mock
    private CommonEventResourceLastMapper commonEventResourceLastMapper;

    @InjectMocks
    private ApiServiceImpl apiService;

    private List<CommonEventResourceLast> mockDataList;

    @BeforeEach
    void setUp() {
        mockDataList = createMockData();
    }

    @Test
    @DisplayName("测试仅内码查询")
    void testQueryResultListWithInnerCodeOnly() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraIndexCode("inner001");
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 50);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getCurrent());
        assertEquals(10, result.getSize());
        assertEquals(50, result.getTotal());
        assertEquals(5, result.getPages());
        assertEquals(10, result.getRecords().size());

        // 验证查询条件
        verify(commonEventResourceLastMapper, times(1)).selectPage(
                argThat(page -> page.getCurrent() == 1 && page.getSize() == 10),
                argThat(wrapper -> {
                    // 这里可以验证查询条件是否正确构建
                    return wrapper != null;
                })
        );

        log.info("仅内码查询测试通过，返回记录数: {}", result.getRecords().size());
    }

    @Test
    @DisplayName("测试仅外码查询")
    void testQueryResultListWithOuterCodeOnly() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraForeignCode("outer001");
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 30);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(30, result.getTotal());
        assertEquals(3, result.getPages());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("仅外码查询测试通过");
    }

    @Test
    @DisplayName("测试内码+外码组合查询")
    void testQueryResultListWithBothCodes() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraForeignCode("outer001");
        dto.setCameraIndexCode("inner001");
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 5);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.getTotal());
        assertEquals(1, result.getPages());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("内码+外码组合查询测试通过");
    }

    @Test
    @DisplayName("测试空条件查询")
    void testQueryResultListWithNoConditions() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 1000);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1000, result.getTotal());
        assertEquals(100, result.getPages());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("空条件查询测试通过");
    }

    @Test
    @DisplayName("测试分页功能验证")
    void testPaginationFunctionality() {
        // 测试第2页，每页5条
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraIndexCode("inner");
        dto.setPage(2);
        dto.setPageSize(5);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList.subList(0, 5), 2, 5, 100);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证分页信息
        assertEquals(2, result.getCurrent());
        assertEquals(5, result.getSize());
        assertEquals(100, result.getTotal());
        assertEquals(20, result.getPages());

        // 验证分页参数传递
        verify(commonEventResourceLastMapper, times(1)).selectPage(
                argThat(page -> page.getCurrent() == 2 && page.getSize() == 5),
                any(LambdaQueryWrapper.class)
        );

        log.info("分页功能验证测试通过");
    }

    @Test
    @DisplayName("测试默认分页参数处理")
    void testDefaultPaginationParameters() {
        // 测试空的分页参数
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraIndexCode("inner001");
        // 不设置page和pageSize

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 50);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证默认值
        assertEquals(1, result.getCurrent());
        assertEquals(10, result.getSize());

        verify(commonEventResourceLastMapper, times(1)).selectPage(
                argThat(page -> page.getCurrent() == 1 && page.getSize() == 10),
                any(LambdaQueryWrapper.class)
        );

        log.info("默认分页参数处理测试通过");
    }

    @Test
    @DisplayName("测试页大小限制")
    void testPageSizeLimit() {
        // 测试超过最大限制的页大小
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraIndexCode("inner001");
        dto.setPage(1);
        dto.setPageSize(2000); // 超过1000的限制

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 1000, 50);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证页大小被限制为1000
        verify(commonEventResourceLastMapper, times(1)).selectPage(
                argThat(page -> page.getSize() == 1000),
                any(LambdaQueryWrapper.class)
        );

        log.info("页大小限制测试通过");
    }

    @Test
    @DisplayName("测试参数为null的异常情况")
    void testNullParameterException() {
        // 测试null参数
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            apiService.queryResultList(null);
        });

        assertEquals("查询参数不能为空", exception.getMessage());

        // 验证没有调用mapper
        verify(commonEventResourceLastMapper, never()).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("null参数异常测试通过");
    }

    @Test
    @DisplayName("测试数据库异常处理")
    void testDatabaseException() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraIndexCode("inner001");
        dto.setPage(1);
        dto.setPageSize(10);

        // 模拟数据库异常
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            apiService.queryResultList(dto);
        });

        assertTrue(exception.getMessage().contains("查询失败"));
        assertTrue(exception.getMessage().contains("数据库连接失败"));

        log.info("数据库异常处理测试通过");
    }

    /**
     * 创建模拟数据
     */
    private List<CommonEventResourceLast> createMockData() {
        List<CommonEventResourceLast> dataList = new ArrayList<>();
        
        for (int i = 1; i <= 10; i++) {
            CommonEventResourceLast entity = new CommonEventResourceLast();
            entity.setEventId("event-" + String.format("%03d", i));
            entity.setCameraForeignCode("outer" + String.format("%03d", i));
            entity.setCameraIndexCode("inner" + String.format("%03d", i));
            entity.setStatus("completed");
            entity.setEventTime(LocalDateTime.now().minusHours(i));
            entity.setStartTime(LocalDateTime.now().minusHours(i));
            entity.setEndTime(LocalDateTime.now().minusHours(i).plusMinutes(30));
            entity.setMinioImageUrl("http://minio.example.com/image" + i + ".jpg");
            entity.setMinioVideoUrl("http://minio.example.com/video" + i + ".mp4");
            
            dataList.add(entity);
        }
        
        return dataList;
    }

    /**
     * 创建模拟分页对象
     */
    private Page<CommonEventResourceLast> createMockPage(List<CommonEventResourceLast> records,
                                                         long current, long size, long total) {
        Page<CommonEventResourceLast> page = new Page<>(current, size);
        page.setRecords(records);
        page.setTotal(total);
        page.setPages((total + size - 1) / size);

        return page;
    }

    @Test
    @DisplayName("测试查询条件字符串处理")
    void testQueryConditionStringHandling() {
        // 测试包含空格的查询条件
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraForeignCode("  outer001  "); // 包含前后空格
        dto.setCameraIndexCode("  inner001  "); // 包含前后空格
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 10);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(10, result.getTotal());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("查询条件字符串处理测试通过");
    }

    @Test
    @DisplayName("测试空字符串查询条件")
    void testEmptyStringQueryConditions() {
        // 测试空字符串查询条件
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraForeignCode(""); // 空字符串
        dto.setCameraIndexCode("   "); // 只有空格
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 100);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(100, result.getTotal());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("空字符串查询条件测试通过");
    }

    @Test
    @DisplayName("测试仅开始时间查询")
    void testQueryResultListWithStartTimeOnly() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setStartTime("2025-07-15 10:00:00");
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 30);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(30, result.getTotal());

        // 验证时间解析
        assertNotNull(dto.getParsedStartTime());
        assertNull(dto.getParsedEndTime());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("仅开始时间查询测试通过");
    }

    @Test
    @DisplayName("测试仅结束时间查询")
    void testQueryResultListWithEndTimeOnly() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setEndTime("2025-07-15 18:00:00");
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 25);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(25, result.getTotal());

        // 验证时间解析
        assertNull(dto.getParsedStartTime());
        assertNotNull(dto.getParsedEndTime());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("仅结束时间查询测试通过");
    }

    @Test
    @DisplayName("测试时间范围查询")
    void testQueryResultListWithTimeRange() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setStartTime("2025-07-15 10:00:00");
        dto.setEndTime("2025-07-15 18:00:00");
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 15);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(15, result.getTotal());

        // 验证时间解析
        assertNotNull(dto.getParsedStartTime());
        assertNotNull(dto.getParsedEndTime());
        assertTrue(dto.hasTimeCondition());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("时间范围查询测试通过");
    }

    @Test
    @DisplayName("测试时间条件与其他查询条件组合")
    void testTimeConditionWithOtherConditions() {
        // 准备测试数据
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setCameraForeignCode("outdoor");
        dto.setCameraIndexCode("cam001");
        dto.setStartTime("2025-07-15 10:00:00");
        dto.setEndTime("2025-07-15 18:00:00");
        dto.setPage(1);
        dto.setPageSize(10);

        Page<CommonEventResourceLast> mockPage = createMockPage(mockDataList, 1, 10, 5);
        when(commonEventResourceLastMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        IPage<CommonEventResourceLast> result = apiService.queryResultList(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.getTotal());

        // 验证所有条件都设置了
        assertTrue(dto.hasTimeCondition());
        assertNotNull(dto.getParsedStartTime());
        assertNotNull(dto.getParsedEndTime());

        verify(commonEventResourceLastMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));

        log.info("时间条件与其他查询条件组合测试通过");
    }

    @Test
    @DisplayName("测试时间格式验证")
    void testTimeFormatValidation() {
        // 测试正确的时间格式
        QueryResultListDTO validDto = new QueryResultListDTO();
        validDto.setStartTime("2025-07-15 10:00:00");
        validDto.setEndTime("2025-07-15 18:00:00");

        // 应该不抛出异常
        assertDoesNotThrow(() -> validDto.validateTimeParameters());

        // 测试错误的开始时间格式
        QueryResultListDTO invalidStartDto = new QueryResultListDTO();
        invalidStartDto.setStartTime("2025-07-15 25:00:00"); // 无效小时

        IllegalArgumentException exception1 = assertThrows(IllegalArgumentException.class, () -> {
            invalidStartDto.validateTimeParameters();
        });
        assertTrue(exception1.getMessage().contains("开始时间格式错误"));

        // 测试错误的结束时间格式
        QueryResultListDTO invalidEndDto = new QueryResultListDTO();
        invalidEndDto.setEndTime("invalid-format");

        IllegalArgumentException exception2 = assertThrows(IllegalArgumentException.class, () -> {
            invalidEndDto.validateTimeParameters();
        });
        assertTrue(exception2.getMessage().contains("结束时间格式错误"));

        log.info("时间格式验证测试通过");
    }

    @Test
    @DisplayName("测试时间逻辑验证")
    void testTimeLogicValidation() {
        // 测试开始时间晚于结束时间
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setStartTime("2025-07-15 18:00:00");
        dto.setEndTime("2025-07-15 10:00:00"); // 结束时间早于开始时间

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            dto.validateTimeParameters();
        });
        assertEquals("开始时间必须早于结束时间", exception.getMessage());

        // 测试相同的开始时间和结束时间
        QueryResultListDTO sameTimeDto = new QueryResultListDTO();
        sameTimeDto.setStartTime("2025-07-15 10:00:00");
        sameTimeDto.setEndTime("2025-07-15 10:00:00");

        IllegalArgumentException exception2 = assertThrows(IllegalArgumentException.class, () -> {
            sameTimeDto.validateTimeParameters();
        });
        assertEquals("开始时间必须早于结束时间", exception2.getMessage());

        log.info("时间逻辑验证测试通过");
    }

    @Test
    @DisplayName("测试空时间字符串处理")
    void testEmptyTimeStringHandling() {
        QueryResultListDTO dto = new QueryResultListDTO();
        dto.setStartTime("   "); // 只有空格
        dto.setEndTime(""); // 空字符串

        // 应该不抛出异常（空字符串被忽略）
        assertDoesNotThrow(() -> dto.validateTimeParameters());

        // 验证解析结果为null
        assertNull(dto.getParsedStartTime());
        assertNull(dto.getParsedEndTime());
        assertFalse(dto.hasTimeCondition());

        log.info("空时间字符串处理测试通过");
    }
}
