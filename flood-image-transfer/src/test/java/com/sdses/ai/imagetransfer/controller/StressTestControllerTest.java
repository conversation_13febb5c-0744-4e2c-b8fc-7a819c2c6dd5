package com.sdses.ai.imagetransfer.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sdses.ai.imagetransfer.controller.TestController.StressTestRequest;
import com.sdses.ai.imagetransfer.controller.TestController.StressTestResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 压力测试控制器单元测试
 * 验证压力测试接口的基本功能（不依赖Spring上下文）
 *
 * <AUTHOR> Assistant
 * @since 2025-07-09
 */
public class StressTestControllerTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testStressTestRequestSerialization() throws Exception {
        // 测试请求参数序列化
        StressTestRequest request = new StressTestRequest();
        request.setMessageCount(100);
        request.setThreadCount(5);
        request.setSendIntervalMs(10);
        request.setTestType("normal");

        String json = objectMapper.writeValueAsString(request);
        System.out.println("Request JSON: " + json);

        // 验证JSON包含所有字段
        assertTrue(json.contains("\"messageCount\":100"));
        assertTrue(json.contains("\"threadCount\":5"));
        assertTrue(json.contains("\"sendIntervalMs\":10"));
        assertTrue(json.contains("\"testType\":\"normal\""));
    }

    @Test
    public void testStressTestResultSerialization() throws Exception {
        // 测试响应结果序列化
        StressTestResult result = new StressTestResult();
        result.setTotalMessages(1000);
        result.setSuccessCount(995);
        result.setFailedCount(5);
        result.setTotalTimeMs(5000);
        result.setThroughputPerSecond(199.0);
        result.setAvgLatencyMs(50.2);
        result.setMinLatencyMs(10);
        result.setMaxLatencyMs(200);
        result.setP50LatencyMs(45);
        result.setP95LatencyMs(120);
        result.setP99LatencyMs(180);
        result.setTestType("normal");
        result.setThreadCount(10);
        result.setSendIntervalMs(0);
        result.setTimestamp("2025-07-09T10:30:00");

        String json = objectMapper.writeValueAsString(result);
        System.out.println("Result JSON: " + json);

        // 验证JSON包含关键字段
        assertTrue(json.contains("\"totalMessages\":1000"));
        assertTrue(json.contains("\"successCount\":995"));
        assertTrue(json.contains("\"failedCount\":5"));
        assertTrue(json.contains("\"throughputPerSecond\":199.0"));
        assertTrue(json.contains("\"testType\":\"normal\""));
    }

    @Test
    public void testStressTestParameterValidation() {
        // 测试参数验证逻辑
        StressTestRequest invalidRequest = new StressTestRequest();
        invalidRequest.setMessageCount(200000); // 超过最大值100000
        invalidRequest.setThreadCount(10);

        // 验证参数范围
        assertTrue(invalidRequest.getMessageCount() > 100000, "消息数量应该超过最大值");
        assertTrue(invalidRequest.getThreadCount() <= 100, "线程数量应该在有效范围内");
    }

    @Test
    public void testStressTestParameterValidation2() {
        // 测试参数验证 - 线程数量超出范围
        StressTestRequest invalidRequest = new StressTestRequest();
        invalidRequest.setMessageCount(1000);
        invalidRequest.setThreadCount(200); // 超过最大值100

        // 验证参数范围
        assertTrue(invalidRequest.getMessageCount() <= 100000, "消息数量应该在有效范围内");
        assertTrue(invalidRequest.getThreadCount() > 100, "线程数量应该超过最大值");
    }

    @Test
    public void testDefaultValues() {
        // 测试默认值
        StressTestRequest request = new StressTestRequest();
        
        assertEquals(1000, request.getMessageCount());
        assertEquals(10, request.getThreadCount());
        assertEquals(0, request.getSendIntervalMs());
        assertEquals("normal", request.getTestType());
    }

    @Test
    public void testToStringMethods() {
        // 测试toString方法
        StressTestRequest request = new StressTestRequest();
        request.setMessageCount(500);
        request.setThreadCount(5);
        request.setSendIntervalMs(100);
        request.setTestType("no-image");

        String requestStr = request.toString();
        System.out.println("Request toString: " + requestStr);
        
        assertTrue(requestStr.contains("messageCount=500"));
        assertTrue(requestStr.contains("threadCount=5"));
        assertTrue(requestStr.contains("sendIntervalMs=100"));
        assertTrue(requestStr.contains("testType='no-image'"));

        StressTestResult result = new StressTestResult();
        result.setTotalMessages(500);
        result.setSuccessCount(490);
        result.setFailedCount(10);
        result.setTotalTimeMs(2500);
        result.setThroughputPerSecond(196.0);
        result.setAvgLatencyMs(25.5);
        result.setP50LatencyMs(20);
        result.setP95LatencyMs(50);
        result.setP99LatencyMs(80);
        result.setTestType("no-image");

        String resultStr = result.toString();
        System.out.println("Result toString: " + resultStr);
        
        assertTrue(resultStr.contains("totalMessages=500"));
        assertTrue(resultStr.contains("successCount=490"));
        assertTrue(resultStr.contains("failedCount=10"));
        assertTrue(resultStr.contains("throughputPerSecond=196.0"));
        assertTrue(resultStr.contains("testType='no-image'"));
    }

    @Test
    public void testRequestDeserialization() throws Exception {
        // 测试JSON反序列化
        String json = "{\"messageCount\":2000,\"threadCount\":20,\"sendIntervalMs\":50,\"testType\":\"large-data\"}";
        
        StressTestRequest request = objectMapper.readValue(json, StressTestRequest.class);
        
        assertEquals(2000, request.getMessageCount());
        assertEquals(20, request.getThreadCount());
        assertEquals(50, request.getSendIntervalMs());
        assertEquals("large-data", request.getTestType());
    }

    @Test
    public void testResultDeserialization() throws Exception {
        // 测试结果JSON反序列化
        String json = "{\"totalMessages\":1500,\"successCount\":1450,\"failedCount\":50," +
                     "\"totalTimeMs\":7500,\"throughputPerSecond\":193.3,\"avgLatencyMs\":45.2," +
                     "\"testType\":\"invalid-url\",\"threadCount\":15}";
        
        StressTestResult result = objectMapper.readValue(json, StressTestResult.class);
        
        assertEquals(1500, result.getTotalMessages());
        assertEquals(1450, result.getSuccessCount());
        assertEquals(50, result.getFailedCount());
        assertEquals(7500, result.getTotalTimeMs());
        assertEquals(193.3, result.getThroughputPerSecond(), 0.1);
        assertEquals(45.2, result.getAvgLatencyMs(), 0.1);
        assertEquals("invalid-url", result.getTestType());
        assertEquals(15, result.getThreadCount());
    }

    @Test
    public void testValidParameterRanges() {
        // 测试有效参数范围
        StressTestRequest validRequest = new StressTestRequest();
        validRequest.setMessageCount(5000);
        validRequest.setThreadCount(25);
        validRequest.setSendIntervalMs(20);
        validRequest.setTestType("normal");

        // 验证参数在有效范围内
        assertTrue(validRequest.getMessageCount() > 0 && validRequest.getMessageCount() <= 100000);
        assertTrue(validRequest.getThreadCount() > 0 && validRequest.getThreadCount() <= 100);
        assertTrue(validRequest.getSendIntervalMs() >= 0);
        assertNotNull(validRequest.getTestType());
    }

    @Test
    public void testBoundaryValues() {
        // 测试边界值
        StressTestRequest minRequest = new StressTestRequest();
        minRequest.setMessageCount(1);
        minRequest.setThreadCount(1);
        minRequest.setSendIntervalMs(0);

        StressTestRequest maxRequest = new StressTestRequest();
        maxRequest.setMessageCount(100000);
        maxRequest.setThreadCount(100);
        maxRequest.setSendIntervalMs(Integer.MAX_VALUE);

        // 验证边界值
        assertEquals(1, minRequest.getMessageCount());
        assertEquals(1, minRequest.getThreadCount());
        assertEquals(0, minRequest.getSendIntervalMs());

        assertEquals(100000, maxRequest.getMessageCount());
        assertEquals(100, maxRequest.getThreadCount());
        assertEquals(Integer.MAX_VALUE, maxRequest.getSendIntervalMs());
    }

    @Test
    public void testTestTypes() {
        // 测试不同的测试类型
        String[] testTypes = {"normal", "no-image", "invalid-url", "large-data"};
        
        for (String testType : testTypes) {
            StressTestRequest request = new StressTestRequest();
            request.setTestType(testType);
            assertEquals(testType, request.getTestType());
        }
    }
}
