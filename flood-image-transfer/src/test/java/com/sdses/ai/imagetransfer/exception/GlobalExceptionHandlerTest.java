package com.sdses.ai.imagetransfer.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sdses.ai.imagetransfer.config.GlobalExceptionHandler;
import com.sdses.ai.imagetransfer.model.ResVoT;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 全局异常处理器测试类
 * 验证各种异常的处理逻辑和响应格式
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
@Slf4j
@SpringBootTest
public class GlobalExceptionHandlerTest {

    @Autowired
    private ObjectMapper objectMapper;

    private final GlobalExceptionHandler exceptionHandler = new GlobalExceptionHandler();

    @Test
    public void testBusinessException() throws Exception {
        log.info("测试BusinessException处理");
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/test");
        
        BusinessException ex = BusinessException.of("BUSINESS_001", "业务处理失败", "测试上下文");
        
        ResponseEntity<ResVoT<Object>> response = exceptionHandler.handleBusinessException(ex, request);
        
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        
        ResVoT<Object> body = response.getBody();
        assertNotNull(body);
        assertFalse(body.getFlag());
        assertEquals("业务处理失败", body.getMessage());
        assertEquals("BUSINESS_001", body.getCode());
        assertEquals("测试上下文", body.getT());
        
        String json = objectMapper.writeValueAsString(body);
        log.info("BusinessException响应JSON: {}", json);
        
        log.info("✅ BusinessException处理测试通过");
    }

    @Test
    public void testApiException() throws Exception {
        log.info("测试ApiException处理");
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/external");
        
        ApiException ex = ApiException.of("API_001", "外部API调用失败");
        
        ResponseEntity<ResVoT<Object>> response = exceptionHandler.handleApiException(ex, request);
        
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        
        ResVoT<Object> body = response.getBody();
        assertNotNull(body);
        assertFalse(body.getFlag());
        assertEquals("外部API调用失败", body.getMessage());
        assertEquals("API_001", body.getCode());
        
        String json = objectMapper.writeValueAsString(body);
        log.info("ApiException响应JSON: {}", json);
        
        log.info("✅ ApiException处理测试通过");
    }

    @Test
    public void testDataNotFoundException() throws Exception {
        log.info("测试DataNotFoundException处理");
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/query/123");
        
        DataNotFoundException ex = DataNotFoundException.byId("Event", "123");
        
        ResponseEntity<ResVoT<Object>> response = exceptionHandler.handleDataNotFoundException(ex, request);
        
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        
        ResVoT<Object> body = response.getBody();
        assertNotNull(body);
        assertFalse(body.getFlag());
        assertEquals("Event with ID '123' not found", body.getMessage());
        assertEquals("DATA_NOT_FOUND", body.getCode());
        
        String json = objectMapper.writeValueAsString(body);
        log.info("DataNotFoundException响应JSON: {}", json);
        
        log.info("✅ DataNotFoundException处理测试通过");
    }

    @Test
    public void testValidationException() throws Exception {
        log.info("测试ValidationException处理");
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/validate");
        
        Map<String, String> validationErrors = new HashMap<>();
        validationErrors.put("eventId", "事件ID不能为空");
        validationErrors.put("cameraCode", "摄像头编码格式错误");
        
        ValidationException ex = ValidationException.of("参数验证失败", validationErrors);
        
        ResponseEntity<ResVoT<Object>> response = exceptionHandler.handleValidationException(ex, request);
        
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        
        ResVoT<Object> body = response.getBody();
        assertNotNull(body);
        assertFalse(body.getFlag());
        assertEquals("参数验证失败", body.getMessage());
        assertEquals("VALIDATION_ERROR", body.getCode());
        assertEquals(validationErrors, body.getT());
        
        String json = objectMapper.writeValueAsString(body);
        log.info("ValidationException响应JSON: {}", json);
        
        log.info("✅ ValidationException处理测试通过");
    }

    @Test
    public void testIllegalArgumentException() throws Exception {
        log.info("测试IllegalArgumentException处理");
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/illegal");
        
        IllegalArgumentException ex = new IllegalArgumentException("参数值不在允许范围内");
        
        ResponseEntity<ResVoT<Object>> response = exceptionHandler.handleIllegalArgumentException(ex, request);
        
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        
        ResVoT<Object> body = response.getBody();
        assertNotNull(body);
        assertFalse(body.getFlag());
        assertEquals("参数错误: 参数值不在允许范围内", body.getMessage());
        assertEquals("ILLEGAL_ARGUMENT", body.getCode());
        
        String json = objectMapper.writeValueAsString(body);
        log.info("IllegalArgumentException响应JSON: {}", json);
        
        log.info("✅ IllegalArgumentException处理测试通过");
    }

    @Test
    public void testRuntimeException() throws Exception {
        log.info("测试RuntimeException处理");
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/runtime");
        
        RuntimeException ex = new RuntimeException("运行时发生未知错误");
        
        ResponseEntity<ResVoT<Object>> response = exceptionHandler.handleRuntimeException(ex, request);
        
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        
        ResVoT<Object> body = response.getBody();
        assertNotNull(body);
        assertFalse(body.getFlag());
        assertEquals("系统运行时异常: 运行时发生未知错误", body.getMessage());
        assertEquals("RUNTIME_ERROR", body.getCode());
        
        String json = objectMapper.writeValueAsString(body);
        log.info("RuntimeException响应JSON: {}", json);
        
        log.info("✅ RuntimeException处理测试通过");
    }

    @Test
    public void testGenericException() throws Exception {
        log.info("测试Exception处理");
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/generic");
        
        Exception ex = new Exception("系统发生未知异常");
        
        ResponseEntity<ResVoT<Object>> response = exceptionHandler.handleGenericException(ex, request);
        
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        
        ResVoT<Object> body = response.getBody();
        assertNotNull(body);
        assertFalse(body.getFlag());
        assertEquals("系统内部错误，请联系管理员", body.getMessage());
        assertEquals("SYSTEM_ERROR", body.getCode());
        
        String json = objectMapper.writeValueAsString(body);
        log.info("Exception响应JSON: {}", json);
        
        log.info("✅ Exception处理测试通过");
    }

    @Test
    public void testExceptionFactoryMethods() {
        log.info("测试异常工厂方法");
        
        // 测试BusinessException工厂方法
        BusinessException businessEx1 = BusinessException.of("简单消息");
        assertEquals("500", businessEx1.getErrorCode());
        assertEquals("简单消息", businessEx1.getErrorMessage());
        
        BusinessException businessEx2 = BusinessException.of("CUSTOM_001", "自定义错误");
        assertEquals("CUSTOM_001", businessEx2.getErrorCode());
        assertEquals("自定义错误", businessEx2.getErrorMessage());
        
        // 测试ApiException工厂方法
        ApiException apiEx = ApiException.of("API调用失败");
        assertEquals("API_ERROR", apiEx.getErrorCode());
        assertEquals("API调用失败", apiEx.getErrorMessage());
        
        // 测试DataNotFoundException工厂方法
        DataNotFoundException dataEx = DataNotFoundException.byId("User", "123");
        assertEquals("DATA_NOT_FOUND", dataEx.getErrorCode());
        assertEquals("User with ID '123' not found", dataEx.getErrorMessage());
        
        // 测试ValidationException工厂方法
        ValidationException validationEx = ValidationException.parameterRequired("eventId");
        assertEquals("VALIDATION_ERROR", validationEx.getErrorCode());
        assertEquals("Parameter 'eventId' is required", validationEx.getErrorMessage());
        
        log.info("✅ 异常工厂方法测试通过");
    }
}
