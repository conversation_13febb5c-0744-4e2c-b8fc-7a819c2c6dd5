package com.sdses.ai.imagetransfer.async;

import com.sdses.ai.imagetransfer.entity.EventMessage;
import com.sdses.ai.imagetransfer.service.async.AsyncImageProcessingService;
import com.sdses.ai.imagetransfer.service.async.AsyncMessageProcessor;
import com.sdses.ai.imagetransfer.service.async.AsyncProcessingMonitor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 异步处理测试类
 * 测试JDK 21虚拟线程的异步处理功能
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-08
 */
@SpringBootTest
@ActiveProfiles("test")
class AsyncProcessingTest {

    @Autowired
    private AsyncImageProcessingService asyncImageProcessingService;

    @Autowired
    private AsyncMessageProcessor asyncMessageProcessor;

    @Autowired
    private AsyncProcessingMonitor asyncProcessingMonitor;

    @Test
    void testAsyncMessageProcessing() throws Exception {
        // 创建测试消息
        List<EventMessage> testMessages = createTestMessages(5);
        
        // 异步处理消息
        CompletableFuture<AsyncMessageProcessor.ProcessingResult> future = 
            asyncMessageProcessor.processMessagesAsync(testMessages);
        
        // 等待处理完成
        AsyncMessageProcessor.ProcessingResult result = future.get(30, TimeUnit.SECONDS);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.getTotalMessages());
        assertTrue(result.getProcessingTime().toMillis() >= 0);
        
        System.out.println("异步处理结果: " + result);
    }

    @Test
    void testSingleMessageAsync() throws Exception {
        // 创建单个测试消息
        EventMessage testMessage = createTestMessage("test-event-001");
        
        // 异步处理单个消息
        CompletableFuture<Boolean> future = 
            asyncImageProcessingService.processMessageAsync(testMessage);
        
        // 等待处理完成
        Boolean result = future.get(10, TimeUnit.SECONDS);
        
        // 验证结果
        assertNotNull(result);
        System.out.println("单个消息异步处理结果: " + result);
    }

    @Test
    void testBatchProcessing() throws Exception {
        // 创建大批量测试消息
        List<EventMessage> testMessages = createTestMessages(50);
        
        // 分批异步处理
        CompletableFuture<Integer> future = 
            asyncImageProcessingService.processBatchAsync(testMessages);
        
        // 等待处理完成
        Integer successCount = future.get(60, TimeUnit.SECONDS);
        
        // 验证结果
        assertNotNull(successCount);
        assertTrue(successCount >= 0);
        assertTrue(successCount <= testMessages.size());
        
        System.out.println("批处理结果: 成功处理 " + successCount + "/" + testMessages.size());
    }

    @Test
    void testPerformanceMonitoring() throws Exception {
        // 重置监控统计
        asyncProcessingMonitor.resetStats();
        
        // 处理一些消息
        List<EventMessage> testMessages = createTestMessages(10);
        CompletableFuture<AsyncMessageProcessor.ProcessingResult> future = 
            asyncMessageProcessor.processMessagesAsync(testMessages);
        
        future.get(30, TimeUnit.SECONDS);
        
        // 检查监控统计
        AsyncProcessingMonitor.PerformanceStats stats = asyncProcessingMonitor.getPerformanceStats();
        
        assertNotNull(stats);
        assertTrue(stats.getTotalProcessedMessages() >= 0);
        
        System.out.println("性能统计: " + stats);
    }

    @Test
    void testVirtualThreadSupport() {
        // 测试JDK 21虚拟线程支持
        boolean virtualThreadSupported = false;
        try {
            Thread.class.getMethod("ofVirtual");
            virtualThreadSupported = true;
        } catch (NoSuchMethodException e) {
            // JDK版本不支持虚拟线程
        }
        
        assertTrue(virtualThreadSupported, "当前JDK版本应该支持虚拟线程");
        System.out.println("虚拟线程支持: " + virtualThreadSupported);
        System.out.println("JDK版本: " + System.getProperty("java.version"));
    }

    @Test
    void testConcurrentProcessing() throws Exception {
        // 测试并发处理能力
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();
        
        // 创建多个并发任务
        for (int i = 0; i < 20; i++) {
            EventMessage message = createTestMessage("concurrent-test-" + i);
            CompletableFuture<Boolean> future = asyncImageProcessingService.processMessageAsync(message);
            futures.add(future);
        }
        
        // 等待所有任务完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0])
        );
        
        allOf.get(60, TimeUnit.SECONDS);
        
        // 统计成功数量
        long successCount = futures.stream()
            .mapToLong(future -> {
                try {
                    return future.get() ? 1 : 0;
                } catch (Exception e) {
                    return 0;
                }
            })
            .sum();
        
        System.out.println("并发处理结果: 成功 " + successCount + "/20");
        assertTrue(successCount >= 0);
    }

    @Test
    void testHealthCheck() {
        // 测试健康检查
        boolean isHealthy = asyncProcessingMonitor.isHealthy();
        
        System.out.println("异步处理系统健康状态: " + (isHealthy ? "健康" : "不健康"));
        
        // 获取详细统计信息
        AsyncProcessingMonitor.PerformanceStats stats = asyncProcessingMonitor.getPerformanceStats();
        System.out.println("详细统计: " + stats);
    }

    /**
     * 创建测试消息列表
     */
    private List<EventMessage> createTestMessages(int count) {
        List<EventMessage> messages = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            messages.add(createTestMessage("test-event-" + String.format("%03d", i)));
        }
        return messages;
    }

    /**
     * 创建单个测试消息
     */
    private EventMessage createTestMessage(String eventId) {
        EventMessage message = new EventMessage();
        message.setEventId(eventId);
        message.setImageUrl("https://100.192.2.82:18001/data/snapshot/20250707/16/336572c364464336a3c4e6b410fa2467/d7d4e6bdbf23836387d14ba9d3b69210.jpg");
        message.setCameraIndexCode("camera-001");
        message.setCameraChannelCode("channel-001");
        message.setEventTime(LocalDateTime.now());
        message.setInfo("测试消息 - " + eventId);
        return message;
    }
}
