package com.sdses.ai.imagetransfer.component;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Redis令牌桶测试类
 * <AUTHOR>
 * @create 2025-07-04
 */
@Slf4j
@SpringBootTest
public class RedisTokenBucketTest {

    @Autowired
    private RedisTokenBucket redisTokenBucket;

    /**
     * 测试基本的令牌获取功能
     */
    @Test
    public void testBasicTokenAcquisition() {
        String bucketKey = "test_bucket_" + System.currentTimeMillis();
        double rate = 10.0; // 每秒10个令牌
        int capacity = 100; // 桶容量100
        
        try {
            // 测试获取单个令牌
            boolean result1 = redisTokenBucket.tryAcquire(bucketKey, rate, capacity, 1);
            log.info("第一次获取令牌结果: {}", result1);
            assert result1 : "第一次获取令牌应该成功";
            
            // 测试获取多个令牌
            boolean result2 = redisTokenBucket.tryAcquire(bucketKey, rate, capacity, 5);
            log.info("第二次获取5个令牌结果: {}", result2);
            assert result2 : "第二次获取5个令牌应该成功";
            
            // 测试获取超过容量的令牌（应该失败）
            boolean result3 = redisTokenBucket.tryAcquire(bucketKey, rate, capacity, 200);
            log.info("获取200个令牌结果: {}", result3);
            assert !result3 : "获取超过容量的令牌应该失败";
            
            log.info("基本令牌获取测试通过！");
            
        } catch (Exception e) {
            log.error("基本令牌获取测试失败", e);
            throw e;
        }
    }

    /**
     * 测试令牌桶的速率限制
     */
    @Test
    public void testRateLimit() {
        String bucketKey = "rate_limit_test_" + System.currentTimeMillis();
        double rate = 2.0; // 每秒2个令牌
        int capacity = 5;  // 桶容量5
        
        try {
            // 快速消耗所有令牌
            for (int i = 0; i < capacity; i++) {
                boolean result = redisTokenBucket.tryAcquire(bucketKey, rate, capacity, 1);
                log.info("第{}次获取令牌: {}", i + 1, result);
                assert result : "前" + capacity + "次获取应该成功";
            }
            
            // 此时桶应该空了，再次获取应该失败
            boolean result = redisTokenBucket.tryAcquire(bucketKey, rate, capacity, 1);
            log.info("桶空后获取令牌: {}", result);
            assert !result : "桶空后获取令牌应该失败";
            
            log.info("速率限制测试通过！");
            
        } catch (Exception e) {
            log.error("速率限制测试失败", e);
            throw e;
        }
    }

    /**
     * 测试简化版的令牌获取方法
     */
    @Test
    public void testSimplifiedAcquisition() {
        String bucketKey = "simple_test_" + System.currentTimeMillis();
        double rate = 5.0;
        int capacity = 10;
        
        try {
            // 使用简化版方法获取令牌
            boolean result = redisTokenBucket.tryAcquire(bucketKey, rate, capacity);
            log.info("简化版获取令牌结果: {}", result);
            assert result : "简化版获取令牌应该成功";
            
            log.info("简化版令牌获取测试通过！");
            
        } catch (Exception e) {
            log.error("简化版令牌获取测试失败", e);
            throw e;
        }
    }

    /**
     * 测试参数边界情况
     */
    @Test
    public void testEdgeCases() {
        String bucketKey = "edge_test_" + System.currentTimeMillis();
        
        try {
            // 测试零值参数
            boolean result1 = redisTokenBucket.tryAcquire(bucketKey, 0.0, 10, 1);
            log.info("零速率获取令牌结果: {}", result1);
            assert !result1 : "零速率应该失败";
            
            // 测试负值参数
            boolean result2 = redisTokenBucket.tryAcquire(bucketKey, -1.0, 10, 1);
            log.info("负速率获取令牌结果: {}", result2);
            assert !result2 : "负速率应该失败";
            
            // 测试零容量
            boolean result3 = redisTokenBucket.tryAcquire(bucketKey, 1.0, 0, 1);
            log.info("零容量获取令牌结果: {}", result3);
            assert !result3 : "零容量应该失败";
            
            log.info("边界情况测试通过！");
            
        } catch (Exception e) {
            log.error("边界情况测试失败", e);
            throw e;
        }
    }
}
