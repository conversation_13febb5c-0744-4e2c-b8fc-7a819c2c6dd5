package com.sdses.ai.imagetransfer.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分布式锁工具类测试
 * 适配Spring Boot 3.5.0 + JDK 21
 * <AUTHOR>
 * @create 2025-07-07
 */
@Slf4j
@SpringBootTest
public class DistributedLockUtilTest {

    @Autowired
    private DistributedLockUtil distributedLockUtil;

    @Test
    public void testBasicLock() {
        log.info("测试基本分布式锁功能");
        
        String lockKey = "test:basic:lock";
        AtomicInteger counter = new AtomicInteger(0);
        
        // 第一次获取锁应该成功
        boolean result1 = distributedLockUtil.tryLockAndExecute(lockKey, 1, 5, () -> {
            counter.incrementAndGet();
            log.info("第一次执行，counter = {}", counter.get());
        });
        
        assertTrue(result1);
        assertEquals(1, counter.get());
        
        log.info("✅ 基本分布式锁测试通过");
    }

    @Test
    public void testConcurrentLock() throws InterruptedException {
        log.info("测试并发分布式锁功能");
        
        String lockKey = "test:concurrent:lock";
        AtomicInteger counter = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        
        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    boolean result = distributedLockUtil.tryLockAndExecute(lockKey, 1, 3, () -> {
                        int current = counter.incrementAndGet();
                        log.info("线程 {} 执行，counter = {}", threadId, current);
                        
                        // 模拟业务处理时间
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    });
                    
                    if (result) {
                        successCount.incrementAndGet();
                        log.info("线程 {} 获取锁成功", threadId);
                    } else {
                        log.info("线程 {} 获取锁失败", threadId);
                    }
                    
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        // 只有一个线程应该成功获取锁
        assertEquals(1, successCount.get());
        assertEquals(1, counter.get());
        
        log.info("✅ 并发分布式锁测试通过，成功执行次数: {}", successCount.get());
    }

    @Test
    public void testLockWithReturnValue() {
        log.info("测试带返回值的分布式锁");
        
        String lockKey = "test:return:value:lock";
        
        String result = distributedLockUtil.tryLockAndExecute(lockKey, 1, 5, () -> {
            log.info("执行带返回值的任务");
            return "执行成功";
        });
        
        assertNotNull(result);
        assertEquals("执行成功", result);
        
        log.info("✅ 带返回值的分布式锁测试通过，返回值: {}", result);
    }

    @Test
    public void testScheduledTaskLock() {
        log.info("测试定时任务分布式锁");
        
        String lockKey = DistributedLockUtil.generateScheduledTaskLockKey("test-task");
        AtomicInteger executeCount = new AtomicInteger(0);
        
        // 模拟多个实例同时执行定时任务
        boolean result1 = distributedLockUtil.executeScheduledTask(lockKey, () -> {
            executeCount.incrementAndGet();
            log.info("定时任务执行，count = {}", executeCount.get());
        });
        
        // 第二次执行应该失败（锁还在持有中）
        boolean result2 = distributedLockUtil.executeScheduledTask(lockKey, () -> {
            executeCount.incrementAndGet();
            log.info("第二次定时任务执行，count = {}", executeCount.get());
        });
        
        assertTrue(result1);
        assertFalse(result2);
        assertEquals(1, executeCount.get());
        
        log.info("✅ 定时任务分布式锁测试通过");
    }

    @Test
    public void testLockStatus() {
        log.info("测试锁状态检查");
        
        String lockKey = "test:status:lock";
        
        // 初始状态锁不存在
        assertFalse(distributedLockUtil.isLocked(lockKey));
        
        // 获取锁后状态应该为true
        boolean executed = distributedLockUtil.tryLockAndExecute(lockKey, 1, 10, () -> {
            assertTrue(distributedLockUtil.isLocked(lockKey));
            log.info("锁状态检查：锁存在");
            
            long remainingTime = distributedLockUtil.getLockRemainingTime(lockKey);
            log.info("锁剩余时间: {}ms", remainingTime);
            assertTrue(remainingTime > 0);
        });
        
        assertTrue(executed);
        
        // 等待一下确保锁已释放
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 锁释放后状态应该为false
        assertFalse(distributedLockUtil.isLocked(lockKey));
        
        log.info("✅ 锁状态检查测试通过");
    }

    @Test
    public void testLockKeyGeneration() {
        log.info("测试锁key生成");
        
        String scheduledKey = DistributedLockUtil.generateScheduledTaskLockKey("my-task");
        assertEquals("scheduled:lock:my-task", scheduledKey);
        
        String businessKey = DistributedLockUtil.generateBusinessLockKey("order", "12345");
        assertEquals("business:lock:order:12345", businessKey);
        
        log.info("✅ 锁key生成测试通过");
        log.info("定时任务锁key: {}", scheduledKey);
        log.info("业务锁key: {}", businessKey);
    }

    @Test
    public void testForceUnlock() {
        log.info("测试强制释放锁");
        
        String lockKey = "test:force:unlock";
        
        // 获取锁但不释放
        distributedLockUtil.tryLockAndExecute(lockKey, 1, 30, () -> {
            log.info("获取锁成功，模拟长时间持有");
            // 在这里不做任何操作，让锁继续持有
        });
        
        // 等待一下确保锁被持有
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 强制释放锁
        boolean unlocked = distributedLockUtil.forceUnlock(lockKey);
        assertTrue(unlocked);
        
        // 验证锁已被释放
        assertFalse(distributedLockUtil.isLocked(lockKey));
        
        log.info("✅ 强制释放锁测试通过");
    }

    @Test
    public void testJdk21VirtualThreads() throws InterruptedException {
        log.info("测试JDK 21虚拟线程与分布式锁的兼容性");
        
        String lockKey = "test:virtual:threads:lock";
        AtomicInteger counter = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        // 使用虚拟线程（JDK 21特性）
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                executor.submit(() -> {
                    try {
                        boolean result = distributedLockUtil.tryLockAndExecute(lockKey, 1, 2, () -> {
                            int current = counter.incrementAndGet();
                            log.info("虚拟线程 {} 执行，counter = {}", threadId, current);
                            
                            try {
                                Thread.sleep(500);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        });
                        
                        if (result) {
                            successCount.incrementAndGet();
                            log.info("虚拟线程 {} 获取锁成功", threadId);
                        } else {
                            log.info("虚拟线程 {} 获取锁失败", threadId);
                        }
                        
                    } finally {
                        latch.countDown();
                    }
                });
            }
            
            latch.await();
        }
        
        // 只有一个虚拟线程应该成功获取锁
        assertEquals(1, successCount.get());
        assertEquals(1, counter.get());
        
        log.info("✅ JDK 21虚拟线程兼容性测试通过，成功执行次数: {}", successCount.get());
    }
}
