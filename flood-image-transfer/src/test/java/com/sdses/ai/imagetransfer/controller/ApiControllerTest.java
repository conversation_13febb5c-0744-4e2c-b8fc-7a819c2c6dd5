package com.sdses.ai.imagetransfer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sdses.ai.imagetransfer.common.dto.QueryResultListDTO;
import com.sdses.ai.imagetransfer.entity.CommonEventResourceLast;
import com.sdses.ai.imagetransfer.exception.BusinessException;
import com.sdses.ai.imagetransfer.service.ApiService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ApiController单元测试类
 * 测试分页查询功能的各种场景
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-15
 */
@Slf4j
@WebMvcTest(ApiController.class)
@DisplayName("ApiController单元测试")
class ApiControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ApiService apiService;

    private List<CommonEventResourceLast> mockDataList;
    private IPage<CommonEventResourceLast> mockPage;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockDataList = createMockData();
        mockPage = createMockPage(mockDataList, 1, 10, 100);
    }

    @Test
    @DisplayName("测试仅内码查询")
    void testQueryResultListWithInnerCodeOnly() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 执行请求
        MvcResult result = mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("cameraIndexCode", "inner001")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.total").value(100))
                .andExpect(jsonPath("$.data.current").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.records").isArray())
                .andReturn();

        // 验证服务调用
        verify(apiService, times(1)).queryResultList(any(QueryResultListDTO.class));
        
        log.info("仅内码查询测试通过: {}", result.getResponse().getContentAsString());
    }

    @Test
    @DisplayName("测试仅外码查询")
    void testQueryResultListWithOuterCodeOnly() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 执行请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("cameraForeignCode", "outer001")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.total").value(100))
                .andExpect(jsonPath("$.data.records").isArray());

        // 验证服务调用
        verify(apiService, times(1)).queryResultList(any(QueryResultListDTO.class));
        
        log.info("仅外码查询测试通过");
    }

    @Test
    @DisplayName("测试内码+外码组合查询")
    void testQueryResultListWithBothCodes() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 执行请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("cameraForeignCode", "outer001")
                        .param("cameraIndexCode", "inner001")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.total").value(100));

        // 验证服务调用参数
        verify(apiService, times(1)).queryResultList(argThat(dto -> 
                "outer001".equals(dto.getCameraForeignCode()) && 
                "inner001".equals(dto.getCameraIndexCode()) &&
                dto.getPage().equals(1) &&
                dto.getPageSize().equals(10)
        ));
        
        log.info("内码+外码组合查询测试通过");
    }

    @Test
    @DisplayName("测试空条件查询（返回所有数据）")
    void testQueryResultListWithNoConditions() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 执行请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.total").value(100));

        // 验证服务调用参数
        verify(apiService, times(1)).queryResultList(argThat(dto -> 
                dto.getCameraForeignCode() == null && 
                dto.getCameraIndexCode() == null &&
                dto.getPage().equals(1) &&
                dto.getPageSize().equals(10)
        ));
        
        log.info("空条件查询测试通过");
    }

    @Test
    @DisplayName("测试分页功能验证")
    void testPaginationFunctionality() throws Exception {
        // 准备第二页数据
        IPage<CommonEventResourceLast> page2 = createMockPage(mockDataList, 2, 5, 100);
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(page2);

        // 执行请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "2")
                        .param("pageSize", "5")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.current").value(2))
                .andExpect(jsonPath("$.data.size").value(5))
                .andExpect(jsonPath("$.data.total").value(100))
                .andExpect(jsonPath("$.data.pages").value(20)); // 100/5=20页

        // 验证分页参数
        verify(apiService, times(1)).queryResultList(argThat(dto -> 
                dto.getPage().equals(2) && dto.getPageSize().equals(5)
        ));
        
        log.info("分页功能验证测试通过");
    }

    @Test
    @DisplayName("测试默认分页参数")
    void testDefaultPaginationParameters() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 执行请求（不传分页参数）
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.current").value(1))
                .andExpect(jsonPath("$.data.size").value(10));

        // 验证默认参数
        verify(apiService, times(1)).queryResultList(argThat(dto -> 
                dto.getPage().equals(1) && dto.getPageSize().equals(10)
        ));
        
        log.info("默认分页参数测试通过");
    }

    @Test
    @DisplayName("测试异常情况处理")
    void testExceptionHandling() throws Exception {
        // 模拟服务层异常
        when(apiService.queryResultList(any(QueryResultListDTO.class)))
                .thenThrow(new BusinessException("数据库连接失败"));

        // 执行请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("cameraIndexCode", "inner001")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("查询失败: 数据库连接失败"));

        verify(apiService, times(1)).queryResultList(any(QueryResultListDTO.class));
        
        log.info("异常情况处理测试通过");
    }

    @Test
    @DisplayName("测试参数边界值")
    void testParameterBoundaryValues() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 测试最大页大小
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("pageNum", "1")
                        .param("pageSize", "1000")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // 验证参数传递
        verify(apiService, times(1)).queryResultList(argThat(dto ->
                dto.getPageSize().equals(1000)
        ));

        log.info("参数边界值测试通过");
    }

    @Test
    @DisplayName("测试仅开始时间查询")
    void testQueryResultListWithStartTimeOnly() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 执行请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "2025-07-15 10:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"));

        // 验证服务调用参数
        verify(apiService, times(1)).queryResultList(argThat(dto ->
                "2025-07-15 10:00:00".equals(dto.getStartTime()) &&
                dto.getEndTime() == null
        ));

        log.info("仅开始时间查询测试通过");
    }

    @Test
    @DisplayName("测试仅结束时间查询")
    void testQueryResultListWithEndTimeOnly() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 执行请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("endTime", "2025-07-15 18:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"));

        // 验证服务调用参数
        verify(apiService, times(1)).queryResultList(argThat(dto ->
                dto.getStartTime() == null &&
                "2025-07-15 18:00:00".equals(dto.getEndTime())
        ));

        log.info("仅结束时间查询测试通过");
    }

    @Test
    @DisplayName("测试时间范围查询")
    void testQueryResultListWithTimeRange() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 执行请求
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "2025-07-15 10:00:00")
                        .param("endTime", "2025-07-15 18:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"));

        // 验证服务调用参数
        verify(apiService, times(1)).queryResultList(argThat(dto ->
                "2025-07-15 10:00:00".equals(dto.getStartTime()) &&
                "2025-07-15 18:00:00".equals(dto.getEndTime())
        ));

        log.info("时间范围查询测试通过");
    }

    @Test
    @DisplayName("测试时间格式错误处理")
    void testInvalidTimeFormatHandling() throws Exception {
        // 测试开始时间格式错误
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "2025-07-15 25:00:00") // 无效小时
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("开始时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式"));

        // 测试结束时间格式错误
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("endTime", "2025-13-15 10:00:00") // 无效月份
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("结束时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式"));

        // 测试完全错误的时间格式
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "invalid-time-format")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("开始时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式"));

        // 验证没有调用服务层
        verify(apiService, never()).queryResultList(any(QueryResultListDTO.class));

        log.info("时间格式错误处理测试通过");
    }

    @Test
    @DisplayName("测试时间逻辑错误处理")
    void testInvalidTimeLogicHandling() throws Exception {
        // 测试开始时间晚于结束时间
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "2025-07-15 18:00:00")
                        .param("endTime", "2025-07-15 10:00:00") // 结束时间早于开始时间
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("开始时间必须早于结束时间"));

        // 测试相同的开始时间和结束时间
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("startTime", "2025-07-15 10:00:00")
                        .param("endTime", "2025-07-15 10:00:00") // 相同时间
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("开始时间必须早于结束时间"));

        // 验证没有调用服务层
        verify(apiService, never()).queryResultList(any(QueryResultListDTO.class));

        log.info("时间逻辑错误处理测试通过");
    }

    @Test
    @DisplayName("测试时间条件与其他查询条件组合")
    void testTimeConditionWithOtherConditions() throws Exception {
        // 准备测试数据
        when(apiService.queryResultList(any(QueryResultListDTO.class))).thenReturn(mockPage);

        // 测试时间范围 + 外码 + 内码组合查询
        mockMvc.perform(get("/api/openAPI/queryResultList")
                        .param("cameraForeignCode", "outdoor")
                        .param("cameraIndexCode", "cam001")
                        .param("startTime", "2025-07-15 10:00:00")
                        .param("endTime", "2025-07-15 18:00:00")
                        .param("pageNum", "1")
                        .param("pageSize", "20")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"));

        // 验证所有参数都正确传递
        verify(apiService, times(1)).queryResultList(argThat(dto ->
                "outdoor".equals(dto.getCameraForeignCode()) &&
                "cam001".equals(dto.getCameraIndexCode()) &&
                "2025-07-15 10:00:00".equals(dto.getStartTime()) &&
                "2025-07-15 18:00:00".equals(dto.getEndTime()) &&
                dto.getPage().equals(1) &&
                dto.getPageSize().equals(20)
        ));

        log.info("时间条件与其他查询条件组合测试通过");
    }

    /**
     * 创建模拟数据
     */
    private List<CommonEventResourceLast> createMockData() {
        List<CommonEventResourceLast> dataList = new ArrayList<>();
        
        for (int i = 1; i <= 10; i++) {
            CommonEventResourceLast entity = new CommonEventResourceLast();
            entity.setEventId("event-" + String.format("%03d", i));
            entity.setCameraForeignCode("outer" + String.format("%03d", i));
            entity.setCameraIndexCode("inner" + String.format("%03d", i));
            entity.setStatus("completed");
            entity.setEventTime(LocalDateTime.now().minusHours(i));
            entity.setStartTime(LocalDateTime.now().minusHours(i));
            entity.setEndTime(LocalDateTime.now().minusHours(i).plusMinutes(30));
            entity.setMinioImageUrl("http://minio.example.com/image" + i + ".jpg");
            entity.setMinioVideoUrl("http://minio.example.com/video" + i + ".mp4");
            
            dataList.add(entity);
        }
        
        return dataList;
    }

    /**
     * 创建模拟分页对象
     */
    private IPage<CommonEventResourceLast> createMockPage(List<CommonEventResourceLast> records, 
                                                          long current, long size, long total) {
        Page<CommonEventResourceLast> page = new Page<>(current, size);
        page.setRecords(records);
        page.setTotal(total);
        page.setPages((total + size - 1) / size); // 计算总页数
        
        return page;
    }
}
