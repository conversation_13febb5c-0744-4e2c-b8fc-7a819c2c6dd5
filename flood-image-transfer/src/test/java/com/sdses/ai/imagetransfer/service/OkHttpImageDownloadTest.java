package com.sdses.ai.imagetransfer.service;

import com.sdses.ai.imagetransfer.service.impl.OKHttpServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.io.InputStream;

/**
 * OkHttp图片下载测试类
 * <AUTHOR>
 * @create 2025-07-04
 */
@Slf4j
@SpringBootTest
public class OkHttpImageDownloadTest {

    @Autowired
    private OKHttpServiceImpl okHttpService;

    /**
     * 测试下载图片流
     */
    @Test
    public void testDownloadImageStream() throws IOException {
        String imageUrl = "https://100.192.2.82:18001/data/snapshot/20250702/10/64e879cea80a445d813a077379edc4a7/daec25ad5246969f7c838998fce42aa3.jpg";

        try {
            InputStream inputStream = okHttpService.downloadImageStream(imageUrl);

            // 读取一些字节验证流是否有效
            byte[] buffer = new byte[1024];
            int bytesRead = inputStream.read(buffer);

            assert bytesRead > 0 : "未读取到数据";
            log.info("成功读取到 {} 字节的图片数据", bytesRead);

            inputStream.close();
            log.info("图片流测试通过！");

        } catch (Exception e) {
            log.error("图片流测试失败", e);
            throw e;
        }
    }
}
