# 使用 OpenJDK 21 作为基础镜像
FROM eclipse-temurin:21-jdk-alpine
# 设置环境变量
ENV APP_HOME=/app
ENV APP_LIB=/app/lib/
ENV SPRING_PROFILES_ACTIVE=prod
ENV APP_NAME=flood-image-transfer-1.0-SNAPSHOT.jar
ENV kafka_bootstrap_servers=hadoop-001:39092,hadoop-002:39092,hadoop-003:39092,hadoop-004:39092,hadoop-005:39092,hadoop-006:39092,hadoop-007:39092,hadoop-008:39092
ENV ai_discovery_event_topic=common_event_aidiscovery
ENV real_time_calculation_topic=feature_identification_threescheduling

RUN mkdir -p /app/config \
    && mkdir -p /app/lib \
    && mkdir -p /app/logs \
    && mkdir -p /app/data

# 安装 curl、vim 和 telnet
RUN apk add --no-cache \
    curl \
    vim \
    busybox-extras \
    && ln -s /bin/busybox-vi /bin/vi

# 创建应用目录
WORKDIR $APP_HOME
# 复制项目的 jar 文件到容器中
COPY $APP_NAME $APP_LIB/

#设置容器内时区和java项目内时区
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo "Asia/Shanghai" > /etc/timezone


# 暴露应用运行的端口   默认 Spring Boot 使用 26058 端口
EXPOSE 26058
# 运行应用，并指定配置文件
#CMD ["sh", "-c", "java -jar $APP_NAME --spring.profiles.active=$SPRING_PROFILES_ACTIVE --spring.nacosurl=$NACOS_URL"]
# 运行应用，并在启动时添加主机名和IP地址映射
CMD ["sh", "-c", "echo '************ hadoop-001' >> /etc/hosts && \
                   echo '*********** hadoop-008' >> /etc/hosts && \
                   echo '************ hadoop-005' >> /etc/hosts && \
                   echo '*********** hadoop-003' >> /etc/hosts && \
                   echo '*********** hadoop-007' >> /etc/hosts && \
                   echo '************ hadoop-002' >> /etc/hosts && \
                   echo '************ hadoop-004' >> /etc/hosts && \
                   echo '*********** hadoop-006' >> /etc/hosts && \
                   java -jar $APP_LIB --spring.profiles.active=$SPRING_PROFILES_ACTIVE  "]

