# 使用dockerfile打包
docker build -t flood-image-transfer:1 .
# 使用容器运行
docker run -d -p 25056:25056 flood-image-transfer:1
# 导出docker的容器
docker save -o flood-image-transfer-prod.tar flood-image-transfer:1

docker logs []
docker exec -it [] /bin/bash

# 如果你不知道容器的ID，可以使用以下命令查看所有容器：
docker ps -a
#查看容器的输出日志
docker logs <container_id>


docker tag flood-image-transfer:1 192.168.102.73/ai_flood_app/flood-image-transfer:1

docker push 192.168.102.73/ai_flood_app/flood-image-transfer:1



#
docker build -t flood-image-transfer:1 .
docker tag flood-image-transfer:1 harbor.sdses.com/ai_flood_app/flood-image-transfer:
docker push harbor.sdses.com/ai_flood_app/flood-image-transfer:1



#docker build -t harbor.sdses.com/ai_flood_app/flood-image-transfer:v06.29.15 .
#docker push harbor.sdses.com/ai_flood_app/flood-image-transfer:v06.29.15

docker login  harbor.sdses.com
admin,Harbor12345

