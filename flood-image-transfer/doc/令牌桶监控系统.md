# 令牌桶监控系统使用指南

## 概述

令牌桶监控系统提供了对分布式令牌桶的全面监控功能，包括实时状态监控、性能统计、告警机制和数据分析。

## 功能特性

### 1. 监控指标
- **令牌数量监控**: 实时监控各令牌桶的当前令牌数量
- **使用率统计**: 计算令牌桶的使用率和负载状态
- **成功/失败统计**: 记录令牌获取的成功和失败次数
- **等待时间分析**: 统计令牌获取的平均等待时间
- **负数检测**: 监控和统计令牌桶负数重置次数
- **热点分析**: 识别高频使用的令牌桶

### 2. 告警机制
- **负数令牌告警**: 当令牌桶出现负数时触发告警
- **高失败率告警**: 当失败率超过阈值时告警
- **长等待时间告警**: 当平均等待时间过长时告警

### 3. 数据访问方式
- **JMX接口**: 通过JMX暴露监控数据
- **HTTP接口**: 提供RESTful API访问监控数据
- **日志输出**: 详细的监控日志记录

## 配置说明

### 1. 基础配置
```yaml
# application.yml
image:
  download:
    monitor-interval: 5000  # 监控间隔（毫秒）

token-bucket:
  monitor:
    alert:
      failure-rate-threshold: 0.8      # 失败率告警阈值
      negative-reset-threshold: 5      # 负数重置告警阈值
    history:
      retention-hours: 24              # 历史数据保留时间
```

### 2. JMX配置
```yaml
management:
  endpoints:
    jmx:
      exposure:
        include: "*"
```

## 使用方式

### 1. HTTP接口访问

#### 获取监控报告
```bash
GET /api/monitor/report
```

#### 获取统计数据
```bash
GET /api/monitor/statistics
```

#### 获取指定令牌桶详情
```bash
GET /api/monitor/bucket/{bucketKey}
```

#### 获取热点令牌桶
```bash
GET /api/monitor/hotspots
```

#### 重置统计数据
```bash
POST /api/monitor/reset
```

#### 健康检查
```bash
GET /api/monitor/health
```

### 2. JMX接口访问

使用JConsole或其他JMX客户端连接到应用，查找MBean：
```
com.sdses.ai.imagetransfer:type=TokenBucketMonitor
```

可用的JMX操作：
- `getTotalAcquisitions()`: 获取总获取次数
- `getTotalFailures()`: 获取总失败次数
- `getTotalNegativeResets()`: 获取总负数重置次数
- `getOverallFailureRate()`: 获取整体失败率
- `generateMonitorReport()`: 生成监控报告
- `getBucketDetails(String bucketKey)`: 获取指定令牌桶详情
- `resetStatistics()`: 重置统计数据

### 3. 编程方式访问

```java
@Resource
private TokenBucketMonitor tokenBucketMonitor;

// 获取监控报告
String report = tokenBucketMonitor.generateMonitorReport();

// 获取统计数据
long totalAcquisitions = tokenBucketMonitor.getTotalAcquisitions();
double failureRate = tokenBucketMonitor.getOverallFailureRate();

// 获取指定令牌桶详情
String details = tokenBucketMonitor.getBucketDetails("bucket_key");
```

## 监控数据说明

### 1. 令牌桶状态
- **NORMAL**: 正常状态
- **HIGH_LOAD**: 高负载（使用率>90%）
- **MEDIUM_LOAD**: 中等负载（使用率>70%）
- **HIGH_FAILURE**: 高失败率
- **NEGATIVE_RESET**: 负数重置
- **IDLE**: 空闲状态
- **NOT_FOUND**: 令牌桶不存在
- **ERROR**: 错误状态

### 2. 关键指标
- **currentTokens**: 当前令牌数量
- **utilizationRate**: 使用率（0.0-1.0）
- **successCount**: 成功获取次数
- **failureCount**: 失败获取次数
- **failureRate**: 失败率（0.0-1.0）
- **averageWaitTime**: 平均等待时间（毫秒）
- **negativeResetCount**: 负数重置次数
- **requestFrequency**: 请求频率（次/分钟）

## 告警处理

### 1. 负数令牌告警
```
【告警】令牌桶 bucket_key 负数重置次数过多: 5, 地址: http://example.com
```
**处理建议**:
- 检查令牌桶配置是否合理
- 分析请求模式是否异常
- 考虑调整令牌桶容量或速率

### 2. 高失败率告警
```
【告警】令牌桶 bucket_key 失败率过高: 85.00%, 地址: http://example.com
```
**处理建议**:
- 检查下游服务是否正常
- 分析请求量是否超出预期
- 考虑增加令牌桶容量

### 3. 长等待时间告警
```
【告警】令牌桶 bucket_key 平均等待时间过长: 12000.00ms, 地址: http://example.com
```
**处理建议**:
- 检查令牌生成速率是否足够
- 分析是否存在请求堆积
- 考虑优化令牌桶参数

## 性能影响

监控系统采用以下策略最小化性能影响：

1. **异步收集**: 监控数据收集使用异步方式
2. **批量处理**: 支持批量处理监控数据
3. **内存缓存**: 监控数据在内存中缓存
4. **定期清理**: 自动清理过期的历史数据
5. **可配置间隔**: 可调整监控频率

## 故障排查

### 1. 监控数据不更新
- 检查监控定时任务是否正常运行
- 确认Redis连接是否正常
- 查看监控器日志是否有异常

### 2. JMX连接失败
- 确认JMX配置是否正确
- 检查防火墙设置
- 验证JMX端口是否开放

### 3. HTTP接口访问失败
- 确认应用是否正常启动
- 检查接口路径是否正确
- 查看应用日志是否有异常

## 最佳实践

1. **合理设置监控间隔**: 根据业务需求平衡监控精度和性能
2. **及时处理告警**: 建立告警响应机制
3. **定期分析数据**: 利用监控数据优化令牌桶配置
4. **备份监控配置**: 保存重要的监控配置和阈值设置
5. **监控系统监控**: 对监控系统本身进行监控
