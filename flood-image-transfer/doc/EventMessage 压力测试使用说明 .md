# EventMessage 压力测试使用说明

## 概述

本文档介绍如何使用新增的压力测试接口来测试 EventMessage 的发送性能。

## 接口信息

- **接口路径**: `POST /api/test/stress-test`
- **功能**: 批量发送 EventMessage 消息进行压力测试
- **返回**: 详细的性能统计信息

## 请求参数

```json
{
  "messageCount": 1000,        // 发送消息总数 (1-100000)
  "threadCount": 10,           // 并发线程数 (1-100)
  "sendIntervalMs": 0,         // 发送间隔毫秒数 (0表示无间隔)
  "testType": "normal"         // 测试类型
}
```

### 测试类型说明

1. **normal**: 正常消息，包含有效的图片URL
2. **no-image**: 无图片URL的消息，测试异常处理
3. **invalid-url**: 无效图片URL，测试错误处理
4. **large-data**: 包含大量Base64数据，测试大数据处理

## 响应结果

```json
{
  "code": 200,
  "message": "压力测试完成",
  "data": {
    "totalMessages": 1000,           // 总消息数
    "successCount": 995,             // 成功数量
    "failedCount": 5,                // 失败数量
    "totalTimeMs": 5000,             // 总耗时(毫秒)
    "throughputPerSecond": 199.0,    // 吞吐量(消息/秒)
    "avgLatencyMs": 50.2,            // 平均延迟(毫秒)
    "minLatencyMs": 10,              // 最小延迟(毫秒)
    "maxLatencyMs": 200,             // 最大延迟(毫秒)
    "p50LatencyMs": 45,              // P50延迟(毫秒)
    "p95LatencyMs": 120,             // P95延迟(毫秒)
    "p99LatencyMs": 180,             // P99延迟(毫秒)
    "testType": "normal",            // 测试类型
    "threadCount": 10,               // 线程数
    "sendIntervalMs": 0,             // 发送间隔
    "timestamp": "2025-07-09T10:30:00" // 测试时间戳
  }
}
```

## 使用示例

### 1. 基础压力测试

```bash
curl -X POST http://localhost:8080/api/test/stress-test \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 1000,
    "threadCount": 10,
    "sendIntervalMs": 0,
    "testType": "normal"
  }'
```

### 2. 高并发测试

```bash
curl -X POST http://localhost:8080/api/test/stress-test \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 10000,
    "threadCount": 50,
    "sendIntervalMs": 0,
    "testType": "normal"
  }'
```

### 3. 限流测试

```bash
curl -X POST http://localhost:8080/api/test/stress-test \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 1000,
    "threadCount": 5,
    "sendIntervalMs": 100,
    "testType": "normal"
  }'
```

### 4. 异常场景测试

```bash
curl -X POST http://localhost:8080/api/test/stress-test \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 500,
    "threadCount": 10,
    "sendIntervalMs": 0,
    "testType": "no-image"
  }'
```

## 性能指标说明

- **吞吐量 (throughputPerSecond)**: 每秒成功发送的消息数量
- **平均延迟 (avgLatencyMs)**: 单条消息发送的平均耗时
- **P50/P95/P99延迟**: 分别表示50%、95%、99%的请求在该时间内完成
- **成功率**: successCount / totalMessages

## 注意事项

1. **资源限制**: 
   - 最大消息数量: 100,000
   - 最大线程数: 100
   - 建议根据服务器性能调整参数

2. **Kafka配置**: 
   - 确保Kafka集群能够处理高并发写入
   - 监控Kafka的CPU、内存和磁盘使用情况

3. **网络带宽**: 
   - 大量消息发送会占用网络带宽
   - 建议在非生产环境进行测试

4. **监控建议**: 
   - 监控应用的CPU和内存使用情况
   - 观察Kafka的消费延迟
   - 检查日志中的错误信息

## 测试建议

1. **逐步增加负载**: 从小规模开始，逐步增加消息数量和并发数
2. **多种场景测试**: 测试不同的testType以验证各种场景
3. **长时间测试**: 进行长时间的稳定性测试
4. **资源监控**: 实时监控系统资源使用情况

## 故障排查

如果测试过程中出现问题，请检查：

1. **应用日志**: 查看详细的错误信息
2. **Kafka状态**: 确认Kafka集群正常运行
3. **网络连接**: 检查网络连接是否稳定
4. **资源使用**: 确认CPU、内存等资源充足
