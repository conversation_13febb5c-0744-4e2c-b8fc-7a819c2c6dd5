# Kafka消费者异步处理指南

## 概述

本文档介绍了基于JDK 21虚拟线程的Kafka消费者异步处理功能，该功能显著提升了消息处理的并发能力和系统吞吐量。

## 核心特性

### 1. JDK 21虚拟线程支持
- 利用JDK 21的虚拟线程特性，支持大量并发任务
- 虚拟线程轻量级，内存占用小，创建成本低
- 适合IO密集型任务，如图片下载和MinIO上传

### 2. 灵活的处理模式
- **异步模式**: 利用虚拟线程并行处理消息
- **同步模式**: 传统的串行处理模式（备选方案）
- **批处理**: 支持消息批量处理，提高效率

### 3. 完善的监控体系
- 实时性能统计
- 线程池状态监控
- 健康状态检查
- REST API接口

## 配置说明

### 异步处理配置

```yaml
# Kafka消费者异步处理配置
kafka:
  consumer:
    async-processing:
      enabled: true                   # 启用异步处理
      timeout-seconds: 60             # 异步处理超时时间
    retry:
      async-processing:
        enabled: true                 # 启用重试消息异步处理
        timeout-seconds: 120          # 重试消息异步处理超时时间

# 异步执行器配置
async:
  virtual-thread:
    enabled: true                     # 启用虚拟线程 (JDK 21特性)
    name-prefix: "kafka-async-"       # 虚拟线程名称前缀
  platform-thread:
    core-pool-size: 10                # 平台线程核心池大小
    max-pool-size: 50                 # 平台线程最大池大小
  processing:
    timeout-seconds: 300              # 处理超时时间
    batch-size: 100                   # 批处理大小
    parallel-enabled: true            # 启用并行处理
  monitoring:
    enabled: true                     # 启用监控
    report-interval-minutes: 5        # 监控报告间隔
```

### 环境差异化配置

#### 开发环境
```yaml
async:
  processing:
    batch-size: 50                    # 较小的批处理大小
    timeout-seconds: 180              # 较短的超时时间
  platform-thread:
    max-pool-size: 20                 # 较小的线程池
```

#### 生产环境
```yaml
async:
  processing:
    batch-size: 200                   # 较大的批处理大小
    timeout-seconds: 600              # 较长的超时时间
  platform-thread:
    max-pool-size: 100                # 较大的线程池
```

## 使用方式

### 1. 消费者自动切换
消费者会根据配置自动选择处理模式：

```java
@KafkaListener(topics = "${spring.kafka.topics.image-events}")
public void listen(List<EventMessage> messages, Acknowledgment ack) {
    // 自动根据配置选择异步或同步处理
    if (asyncProcessingEnabled) {
        processMessagesAsync(messages, ack);
    } else {
        processMessagesSync(messages, ack);
    }
}
```

### 2. 手动异步处理
也可以直接调用异步处理服务：

```java
@Autowired
private AsyncMessageProcessor asyncMessageProcessor;

public void processMessages(List<EventMessage> messages) {
    CompletableFuture<ProcessingResult> future = 
        asyncMessageProcessor.processMessagesAsync(messages);
    
    future.thenAccept(result -> {
        log.info("处理完成: {}", result);
    });
}
```

## 监控和管理

### REST API接口

#### 1. 获取性能统计
```bash
GET /api/async/stats
```

响应示例：
```json
{
  "totalProcessedMessages": 1000,
  "totalSuccessMessages": 950,
  "totalFailedMessages": 50,
  "successRate": "95.00%",
  "averageProcessingTimeMs": "150.25",
  "threadPoolCount": 3
}
```

#### 2. 获取线程池状态
```bash
GET /api/async/thread-pools
```

#### 3. 健康检查
```bash
GET /api/async/health
```

#### 4. 重置统计信息
```bash
POST /api/async/reset-stats
```

#### 5. 获取系统信息
```bash
GET /api/async/system-info
```

### 监控指标

#### 性能指标
- **总处理消息数**: 累计处理的消息总数
- **成功率**: 成功处理的消息比例
- **平均处理时间**: 每条消息的平均处理时间
- **吞吐量**: 每秒处理的消息数量

#### 系统指标
- **线程池状态**: 活跃线程数、总线程数
- **内存使用**: JVM内存使用情况
- **虚拟线程支持**: JDK版本和虚拟线程支持状态

## 性能优化

### 1. 批处理大小调优
- **小批次**: 延迟低，但吞吐量可能不足
- **大批次**: 吞吐量高，但延迟可能增加
- **建议**: 根据业务需求和系统资源调整

### 2. 超时时间设置
- **短超时**: 快速失败，避免资源占用
- **长超时**: 适合处理复杂任务
- **建议**: 根据任务复杂度设置合理超时

### 3. 并发控制
- **虚拟线程**: 适合IO密集型任务，可以创建大量线程
- **平台线程**: 适合CPU密集型任务，数量应控制在合理范围

## 故障排查

### 常见问题

#### 1. 虚拟线程不可用
**现象**: 启动时提示虚拟线程不支持
**原因**: JDK版本低于21
**解决**: 升级到JDK 21或设置 `async.virtual-thread.enabled=false`

#### 2. 异步处理超时
**现象**: 消息处理经常超时
**原因**: 超时时间设置过短或系统负载过高
**解决**: 增加超时时间或优化处理逻辑

#### 3. 内存使用过高
**现象**: JVM内存使用持续增长
**原因**: 批处理大小过大或并发数过高
**解决**: 减少批处理大小或降低并发数

### 日志监控
系统会输出详细的处理日志：

```
2025-07-08 10:30:15 INFO  - 接收到 100 条消息，异步处理模式: true
2025-07-08 10:30:16 INFO  - 异步处理完成: ProcessingResult{total=100, success=95, failed=5, rate=95.00%, time=1200ms, throughput=83.33 msg/s, parallel=true}
2025-07-08 10:35:15 INFO  - 异步处理性能报告: 总消息数=1000, 成功率=95.50%, 平均处理时间=145.30ms, 线程池数量=3
```

## 最佳实践

1. **合理配置批处理大小**: 根据消息大小和处理复杂度调整
2. **监控系统资源**: 定期检查CPU、内存使用情况
3. **设置合理超时**: 避免任务长时间占用资源
4. **启用监控**: 利用监控接口了解系统运行状态
5. **测试验证**: 在生产环境部署前充分测试

## 注意事项

1. **JDK版本要求**: 虚拟线程需要JDK 21+
2. **资源监控**: 虚拟线程虽然轻量，但仍需监控系统资源
3. **错误处理**: 异步处理中的异常需要妥善处理
4. **数据一致性**: 确保异步处理不影响数据一致性
