# asyncUpload 异步上传接口使用指南

## 功能概述

`asyncUpload` 接口提供异步图片上传处理功能，支持批量消息处理。接口采用立即返回机制，提交任务后立即返回唯一的 `taskId`，客户端可以通过该 `taskId` 查询任务执行状态。

## 接口信息

- **接口路径**: `/api/openAPI/async/upload`
- **请求方法**: `POST`
- **Content-Type**: `application/json`
- **认证方式**: 无需认证
- **响应格式**: JSON

## 核心特性

### 1. 立即返回机制
- ✅ 接口立即返回 `taskId`，不等待处理完成
- ✅ 后台异步处理消息列表
- ✅ 非阻塞调用，提高系统响应性

### 2. 任务状态跟踪
- ✅ 返回唯一的 `taskId` 用于状态查询
- ✅ 与 `asyncStatus` 接口完全兼容
- ✅ 支持任务执行进度监控

### 3. 批量处理支持
- ✅ 支持批量消息上传（最大1000条）
- ✅ 并行处理提高效率
- ✅ 完善的异常处理机制

## 请求参数

### Request Body

```json
[
  {
    "eventId": "event-001",
    "cameraForeignCode": "camera001",
    "cameraIndexCode": "index001",
    "imageUrl": "http://example.com/image1.jpg",
    "eventTime": "2025-07-15T10:30:00",
    "startTime": "2025-07-15T10:30:00",
    "endTime": "2025-07-15T10:35:00"
  },
  {
    "eventId": "event-002",
    "cameraForeignCode": "camera002",
    "cameraIndexCode": "index002",
    "imageUrl": "http://example.com/image2.jpg",
    "eventTime": "2025-07-15T11:00:00",
    "startTime": "2025-07-15T11:00:00",
    "endTime": "2025-07-15T11:05:00"
  }
]
```

### 参数说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| eventId | String | 是 | 事件唯一标识 |
| cameraForeignCode | String | 是 | 摄像头外码 |
| cameraIndexCode | String | 是 | 摄像头内码 |
| imageUrl | String | 是 | 图片URL地址 |
| eventTime | String | 是 | 事件时间 |
| startTime | String | 是 | 开始时间 |
| endTime | String | 是 | 结束时间 |

### 限制条件

- **消息数量**: 最大1000条消息
- **消息列表**: 不能为空
- **时间格式**: ISO 8601格式 (yyyy-MM-ddTHH:mm:ss)

## 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "success",
  "data": "a1b2c3d4e5f6g7h8i9j0"
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| message | String | 响应消息 |
| data | String | 任务ID，用于后续状态查询 |

### 错误响应

#### 消息列表为空
```json
{
  "code": 500,
  "message": "messageList不能为空",
  "data": null
}
```

#### 消息数量超限
```json
{
  "code": 500,
  "message": "messageList数量超过限制: 1000",
  "data": null
}
```

#### 提交任务失败
```json
{
  "code": 500,
  "message": "提交异步任务失败: 具体错误信息",
  "data": null
}
```

## 使用示例

### 1. JavaScript/Ajax 调用

```javascript
// 异步上传函数
async function asyncUpload(messageList) {
  try {
    const response = await fetch('/api/openAPI/async/upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(messageList)
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('任务提交成功，taskId:', result.data);
      return result.data; // 返回taskId
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('异步上传失败:', error);
    throw error;
  }
}

// 使用示例
const messageList = [
  {
    eventId: 'event-001',
    cameraForeignCode: 'camera001',
    cameraIndexCode: 'index001',
    imageUrl: 'http://example.com/image1.jpg',
    eventTime: '2025-07-15T10:30:00',
    startTime: '2025-07-15T10:30:00',
    endTime: '2025-07-15T10:35:00'
  }
];

asyncUpload(messageList)
  .then(taskId => {
    console.log('获得taskId:', taskId);
    // 可以用taskId查询状态
    return checkTaskStatus(taskId);
  })
  .catch(error => {
    console.error('上传失败:', error);
  });
```

### 2. 状态查询示例

```javascript
// 查询任务状态
async function checkTaskStatus(taskId) {
  try {
    const response = await fetch(`/api/openAPI/async/status?taskId=${taskId}`);
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('任务状态:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('状态查询失败:', error);
    throw error;
  }
}

// 轮询状态示例
async function pollTaskStatus(taskId, maxAttempts = 10) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const status = await checkTaskStatus(taskId);
      console.log(`第${i + 1}次查询，状态记录数:`, status.length);
      
      // 根据业务逻辑判断是否完成
      if (status.length > 0) {
        console.log('任务处理完成');
        return status;
      }
      
      // 等待2秒后再次查询
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`第${i + 1}次状态查询失败:`, error);
    }
  }
  
  console.warn('达到最大查询次数，停止轮询');
}
```

### 3. cURL 调用示例

```bash
# 基本调用
curl -X POST "http://localhost:26058/api/openAPI/async/upload" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "eventId": "event-001",
      "cameraForeignCode": "camera001",
      "cameraIndexCode": "index001",
      "imageUrl": "http://example.com/image1.jpg",
      "eventTime": "2025-07-15T10:30:00",
      "startTime": "2025-07-15T10:30:00",
      "endTime": "2025-07-15T10:35:00"
    }
  ]'

# 批量上传
curl -X POST "http://localhost:26058/api/openAPI/async/upload" \
  -H "Content-Type: application/json" \
  -d @batch_messages.json
```

### 4. Java 客户端调用

```java
// 使用RestTemplate
@Service
public class AsyncUploadClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public String asyncUpload(List<EventMessage> messageList) {
        String url = "http://localhost:26058/api/openAPI/async/upload";
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<List<EventMessage>> request = new HttpEntity<>(messageList, headers);
        
        ResVoT<String> response = restTemplate.postForObject(url, request, ResVoT.class);
        
        if (response != null && response.getCode() == 200) {
            return response.getData();
        } else {
            throw new RuntimeException("异步上传失败: " + 
                (response != null ? response.getMessage() : "未知错误"));
        }
    }
}
```

## 完整工作流程

### 1. 提交异步任务

```javascript
// 1. 准备消息数据
const messageList = [
  // ... 消息数据
];

// 2. 提交异步上传任务
const taskId = await asyncUpload(messageList);
console.log('任务已提交，taskId:', taskId);
```

### 2. 监控任务状态

```javascript
// 3. 定期查询任务状态
const checkInterval = setInterval(async () => {
  try {
    const status = await checkTaskStatus(taskId);
    console.log('当前状态:', status);
    
    // 根据业务逻辑判断是否完成
    if (isTaskCompleted(status)) {
      clearInterval(checkInterval);
      console.log('任务完成');
    }
  } catch (error) {
    console.error('状态查询失败:', error);
  }
}, 2000); // 每2秒查询一次
```

## 性能特性

### 1. 响应时间
- **接口响应**: < 100ms
- **任务提交**: 立即返回
- **处理能力**: 支持1000条消息批量处理

### 2. 并发支持
- **并发请求**: 支持多个并发上传任务
- **任务隔离**: 每个任务独立处理
- **资源管理**: 自动管理线程池资源

### 3. 可靠性
- **异常处理**: 完善的异常处理机制
- **任务跟踪**: 全程任务状态跟踪
- **失败重试**: 支持任务失败重试

## 最佳实践

### 1. 批量大小控制

```javascript
// 推荐：控制批量大小
function splitIntoBatches(messageList, batchSize = 100) {
  const batches = [];
  for (let i = 0; i < messageList.length; i += batchSize) {
    batches.push(messageList.slice(i, i + batchSize));
  }
  return batches;
}

// 分批提交
async function uploadInBatches(messageList) {
  const batches = splitIntoBatches(messageList);
  const taskIds = [];
  
  for (const batch of batches) {
    const taskId = await asyncUpload(batch);
    taskIds.push(taskId);
  }
  
  return taskIds;
}
```

### 2. 错误处理

```javascript
// 推荐：完善的错误处理
async function safeAsyncUpload(messageList, retryCount = 3) {
  for (let i = 0; i < retryCount; i++) {
    try {
      return await asyncUpload(messageList);
    } catch (error) {
      console.warn(`第${i + 1}次上传失败:`, error.message);
      
      if (i === retryCount - 1) {
        throw error; // 最后一次重试失败，抛出异常
      }
      
      // 指数退避重试
      await new Promise(resolve => 
        setTimeout(resolve, Math.pow(2, i) * 1000)
      );
    }
  }
}
```

### 3. 状态监控

```javascript
// 推荐：智能状态监控
class TaskMonitor {
  constructor(taskId) {
    this.taskId = taskId;
    this.checkCount = 0;
    this.maxChecks = 30; // 最多检查30次
  }
  
  async monitor() {
    return new Promise((resolve, reject) => {
      const interval = setInterval(async () => {
        try {
          this.checkCount++;
          const status = await checkTaskStatus(this.taskId);
          
          if (this.isCompleted(status)) {
            clearInterval(interval);
            resolve(status);
          } else if (this.checkCount >= this.maxChecks) {
            clearInterval(interval);
            reject(new Error('监控超时'));
          }
        } catch (error) {
          clearInterval(interval);
          reject(error);
        }
      }, 2000);
    });
  }
  
  isCompleted(status) {
    // 根据业务逻辑判断任务是否完成
    return status && status.length > 0;
  }
}

// 使用示例
const taskId = await asyncUpload(messageList);
const monitor = new TaskMonitor(taskId);
const finalStatus = await monitor.monitor();
```

## 注意事项

1. **任务ID保存**: 请妥善保存返回的 `taskId`，用于后续状态查询
2. **批量大小**: 建议单次上传不超过500条消息，以获得最佳性能
3. **状态查询**: 建议间隔2-5秒查询一次任务状态，避免频繁请求
4. **超时处理**: 设置合理的超时时间，避免无限等待
5. **错误重试**: 实现适当的重试机制，提高成功率

## 版本信息

- **接口版本**: 2.0.0
- **更新日期**: 2025-07-15
- **兼容性**: 向后兼容
- **依赖**: Spring Boot 3.5.0, JDK 21
