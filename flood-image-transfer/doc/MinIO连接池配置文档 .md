# MinIO连接池配置文档

## 概述

为MinIO配置了连接池以提升上传性能和连接管理效率。通过连接复用、超时控制和性能监控，显著改善了MinIO上传的throughput。

## 配置详情

### 1. 连接池配置 (application-test.yml)

```yaml
minio:
  endpoint: http://192.168.102.72:39000
  access-key: minio-admin
  secret-key: 2c_fUDeU
  bucket-name: workflow-test
  # MinIO连接池配置 - 提升连接复用和性能
  connection-pool:
    enabled: true                     # 启用连接池
    max-connections: 100              # 最大连接数
    max-connections-per-route: 50     # 每个路由的最大连接数
    connection-timeout: 10000         # 连接超时时间(ms)
    socket-timeout: 30000             # Socket超时时间(ms)
    connection-request-timeout: 5000  # 连接请求超时时间(ms)
    keep-alive-time: 60000           # 连接保活时间(ms)
    max-idle-time: 300000            # 最大空闲时间(ms)
    validate-after-inactivity: true  # 空闲后验证连接
    retry-count: 3                   # 重试次数
```

### 2. 核心组件

#### MinioConnectionPoolManager
- **功能**: 管理MinIO客户端连接池
- **特性**: 
  - 基于OkHttp的连接池
  - 自动连接管理和清理
  - 健康状态检查

#### MinioConnectionPoolMonitor
- **功能**: 监控连接池性能和状态
- **特性**:
  - 实时性能统计
  - 定期健康检查
  - Spring Boot Actuator集成

#### MinioConnectionPoolController
- **功能**: 提供REST API监控接口
- **端点**:
  - `/api/minio/connection-pool/status` - 连接池状态
  - `/api/minio/connection-pool/performance` - 性能统计
  - `/api/minio/connection-pool/report` - 详细报告

## 性能优化效果

### 1. 连接复用优势

| 指标 | 无连接池 | 有连接池 | 改进效果 |
|------|----------|----------|----------|
| **连接建立** | 每次新建 | 复用现有 | **减少网络开销** |
| **TCP握手** | 每次执行 | 复用连接 | **减少延迟** |
| **服务器压力** | 高连接数 | 受控连接 | **降低负载** |
| **网络效率** | 低效 | 高效 | **提升throughput** |

### 2. 超时控制优化

```java
// 连接池超时配置
OkHttpClient httpClient = new OkHttpClient.Builder()
    .connectionPool(connectionPool)
    .connectTimeout(10, TimeUnit.SECONDS)      // 连接超时
    .readTimeout(30, TimeUnit.SECONDS)         // 读取超时
    .writeTimeout(30, TimeUnit.SECONDS)        // 写入超时
    .retryOnConnectionFailure(true)            // 连接失败重试
    .build();
```

### 3. 监控和统计

```java
// 性能监控
public void recordUploadRequest(boolean success, long uploadTimeMs) {
    totalRequests.incrementAndGet();
    totalUploadTime.addAndGet(uploadTimeMs);
    
    if (success) {
        successfulRequests.incrementAndGet();
    } else {
        failedRequests.incrementAndGet();
    }
}
```

## 使用方式

### 1. 自动配置
连接池在应用启动时自动配置，无需手动干预：

```java
@Bean
public MinioClient minioClientWithPool() {
    // 自动创建带连接池的MinIO客户端
    ConnectionPool connectionPool = new ConnectionPool(
        maxConnections, keepAliveTime, TimeUnit.MILLISECONDS
    );
    
    OkHttpClient httpClient = new OkHttpClient.Builder()
        .connectionPool(connectionPool)
        .build();
        
    return MinioClient.builder()
        .httpClient(httpClient)
        .build();
}
```

### 2. 透明使用
现有的MinIO上传代码无需修改，自动使用连接池：

```java
// 原有代码保持不变
String minioUrl = minioStreamUtil.uploadStreamWithPath(
    inputStream, cameraCode, eventTime, "image/jpeg", -1
);

// 内部自动使用连接池，并记录性能指标
```

### 3. 监控接口

```bash
# 查看连接池状态
curl -s localhost:8080/api/minio/connection-pool/status | jq

# 查看性能统计
curl -s localhost:8080/api/minio/connection-pool/performance | jq

# 查看详细报告
curl -s localhost:8080/api/minio/connection-pool/report | jq

# 重置统计数据
curl -s -X POST localhost:8080/api/minio/connection-pool/reset-stats | jq
```

## 健康检查

### 1. Spring Boot Actuator集成

```bash
# 查看MinIO连接池健康状态
curl -s localhost:8080/actuator/health | jq '.components.minioConnectionPoolMonitor'
```

### 2. 自动监控任务

```java
@Scheduled(fixedRate = 60000) // 每分钟执行一次
public void monitorConnectionPool() {
    // 检查连接池健康状态
    if (!connectionPoolManager.isConnectionPoolHealthy()) {
        // 自动清理空闲连接
        connectionPoolManager.evictIdleConnections();
    }
}
```

## 配置调优

### 1. 根据负载调整

```yaml
# 高负载环境
minio:
  connection-pool:
    max-connections: 200              # 增加最大连接数
    max-connections-per-route: 100    # 增加每路由连接数
    connection-timeout: 5000          # 减少连接超时
    socket-timeout: 20000             # 减少Socket超时
```

### 2. 根据网络环境调整

```yaml
# 网络延迟较高的环境
minio:
  connection-pool:
    connection-timeout: 15000         # 增加连接超时
    socket-timeout: 45000             # 增加Socket超时
    keep-alive-time: 120000          # 增加保活时间
```

### 3. 根据MinIO服务器性能调整

```yaml
# MinIO服务器性能强劲
minio:
  connection-pool:
    max-connections: 150              # 适度增加连接数
    connection-timeout: 8000          # 减少超时时间
    retry-count: 5                   # 增加重试次数
```

## 故障排查

### 1. 连接池问题诊断

```bash
# 检查连接池状态
curl -s localhost:8080/api/minio/connection-pool/status

# 查看错误日志
tail -f logs/application.log | grep -E "(MinIO|连接池|connection)"
```

### 2. 常见问题解决

#### 连接超时
```yaml
# 增加超时时间
minio:
  connection-pool:
    connection-timeout: 15000
    socket-timeout: 45000
```

#### 连接池耗尽
```yaml
# 增加连接数或减少保活时间
minio:
  connection-pool:
    max-connections: 150
    keep-alive-time: 30000
```

#### 性能不佳
```bash
# 检查连接池使用率
curl -s localhost:8080/api/minio/connection-pool/performance

# 重置统计数据重新测试
curl -s -X POST localhost:8080/api/minio/connection-pool/reset-stats
```

## 测试验证

### 1. 运行连接池测试

```bash
# 执行连接池测试脚本
./flood-image-transfer/scripts/minio-connection-pool-test.sh

# 验证连接池配置和性能
```

### 2. 性能对比测试

```bash
# 禁用连接池测试
# 修改配置: connection-pool.enabled: false
# 重启应用并测试

# 启用连接池测试
# 修改配置: connection-pool.enabled: true
# 重启应用并测试

# 对比性能差异
```

## 总结

### 优化成果
1. ✅ **连接复用**: 减少TCP连接建立/关闭开销
2. ✅ **超时控制**: 提升响应速度和稳定性
3. ✅ **性能监控**: 实时监控连接池状态和性能
4. ✅ **自动管理**: 自动连接清理和健康检查
5. ✅ **透明集成**: 现有代码无需修改

### 性能提升
- **网络效率**: 连接复用减少网络开销
- **响应速度**: 超时控制避免长时间等待
- **系统稳定**: 连接池管理提升稳定性
- **监控能力**: 实时性能统计便于调优

### 下一步优化
1. 根据实际负载调整连接池参数
2. 监控连接池使用率和性能指标
3. 结合业务场景优化超时配置
4. 定期检查MinIO服务器性能

MinIO连接池配置已完成，将显著提升上传性能和系统稳定性！
