# 优雅停止功能使用指南

## 概述

本项目实现了完整的优雅停止功能，确保在应用关闭时能够等待正在运行的多线程异步任务完成后再停止，避免数据丢失和任务中断。

## 功能特性

### 1. 优雅停止管理器 (GracefulShutdownManager)
- 监听应用关闭事件 (ContextClosedEvent)
- 管理所有异步执行器的生命周期
- 支持配置化的超时时间控制
- 提供强制停止机制

### 2. 异步任务跟踪器 (AsyncTaskTracker)
- 实时跟踪所有活跃的异步任务
- 提供任务统计信息
- 支持等待任务完成功能
- 记录任务执行详情

### 3. 执行器集成
- 自动注册所有异步执行器到优雅停止管理器
- 支持虚拟线程和平台线程两种模式
- 包含以下执行器：
  - `kafkaAsyncExecutor` - Kafka消息异步处理
  - `imageDownloadExecutor` - 图片下载专用执行器
  - `minioUploadExecutor` - MinIO上传专用执行器

## 配置参数

在 `application.yml` 中添加以下配置：

```yaml
# 优雅停止配置
graceful-shutdown:
  enabled: true                           # 启用优雅停止功能
  timeout-seconds: 10                     # 优雅停止超时时间（秒）
  force-shutdown-after-timeout: true      # 超时后是否强制停止
```

### 配置说明

- `enabled`: 是否启用优雅停止功能，默认为 `true`
- `timeout-seconds`: 优雅停止的最大等待时间，默认为 `10` 秒
- `force-shutdown-after-timeout`: 超时后是否强制停止，默认为 `true`

## 优雅停止流程

### 1. 触发条件
- 应用接收到停止信号 (SIGTERM)
- Spring Boot 应用关闭事件
- 手动调用 `@PreDestroy` 方法

### 2. 停止步骤
1. **停止Kafka消费者**
   - 停止所有Kafka监听器容器
   - 等待容器完全停止（最多5秒）

2. **等待任务完成**
   - 检查当前活跃任务数量
   - 等待所有任务完成（最多 `timeout-seconds/2` 时间）
   - 记录未完成任务详情

3. **停止执行器**
   - 优雅停止所有注册的执行器
   - 等待执行器终止（最多 `timeout-seconds` 时间）
   - 超时后强制停止（如果启用）

### 3. 日志输出
```
2025-07-14 10:30:00.123 INFO  - 开始执行优雅停止流程，超时时间: 10 秒
2025-07-14 10:30:00.124 INFO  - 开始停止Kafka消费者...
2025-07-14 10:30:00.234 INFO  - 所有Kafka消费者已停止
2025-07-14 10:30:00.235 INFO  - 等待当前正在处理的任务完成...
2025-07-14 10:30:00.236 INFO  - 当前有 5 个活跃任务正在执行，等待完成...
2025-07-14 10:30:02.456 INFO  - 所有活跃任务已完成
2025-07-14 10:30:02.457 INFO  - 开始停止 3 个执行器服务...
2025-07-14 10:30:02.567 INFO  - 执行器已成功停止: ThreadPerTaskExecutor
2025-07-14 10:30:02.678 INFO  - 优雅停止流程完成，耗时: 2555 毫秒
```

## API接口

### 1. 获取优雅停止状态
```http
GET /api/graceful-shutdown/status
```

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "shutdownInProgress": false,
    "shutdownCompleted": false,
    "registeredExecutorCount": 3,
    "taskStatistics": {
      "activeTaskCount": 2,
      "totalTaskCount": 150,
      "completedTaskCount": 148,
      "timestamp": "2025-07-14T10:30:00"
    },
    "queryTime": "2025-07-14T10:30:00"
  }
}
```

### 2. 获取活跃任务详情
```http
GET /api/graceful-shutdown/active-tasks
```

### 3. 检查是否可以安全停止
```http
GET /api/graceful-shutdown/can-shutdown
```

### 4. 等待任务完成
```http
POST /api/graceful-shutdown/wait-tasks-completion?timeoutSeconds=30
```

### 5. 获取系统健康状态
```http
GET /api/graceful-shutdown/health
```

## 使用场景

### 1. 生产环境部署
在生产环境中，使用优雅停止确保：
- 正在处理的图片下载任务完成
- MinIO上传操作不被中断
- Kafka消息处理完整
- 数据库事务正确提交

### 2. 容器化部署
在Docker/Kubernetes环境中：
```dockerfile
# 确保容器接收SIGTERM信号
STOPSIGNAL SIGTERM

# 设置合适的停止超时时间
# docker run --stop-timeout=30 your-image
```

### 3. 监控和告警
通过API接口监控：
- 活跃任务数量
- 优雅停止状态
- 任务完成情况

## 最佳实践

### 1. 超时时间设置
- 开发环境：5-10秒
- 测试环境：10-15秒  
- 生产环境：15-30秒

### 2. 任务设计
- 确保任务具有合理的执行时间
- 避免长时间运行的任务
- 实现任务的可中断性

### 3. 监控建议
- 定期检查活跃任务数量
- 监控优雅停止完成时间
- 设置任务超时告警

## 故障排查

### 1. 优雅停止超时
**现象**: 应用停止时间过长
**排查**:
1. 检查活跃任务数量：`GET /api/graceful-shutdown/active-tasks`
2. 查看任务类型和执行时间
3. 调整超时时间配置

### 2. 任务无法完成
**现象**: 任务一直处于活跃状态
**排查**:
1. 检查任务是否正确调用 `asyncTaskTracker.completeTask()`
2. 查看任务执行日志
3. 检查是否存在死锁或阻塞

### 3. 强制停止频繁触发
**现象**: 经常触发强制停止
**排查**:
1. 增加超时时间
2. 优化任务执行效率
3. 检查资源竞争问题

## 注意事项

1. **任务跟踪**: 所有异步任务必须正确调用任务跟踪器的开始和完成方法
2. **超时设置**: 超时时间应该根据实际任务执行时间合理设置
3. **资源清理**: 确保在强制停止时正确清理资源
4. **日志记录**: 重要的停止事件都会记录详细日志
5. **测试验证**: 在部署前充分测试优雅停止功能

## 版本信息

- 支持的Spring Boot版本：3.5.0+
- 支持的JDK版本：21+
- 依赖的组件：Kafka, Redis, MinIO
