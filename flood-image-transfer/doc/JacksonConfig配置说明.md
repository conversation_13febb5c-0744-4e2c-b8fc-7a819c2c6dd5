# JacksonConfig配置说明

## 功能概述

JacksonConfig已配置为确保实体类转为JSON时，即使字段没有值或为空，字段也要存在。

## 核心配置

```java
@Configuration
public class JacksonConfig {
    
    @Bean
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper objectMapper = builder.build();
        
        // 🔥 核心配置：确保所有字段都包含在JSON中，即使为null或空值
        objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        
        return objectMapper;
    }
}
```

## JsonInclude.Include选项说明

- `ALWAYS`: 总是包含字段，无论值是什么（null、空字符串、空集合等）
- `NON_NULL`: 只包含非null字段（默认行为）
- `NON_EMPTY`: 只包含非空字段（排除null和空字符串、空集合）
- `NON_DEFAULT`: 只包含非默认值字段

## 配置效果示例

### 1. EventMessage序列化示例

**Java对象**:
```java
EventMessage message = new EventMessage();
message.setEventId("test-001");
message.setCameraIndexCode("camera-001");
// 其他字段保持null
```

**配置前的JSON输出**（NON_NULL）:
```json
{
  "event_id": "test-001",
  "camera_index_code": "camera-001"
}
```

**配置后的JSON输出**（ALWAYS）:
```json
{
  "camera_index_code": "camera-001",
  "camera_channel_code": null,
  "camera_foreign_code": null,
  "event_time": null,
  "source_system": null,
  "source_module": null,
  "info": null,
  "event_type": null,
  "event_id": "test-001",
  "image_url": null,
  "video_url": null,
  "image_base64": null,
  "only_picture": null
}
```

### 2. ResourceMessage序列化示例

**Java对象**:
```java
ResourceMessage message = ResourceMessage.builder()
    .eventId("test-resource-001")
    .status("processing")
    .imageRetry(0)
    .duration(1500L)
    // 其他字段保持null
    .build();
```

**配置后的JSON输出**:
```json
{
  "camera_index_code": null,
  "camera_channel_code": null,
  "camera_foreign_code": null,
  "start_time": null,
  "end_time": null,
  "event_time": null,
  "event_id": "test-resource-001",
  "minio_video_url": null,
  "minio_image_url": null,
  "status": "processing",
  "image_retry": 0,
  "video_retry": null,
  "image_msg": null,
  "video_msg": null,
  "duration": 1500,
  "source_system": null,
  "source_module": null,
  "info": null
}
```

## 不同值类型的处理

### null值
```java
message.setCameraIndexCode(null);
```
JSON输出: `"camera_index_code": null`

### 空字符串
```java
message.setCameraIndexCode("");
```
JSON输出: `"camera_index_code": ""`

### 0值
```java
message.setImageRetry(0);
```
JSON输出: `"image_retry": 0`

### 空集合
```java
message.setTags(new ArrayList<>());
```
JSON输出: `"tags": []`

## 与Kafka消息的配合

这个配置与之前的Kafka配置完美配合：

```java
// KafkaProducerConfig中也使用了相同的配置
@Bean
public ObjectMapper kafkaObjectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    mapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
    return mapper;
}
```

确保了：
1. **HTTP API响应**：通过JacksonConfig配置
2. **Kafka消息**：通过KafkaProducerConfig配置
3. **一致性**：两处使用相同的序列化策略

## 验证方法

### 1. 单元测试验证
```java
@Test
public void testAllFieldsIncluded() throws Exception {
    EventMessage message = new EventMessage();
    message.setEventId("test");
    // 其他字段保持null
    
    String json = objectMapper.writeValueAsString(message);
    
    // 验证所有@JsonProperty字段都存在
    assertTrue(json.contains("\"camera_index_code\""));
    assertTrue(json.contains("\"event_type\""));
    assertTrue(json.contains("\"image_url\""));
}
```

### 2. 日志验证
```java
log.info("序列化结果: {}", objectMapper.writeValueAsString(message));
```

### 3. API测试验证
调用任何返回实体类的API接口，检查响应JSON是否包含所有字段。

## 注意事项

1. **性能影响**: 包含所有字段会增加JSON大小，但提高了数据完整性
2. **前端兼容**: 前端可以依赖字段始终存在，简化null检查
3. **调试便利**: 完整的字段信息有助于调试和日志分析
4. **数据一致性**: 确保不同环境下的JSON格式一致

## 优势

1. **字段完整性**: 所有定义的字段都会出现在JSON中
2. **前端友好**: 前端不需要检查字段是否存在
3. **调试方便**: 可以清楚看到所有字段的状态
4. **Kafka兼容**: 与Kafka消息格式保持一致
5. **API一致性**: 所有API响应格式统一
