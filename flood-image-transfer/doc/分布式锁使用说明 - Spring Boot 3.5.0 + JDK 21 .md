# 分布式锁使用说明 - Spring Boot 3.5.0 + JDK 21

## 概述

本项目使用Redisson实现分布式锁，适配Spring Boot 3.5.0和JDK 21，主要用于控制多副本场景下定时任务的执行，确保同一时间只有一个实例执行特定任务。

## 技术栈

- **Spring Boot**: 3.5.0
- **JDK**: 21
- **Redisson**: 3.25.0
- **Redis**: 支持单机和集群模式

## 核心组件

### 1. RedissonConfig
- 配置Redisson客户端
- 适配Spring Boot 3.5.0的配置方式
- 支持JDK 21的虚拟线程

### 2. DistributedLockUtil
- 分布式锁工具类
- 提供多种锁操作方法
- 支持自定义等待时间和持有时间

### 3. ScheduledTask
- 定时任务类
- 使用分布式锁控制执行
- 支持多个不同的定时任务

## 配置要求

### 1. Redis配置 (application.yml)
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 3000
      connect-timeout: 3000
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000
```

**注意**: Redisson配置已通过`RedissonConfig.java`代码方式实现，
自动读取上述`spring.data.redis.*`配置项，无需额外的配置文件。

### 2. Maven依赖
```xml
<!-- Redisson Spring Boot Starter -->
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson-spring-boot-starter</artifactId>
    <version>3.25.0</version>
</dependency>

<!-- Spring Boot Data Redis -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

## 使用方式

### 1. 定时任务中使用

```java
@Scheduled(cron = "0 */15 * * * *")
public void scheduledTask() {
    String lockKey = DistributedLockUtil.generateScheduledTaskLockKey("scheduled-task-15min");
    
    boolean executed = distributedLockUtil.executeScheduledTask(lockKey, () -> {
        // 业务逻辑
        scheduledService.task();
    });
    
    if (executed) {
        log.info("定时任务执行成功");
    } else {
        log.info("定时任务跳过执行（其他实例正在执行）");
    }
}
```

### 2. 使用工具类

```java
// 基本用法
boolean success = distributedLockUtil.tryLockAndExecute(
    "business:lock:user:123", 
    3,    // 等待3秒
    300,  // 持有5分钟
    () -> {
        // 业务逻辑
    }
);

// 带返回值
String result = distributedLockUtil.tryLockAndExecute(
    "business:lock:order:456",
    2, 600,
    () -> {
        // 业务逻辑
        return "处理结果";
    }
);
```

### 3. JDK 21虚拟线程支持

```java
// 使用虚拟线程执行分布式锁任务
Thread.ofVirtual().name("VirtualTask").start(() -> {
    boolean result = distributedLockUtil.tryLockAndExecute(lockKey, 1, 60, () -> {
        // 业务逻辑
    });
});
```

## 锁Key命名规范

### 定时任务锁
```
scheduled:lock:{taskName}
```
示例：
- `scheduled:lock:image-cleanup`
- `scheduled:lock:data-sync`

### 业务锁
```
business:lock:{businessType}:{businessId}
```
示例：
- `business:lock:user:123`
- `business:lock:order:456`

## 配置参数说明

| 参数 | 说明 | 默认值 | 建议值 |
|------|------|--------|--------|
| waitTime | 等待获取锁的时间（秒） | 1 | 定时任务: 1-2秒<br>业务操作: 3-5秒 |
| leaseTime | 锁的持有时间（秒） | 600 | 定时任务: 任务执行时间的2-3倍<br>业务操作: 根据业务复杂度 |

## Spring Boot 3.5.0 特性

### 1. 配置属性变化
- 使用 `spring.data.redis.*` 替代 `spring.redis.*`
- 支持更灵活的超时配置格式

### 2. 依赖注入
- 使用 `@Autowired` 替代 `@Resource`
- 支持构造函数注入

### 3. 日志改进
- 使用结构化日志
- 支持更好的异常处理

## JDK 21 特性支持

### 1. 虚拟线程
- 支持虚拟线程执行分布式锁任务
- 提高并发性能
- 减少线程创建开销

### 2. 模式匹配
- 使用现代Java语法
- 提高代码可读性

### 3. 性能优化
- 利用JDK 21的性能改进
- 更好的垃圾回收

## 测试验证

### 1. 单元测试
```bash
mvn test -Dtest=DistributedLockUtilTest
```

### 2. 演示程序
```bash
java -jar flood-image-transfer.jar demo
```

### 3. 验证要点
- 锁的互斥性
- 锁的自动释放
- 虚拟线程兼容性
- 异常处理

## 最佳实践

### 1. 锁粒度控制
- 使用细粒度锁，避免全局锁
- 根据业务场景选择合适的锁key
- 避免锁竞争过于激烈

### 2. 超时时间设置
- waitTime不宜过长，避免线程阻塞
- leaseTime要大于业务执行时间
- 考虑网络延迟和系统负载

### 3. 异常处理
- 业务代码要有完善的异常处理
- 锁释放要在finally块中
- 记录详细的日志信息

### 4. 虚拟线程使用
- 适合I/O密集型任务
- 避免在虚拟线程中使用synchronized
- 使用Redisson锁替代传统锁

## 监控和运维

### 1. 锁状态监控
```java
// 检查锁状态
boolean isLocked = distributedLockUtil.isLocked(lockKey);

// 获取剩余时间
long remainingTime = distributedLockUtil.getLockRemainingTime(lockKey);

// 强制释放锁（谨慎使用）
boolean unlocked = distributedLockUtil.forceUnlock(lockKey);
```

### 2. 性能指标
- 锁获取成功率
- 锁持有时间分布
- 锁竞争情况

### 3. 告警设置
- 锁获取失败率过高
- 锁持有时间过长
- Redis连接异常

## 故障排查

### 1. 锁获取失败
- 检查Redis连接状态
- 检查锁key是否正确
- 检查等待时间设置

### 2. 锁未释放
- 检查业务代码是否有异常
- 检查锁的持有时间设置
- 使用forceUnlock强制释放

### 3. 性能问题
- 检查锁的竞争情况
- 优化锁的粒度
- 考虑使用虚拟线程

## 升级注意事项

### 从Spring Boot 2.x升级
1. 更新配置属性名称
2. 检查依赖版本兼容性
3. 测试虚拟线程功能

### 从JDK 8/11升级
1. 利用虚拟线程特性
2. 使用现代Java语法
3. 性能测试和优化

## 总结

本实现为Spring Boot 3.5.0 + JDK 21环境提供了完整的分布式锁解决方案，特别针对定时任务的多副本执行控制进行了优化，支持现代Java特性，提供了良好的性能和可维护性。
