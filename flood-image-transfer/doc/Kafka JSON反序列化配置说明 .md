# Kafka JSON反序列化配置说明

## 概述

本文档说明了如何配置Kafka消费者以支持JSON消息反序列化为指定的EventMessage结构。

## 主要修改

### 1. KafkaConfig.java 修改

- **消费者工厂**: 将StringDeserializer替换为JsonDeserializer
- **监听器容器工厂**: 更新泛型类型从`<String, String>`到`<String, EventMessage>`
- **JSON配置**: 添加信任包、默认类型等配置
- **备用工厂**: 保留字符串消费者工厂作为备用方案

### 2. EventMessage.java 增强

- 添加`@JsonIgnoreProperties(ignoreUnknown = true)`注解提高兼容性
- 保留所有现有的`@JsonAlias`注解支持字段映射
- 保持`@JsonInclude(JsonInclude.Include.ALWAYS)`确保所有字段都包含在JSON中

### 3. 配置属性扩展

在`KafkaConsumerProperties.java`中添加：
- `trustedPackages`: JSON反序列化信任包
- `useTypeInfoHeaders`: 是否使用类型信息头

### 4. YAML配置更新

在`application-dev.yml`和`application-prod.yml`中：
```yaml
spring:
  kafka:
    consumer:
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      trusted-packages: com.sdses.ai.imagetransfer.entity
      use-type-info-headers: false
```

## 使用方式

### 主要消费者

```java
@KafkaListener(topics = "${spring.kafka.topics.image-events}",
        containerFactory = "kafkaListenerContainerFactory")
public void listen(List<EventMessage> messages, Acknowledgment ack) {
    // 直接接收EventMessage对象，无需手动反序列化
    for (EventMessage message : messages) {
        log.info("处理消息: {}", message.getEventId());
        // 业务逻辑处理
    }
    ack.acknowledge();
}
```

### 备用字符串消费者

```java
@KafkaListener(topics = "${spring.kafka.topics.raw-messages}",
        containerFactory = "stringKafkaListenerContainerFactory")
public void listenRaw(List<String> messages, Acknowledgment ack) {
    // 处理原始字符串消息
    for (String message : messages) {
        // 手动解析JSON
        EventMessage eventMessage = objectMapper.readValue(message, EventMessage.class);
    }
    ack.acknowledge();
}
```

## 测试验证

### 启用测试消费者

在配置文件中添加：
```yaml
kafka:
  consumer:
    test:
      enabled: true
      string-enabled: true
spring:
  kafka:
    topics:
      test-events: test-event-message
      test-string: test-string-message
```

### 测试消息格式

发送到Kafka的JSON消息应符合EventMessage结构：
```json
{
  "event_id": "test-123",
  "event_type": "flood_detection",
  "camera_index_code": "CAM001",
  "event_time": "2025-07-08T10:30:00.000+08:00",
  "image_url": "http://example.com/image.jpg",
  "source_system": "flood-monitor"
}
```

## 容错机制

1. **忽略未知字段**: `@JsonIgnoreProperties(ignoreUnknown = true)`
2. **字段别名支持**: `@JsonAlias`注解支持多种字段名格式
3. **备用消费者**: 字符串消费者工厂作为降级方案
4. **错误处理**: 配置了重试和错误处理机制

## 性能优化

1. **批量处理**: 支持批量消费和处理
2. **并发控制**: 通过YAML配置动态调整并发数
3. **异步处理**: 支持异步消息处理模式
4. **连接池**: 复用Kafka连接和消费者实例

## 注意事项

1. **向后兼容**: 保留了字符串消费者工厂确保向后兼容
2. **安全性**: 通过`trusted-packages`限制反序列化的包范围
3. **监控**: 增加了详细的日志记录便于问题排查
4. **配置灵活**: 支持通过YAML文件动态配置各种参数
