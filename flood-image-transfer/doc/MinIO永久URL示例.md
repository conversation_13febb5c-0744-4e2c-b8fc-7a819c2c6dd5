# MinIO永久URL功能说明

## 功能概述

`uploadStreamWithPath()` 方法现在直接返回MinIO的永久图片地址，不再返回临时地址。

## 返回URL格式

```
http://192.168.102.72:39000/workflow-test/images/20250707/5e18d86759f84324a8763fabf62e4fc8/5e18d86759f84324a8763fabf62e4fc8_20250707085609.jpg
```

### URL结构说明

- `http://192.168.102.72:39000` - MinIO服务器地址（来自配置）
- `workflow-test` - 存储桶名称（来自配置）
- `images/20250707/5e18d86759f84324a8763fabf62e4fc8/` - 自动生成的目录结构
- `5e18d86759f84324a8763fabf62e4fc8_20250707085609.jpg` - 文件名

## 代码示例

### 1. 基本使用

```java
@Autowired
private MinioStreamUtil minioStreamUtil;

public String uploadImage(InputStream imageStream, String cameraCode) {
    try {
        LocalDateTime eventTime = LocalDateTime.now();
        String contentType = "image/jpeg";
        long size = -1; // 未知大小
        
        // 上传并获取永久URL
        String permanentUrl = minioStreamUtil.uploadStreamWithPath(
            imageStream, cameraCode, eventTime, contentType, size);
        
        log.info("图片上传成功，永久地址: {}", permanentUrl);
        return permanentUrl;
        
    } catch (Exception e) {
        log.error("图片上传失败", e);
        throw new RuntimeException("图片上传失败", e);
    }
}
```

### 2. 在ImageEventConsumerService中的使用

```java
// 原来的代码
String minioImageUrl = minioStreamUtil.uploadStreamWithPath(
    imageInputStream, cameraIndexCode, eventTime, "image/jpeg", -1);

// 现在返回的就是永久URL，格式如：
// http://192.168.102.72:39000/workflow-test/images/20250707/5e18d86759f84324a8763fabf62e4fc8/5e18d86759f84324a8763fabf62e4fc8_20250707085609.jpg
```

## 配置要求

确保application.yml中的MinIO配置正确：

```yaml
minio:
  endpoint: http://192.168.102.72:39000  # MinIO服务器地址
  bucket-name: workflow-test             # 存储桶名称
  access-key: your-access-key           # 访问密钥
  secret-key: your-secret-key           # 密钥
```

## 新增方法说明

### `getPermanentFileUrl(String objectName)`

直接根据对象名生成永久URL：

```java
String objectName = "images/20250707/camera001/camera001_20250707085609.jpg";
String permanentUrl = minioStreamUtil.getPermanentFileUrl(objectName);
// 返回: http://192.168.102.72:39000/workflow-test/images/20250707/camera001/camera001_20250707085609.jpg
```

### `uploadStreamOnly(InputStream, String, String, long)`

仅上传文件，不返回URL（内部使用）：

```java
// 内部方法，用于分离上传和URL生成逻辑
minioStreamUtil.uploadStreamOnly(inputStream, objectName, contentType, size);
```

## 优势

1. **永久访问**: 返回的URL不会过期，可以长期使用
2. **简洁格式**: URL格式简洁，易于理解和调试
3. **性能优化**: 避免了生成临时URL的开销
4. **兼容性**: 保持了原有方法签名，无需修改调用代码

## 注意事项

1. **存储桶权限**: 确保MinIO存储桶配置为公开读取，否则永久URL无法访问
2. **网络访问**: 确保客户端能够访问MinIO服务器地址
3. **URL格式**: 永久URL不包含任何认证参数，依赖存储桶的公开访问策略

## 测试验证

运行测试类验证功能：

```bash
mvn test -Dtest=MinioStreamUtilTest#testUploadStreamWithPathReturnsPermanentUrl
```

测试将验证：
- URL格式正确性
- 不包含临时签名参数
- 路径结构符合预期
