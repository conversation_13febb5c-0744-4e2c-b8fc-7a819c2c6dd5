# Redis配置说明

## 概述
本文档详细说明了项目中Redis的配置项和优化设置，包括连接池配置、超时设置等。

## 配置项详解

### 1. 基础连接配置

```yaml
spring:
  data:
    redis:
      host: ***********        # Redis服务器地址
      port: 6399               # Redis服务器端口
      password: Bigdata@2024   # Redis密码
      database: 0             # 使用的数据库索引
```

### 2. 超时配置

```yaml
timeout: 3000          # 命令执行超时时间(ms)
connect-timeout: 3000  # 连接超时时间(ms)
```

**配置说明：**
- `timeout`: Redis命令执行的超时时间，防止长时间阻塞
- `connect-timeout`: 建立连接的超时时间，避免连接建立过程中的长时间等待

**注意：** 在Redisson 3.16.4版本中，`timeout`配置已经涵盖了命令执行超时的功能，无需单独配置`command-timeout`。

### 3. 连接池配置

```yaml
pool:
  max-active: 20  # 连接池最大连接数
  max-idle: 10    # 连接池最大空闲连接数
  min-idle: 5     # 连接池最小空闲连接数
  max-wait: 3000  # 连接池最大阻塞等待时间(ms)
```

**配置说明：**
- `max-active`: 连接池中最大连接数，控制并发访问Redis的连接数量
- `max-idle`: 连接池中最大空闲连接数，避免过多空闲连接占用资源
- `min-idle`: 连接池中最小空闲连接数，保证基本的连接可用性
- `max-wait`: 当连接池耗尽时，获取连接的最大等待时间

## Redisson高级配置

### 1. 连接池设置
```java
config.useSingleServer()
    .setConnectionPoolSize(maxActive)           // 连接池大小
    .setConnectionMinimumIdleSize(minIdle)      // 最小空闲连接数
```

### 2. 超时设置
```java
config.useSingleServer()
    .setTimeout(timeout)                        // 命令超时
    .setConnectTimeout(connectTimeout)          // 连接超时
    .setIdleConnectionTimeout(10000)            // 空闲连接超时
```

### 3. 重试机制
```java
config.useSingleServer()
    .setRetryAttempts(3)                       // 重试次数
    .setRetryInterval(1500)                    // 重试间隔(ms)
```

### 4. 心跳检测
```java
config.useSingleServer()
    .setPingConnectionInterval(30000)          // 心跳检测间隔(ms)
    .setKeepAlive(true)                        // 保持连接活跃
```

## 环境差异配置

### 开发环境 (application-dev.yml)
- 连接池较小：max-active=20
- 适合开发调试的配置

### 生产环境 (application-prod.yml)
- 连接池较大：max-active=30
- 使用环境变量配置敏感信息
- 更高的并发处理能力

## 性能优化建议

### 1. 连接池大小调优
- 根据应用并发量调整max-active
- 监控连接池使用情况，避免连接不足或过多

### 2. 超时时间设置
- 根据网络环境调整超时时间
- 避免超时时间过长导致资源占用

### 3. 监控指标
- 连接池使用率
- 命令执行时间
- 连接建立成功率

## 故障排查

### 1. 连接超时
- 检查网络连通性
- 调整connect-timeout配置
- 检查Redis服务状态

### 2. 命令执行慢
- 检查Redis服务器性能
- 调整timeout配置
- 优化Redis命令

### 3. 连接池耗尽
- 增加max-active配置
- 检查连接泄漏
- 优化应用逻辑

## 版本兼容性说明

### Redisson 3.16.4 版本特性
- 使用`setTimeout()`方法设置命令执行超时
- 不支持`setCommandTimeout()`方法（该方法在更高版本中可用）
- 支持`setIdleConnectionTimeout()`设置空闲连接超时
- 连接池配置使用`setConnectionPoolSize()`和`setConnectionMinimumIdleSize()`

### API变更注意事项
如果升级Redisson版本，可能需要调整以下配置：
- 3.17.0+ 版本支持`setCommandTimeout()`方法
- 某些配置方法名可能有变化，请参考对应版本文档

## 安全建议

1. **密码管理**: 使用环境变量存储Redis密码
2. **网络安全**: 配置防火墙限制Redis访问
3. **权限控制**: 使用Redis ACL控制访问权限
4. **SSL/TLS**: 在生产环境中启用加密连接

## 配置模板

### 高并发场景
```yaml
pool:
  max-active: 50
  max-idle: 20
  min-idle: 10
  max-wait: 2000
timeout: 2000
```

### 低延迟场景
```yaml
pool:
  max-active: 30
  max-idle: 15
  min-idle: 8
  max-wait: 1000
timeout: 1000
connect-timeout: 1000
```
