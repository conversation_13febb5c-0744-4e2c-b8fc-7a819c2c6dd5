# Redis和Redisson配置说明

## 概述

本项目使用Redis作为缓存和分布式锁的存储，通过Redisson实现分布式锁功能。配置采用代码方式，统一使用Spring Boot的Redis配置项。

## 配置架构

### 1. Redis基础配置
使用Spring Boot标准的Redis配置：

```yaml
spring:
  data:
    redis:
      host: 127.0.0.1          # Redis服务器地址
      port: 6399               # Redis端口
      password: 123456         # Redis密码
      database: 15             # 数据库编号
      timeout: 3000            # 连接超时时间
      connect-timeout: 3000    # 连接超时时间
      pool:                    # 连接池配置
        max-active: 20         # 最大连接数
        max-idle: 10           # 最大空闲连接
        min-idle: 5            # 最小空闲连接
        max-wait: 3000         # 最大等待时间
```

### 2. Redisson配置
通过`RedissonConfig.java`代码方式配置，自动读取`spring.data.redis.*`配置项：

```java
@Configuration
public class RedissonConfig {
    
    @Value("${spring.data.redis.host:localhost}")
    private String host;
    
    @Value("${spring.data.redis.port:6379}")
    private String port;
    
    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
              .setAddress("redis://" + host + ":" + port)
              .setDatabase(database)
              .setConnectionPoolSize(64)
              // ... 其他配置
        return Redisson.create(config);
    }
}
```

## 配置优势

### 1. 统一配置源
- Redis和Redisson都使用`spring.data.redis.*`配置
- 避免配置重复和不一致
- 便于环境切换和管理

### 2. 代码配置方式
- 类型安全，编译时检查
- 支持复杂的配置逻辑
- 便于添加自定义配置

### 3. 环境差异化
不同环境可以有不同的配置：

#### 开发环境
```yaml
spring:
  data:
    redis:
      host: 127.0.0.1
      port: 6399
      password: 123456
      database: 15
```

#### 生产环境
```yaml
spring:
  data:
    redis:
      host: prod-redis-cluster
      port: 6379
      password: ${REDIS_PASSWORD}
      database: 0
```

## 功能说明

### 1. Redis基础功能
- **缓存**: 通过`RedisTemplate`实现
- **数据存储**: 支持各种数据类型
- **过期策略**: 支持TTL设置

### 2. Redisson分布式功能
- **分布式锁**: 控制并发访问
- **定时任务锁**: 多实例环境下的任务调度
- **分布式对象**: 分布式集合、队列等

## 使用示例

### 1. Redis基础操作
```java
@Autowired
private RedisTemplate<String, Object> redisTemplate;

public void setCache(String key, Object value) {
    redisTemplate.opsForValue().set(key, value, Duration.ofMinutes(30));
}

public Object getCache(String key) {
    return redisTemplate.opsForValue().get(key);
}
```

### 2. 分布式锁使用
```java
@Autowired
private DistributedLockUtil distributedLockUtil;

public void executeWithLock() {
    String lockKey = "business:lock:order:123";
    
    boolean executed = distributedLockUtil.tryLockAndExecute(
        lockKey, 
        1,    // 等待1秒
        300,  // 持有5分钟
        () -> {
            // 业务逻辑
            processOrder();
        }
    );
    
    if (executed) {
        log.info("业务处理成功");
    } else {
        log.info("获取锁失败，跳过处理");
    }
}
```

### 3. 定时任务锁
```java
@Scheduled(cron = "0 */15 * * * *")
public void scheduledTask() {
    String lockKey = DistributedLockUtil.generateScheduledTaskLockKey("data-sync");
    
    distributedLockUtil.executeScheduledTask(lockKey, () -> {
        // 定时任务逻辑
        syncData();
    });
}
```

## 监控和维护

### 1. 连接监控
- 监控Redis连接池状态
- 检查连接超时和失败情况
- 观察内存使用情况

### 2. 分布式锁监控
- 监控锁的获取和释放
- 检查锁的持有时间
- 观察锁竞争情况

### 3. 性能优化
- 合理设置连接池大小
- 优化锁的持有时间
- 使用合适的数据结构

## 故障排查

### 常见问题

#### 1. Redis连接失败
**现象**: 应用启动时Redis连接异常
**排查**:
- 检查Redis服务是否启动
- 验证host、port、password配置
- 检查网络连通性

#### 2. 分布式锁获取失败
**现象**: 锁一直获取不到
**排查**:
- 检查锁是否被其他实例持有
- 验证锁的过期时间设置
- 查看Redis中的锁状态

#### 3. 内存使用过高
**现象**: Redis内存持续增长
**排查**:
- 检查是否有大量未过期的key
- 验证TTL设置是否合理
- 清理无用的缓存数据

## 最佳实践

1. **合理设置TTL**: 避免内存泄漏
2. **使用连接池**: 提高性能和稳定性
3. **监控锁状态**: 避免死锁和长时间占用
4. **环境隔离**: 不同环境使用不同的database
5. **异常处理**: 妥善处理Redis连接异常

## 注意事项

1. **配置一致性**: 确保Redis和Redisson使用相同的连接配置
2. **密码安全**: 生产环境使用环境变量或配置中心
3. **版本兼容**: 确保Redisson版本与Redis版本兼容
4. **资源释放**: 确保连接和锁的正确释放

## 配置清理说明

### 已清理的冗余配置
1. **删除了`redisson.yml`文件**: 该文件未被使用
2. **移除了YAML中的`redisson.config`**: 避免与代码配置冲突
3. **统一使用`spring.data.redis.*`**: 简化配置管理

### 当前配置方式
- **Redis配置**: 通过`spring.data.redis.*`在YAML中配置
- **Redisson配置**: 通过`RedissonConfig.java`代码配置，读取Redis配置项
- **分布式锁**: 通过`DistributedLockUtil`工具类使用

这种配置方式更加清晰、统一，避免了配置冲突和重复。
