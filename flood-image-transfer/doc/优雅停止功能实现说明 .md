# 优雅停止功能实现说明

## 功能概述

本项目已成功实现了完整的优雅停止功能，确保在应用关闭时能够：
- 等待正在运行的多线程异步任务完成
- 优雅停止Kafka消费者
- 停止定时任务调度器
- 最多等待10秒钟，超过时间强制停止
- 提供详细的监控和控制接口

## 核心组件

### 1. GracefulShutdownManager
**位置**: `src/main/java/com/sdses/ai/imagetransfer/config/GracefulShutdownManager.java`

**功能**:
- 监听应用关闭事件 (ContextClosedEvent)
- 管理所有异步执行器和调度器的生命周期
- 协调优雅停止流程
- 支持配置化的超时控制

**关键特性**:
- 自动注册执行器服务
- 分步骤停止流程
- 超时强制停止机制
- 详细的日志记录

### 2. AsyncTaskTracker
**位置**: `src/main/java/com/sdses/ai/imagetransfer/service/async/AsyncTaskTracker.java`

**功能**:
- 实时跟踪所有活跃的异步任务
- 提供任务统计信息
- 支持等待任务完成功能
- 记录任务执行详情

**关键特性**:
- 线程安全的任务计数
- 任务详情记录
- 等待完成机制
- 统计信息导出

### 3. AsyncConfig (增强版)
**位置**: `src/main/java/com/sdses/ai/imagetransfer/config/AsyncConfig.java`

**功能**:
- 创建支持优雅停止的异步执行器
- 自动注册执行器到优雅停止管理器
- 支持虚拟线程和平台线程

**管理的执行器**:
- `kafkaAsyncExecutor` - Kafka消息异步处理
- `imageDownloadExecutor` - 图片下载专用执行器
- `minioUploadExecutor` - MinIO上传专用执行器

### 4. SchedulingConfig
**位置**: `src/main/java/com/sdses/ai/imagetransfer/config/SchedulingConfig.java`

**功能**:
- 创建支持优雅停止的定时任务调度器
- 配置调度器的优雅停止参数
- 自动注册调度器到优雅停止管理器

**管理的调度器**:
- `taskScheduler` - 通用定时任务调度器
- `distributedLockTaskScheduler` - 分布式锁定时任务调度器

### 5. GracefulShutdownController
**位置**: `src/main/java/com/sdses/ai/imagetransfer/controller/GracefulShutdownController.java`

**功能**:
- 提供优雅停止状态监控接口
- 支持手动控制和测试
- 实时查看活跃任务信息

## 配置参数

### application.yml 配置
```yaml
# 优雅停止配置
graceful-shutdown:
  enabled: true                           # 启用优雅停止功能
  timeout-seconds: 10                     # 优雅停止超时时间（秒）
  force-shutdown-after-timeout: true      # 超时后是否强制停止

# 定时任务配置
scheduling:
  pool-size: 5                            # 定时任务线程池大小
  thread-name-prefix: "scheduled-task-"   # 线程名前缀
  wait-for-tasks-to-complete-on-shutdown: true  # 关闭时等待任务完成
  await-termination-seconds: 10           # 等待终止的最大时间（秒）
```

## 优雅停止流程

### 触发方式
1. **应用关闭事件**: Spring Boot应用接收到停止信号时自动触发
2. **@PreDestroy**: Bean销毁前自动执行
3. **手动触发**: 通过API接口手动控制

### 执行步骤
1. **停止Kafka消费者** (最多5秒)
   - 停止所有Kafka监听器容器
   - 等待容器完全停止

2. **停止定时任务调度器** (最多2秒)
   - 停止所有注册的调度器
   - 等待调度器终止

3. **等待任务完成** (最多 timeout/2 秒)
   - 检查活跃任务数量
   - 等待所有任务完成
   - 记录未完成任务详情

4. **停止执行器** (最多 timeout 秒)
   - 优雅停止所有执行器
   - 等待执行器终止
   - 超时后强制停止

### 日志示例
```
2025-07-14 10:30:00.123 INFO  - 开始执行优雅停止流程，超时时间: 10 秒
2025-07-14 10:30:00.124 INFO  - 开始停止Kafka消费者...
2025-07-14 10:30:00.234 INFO  - 所有Kafka消费者已停止
2025-07-14 10:30:00.235 INFO  - 开始停止 2 个定时任务调度器...
2025-07-14 10:30:00.345 INFO  - 等待当前正在处理的任务完成...
2025-07-14 10:30:00.346 INFO  - 当前有 3 个活跃任务正在执行，等待完成...
2025-07-14 10:30:02.456 INFO  - 所有活跃任务已完成
2025-07-14 10:30:02.457 INFO  - 开始停止 3 个执行器服务...
2025-07-14 10:30:02.567 INFO  - 优雅停止流程完成，耗时: 2444 毫秒
```

## API接口

### 1. 获取优雅停止状态
```http
GET /api/graceful-shutdown/status
```

### 2. 获取活跃任务详情
```http
GET /api/graceful-shutdown/active-tasks
```

### 3. 检查是否可以安全停止
```http
GET /api/graceful-shutdown/can-shutdown
```

### 4. 等待任务完成
```http
POST /api/graceful-shutdown/wait-tasks-completion?timeoutSeconds=30
```

### 5. 获取系统健康状态
```http
GET /api/graceful-shutdown/health
```

## 测试工具

### 测试脚本
**位置**: `scripts/test-graceful-shutdown.sh`

**使用方法**:
```bash
# 获取状态
./scripts/test-graceful-shutdown.sh status

# 执行完整测试
./scripts/test-graceful-shutdown.sh test

# 执行完整测试并监控停止过程
./scripts/test-graceful-shutdown.sh full-test
```

## 集成说明

### 1. 异步任务集成
所有异步任务都已集成任务跟踪功能：
- `AsyncMessageProcessor` - 消息批处理任务
- `AsyncImageProcessingService` - 图片处理任务

### 2. 执行器注册
所有异步执行器都已自动注册到优雅停止管理器：
- 虚拟线程执行器
- 平台线程执行器
- 定时任务调度器

### 3. 任务跟踪
所有异步任务都会自动跟踪：
- 任务开始时调用 `asyncTaskTracker.startTask()`
- 任务完成时调用 `asyncTaskTracker.completeTask()`

## 部署建议

### 1. 容器化部署
```dockerfile
# 确保容器接收SIGTERM信号
STOPSIGNAL SIGTERM

# 设置合适的停止超时时间
# docker run --stop-timeout=30 your-image
```

### 2. Kubernetes部署
```yaml
spec:
  terminationGracePeriodSeconds: 30  # 设置优雅停止时间
```

### 3. 监控配置
- 监控活跃任务数量
- 设置优雅停止时间告警
- 记录停止完成时间

## 注意事项

1. **超时时间**: 根据实际任务执行时间合理设置超时参数
2. **任务设计**: 确保任务具有合理的执行时间，避免长时间运行
3. **资源清理**: 在强制停止时确保正确清理资源
4. **测试验证**: 部署前充分测试优雅停止功能
5. **日志监控**: 重要的停止事件都有详细日志记录

## 版本要求

- Spring Boot: 3.5.0+
- JDK: 21+
- 依赖组件: Kafka, Redis, MinIO

## 文档参考

- [优雅停止功能使用指南](优雅停止功能使用指南 )
- [异步处理指南](Kafka消费者异步处理指南 )
