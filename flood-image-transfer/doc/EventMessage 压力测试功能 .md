# EventMessage 压力测试功能

## 概述

本项目新增了专门用于 EventMessage 压力测试的功能，可以模拟大量并发消息发送，用于测试系统的性能和稳定性。

## 功能特性

### 🚀 核心功能
- **批量消息发送**: 支持发送1-100,000条消息
- **多线程并发**: 支持1-100个并发线程
- **发送速率控制**: 可配置消息发送间隔
- **多种测试场景**: 支持正常、异常、大数据等测试类型
- **详细性能统计**: 提供吞吐量、延迟分布等指标

### 📊 性能指标
- 总消息数和成功/失败统计
- 吞吐量 (消息/秒)
- 延迟统计 (平均、最小、最大、P50/P95/P99)
- 测试耗时和时间戳

### 🎯 测试场景
1. **normal**: 正常消息，包含有效图片URL
2. **no-image**: 无图片URL消息，测试异常处理
3. **invalid-url**: 无效图片URL，测试错误处理
4. **large-data**: 大数据消息，测试大数据处理能力

## 快速开始

### 1. 基础压力测试

```bash
curl -X POST http://localhost:8080/api/test/stress-test \
  -H "Content-Type: application/json" \
  -d '{
    "messageCount": 1000,
    "threadCount": 10,
    "sendIntervalMs": 0,
    "testType": "normal"
  }'
```

### 2. 使用脚本进行测试

```bash
# 运行预定义的测试示例
./flood-image-transfer/scripts/stress-test-examples.sh

# 监控系统资源
./flood-image-transfer/scripts/monitor-stress-test.sh 300 all
```

## API 接口

### 请求接口
- **URL**: `POST /api/test/stress-test`
- **Content-Type**: `application/json`

### 请求参数

| 参数 | 类型 | 默认值 | 范围 | 说明 |
|------|------|--------|------|------|
| messageCount | int | 1000 | 1-100000 | 发送消息总数 |
| threadCount | int | 10 | 1-100 | 并发线程数 |
| sendIntervalMs | int | 0 | ≥0 | 发送间隔(毫秒) |
| testType | string | "normal" | - | 测试类型 |

### 响应结果

```json
{
  "code": 200,
  "message": "压力测试完成",
  "data": {
    "totalMessages": 1000,
    "successCount": 995,
    "failedCount": 5,
    "totalTimeMs": 5000,
    "throughputPerSecond": 199.0,
    "avgLatencyMs": 50.2,
    "minLatencyMs": 10,
    "maxLatencyMs": 200,
    "p50LatencyMs": 45,
    "p95LatencyMs": 120,
    "p99LatencyMs": 180,
    "testType": "normal",
    "threadCount": 10,
    "sendIntervalMs": 0,
    "timestamp": "2025-07-09T10:30:00"
  }
}
```

## 文件结构

```
flood-image-transfer/
├── src/main/java/com/sdses/ai/imagetransfer/controller/
│   └── TestController.java                    # 压力测试控制器
├── src/test/java/com/sdses/ai/imagetransfer/controller/
│   └── StressTestControllerTest.java          # 单元测试
├── scripts/
│   ├── stress-test-examples.sh               # 测试示例脚本
│   └── monitor-stress-test.sh                # 监控脚本
├── doc/
│   └── stress-test-usage.md                  # 详细使用说明
└── README-stress-test.md                     # 本文件
```

## 测试建议

### 🔧 测试策略
1. **渐进式测试**: 从小规模开始，逐步增加负载
2. **多场景覆盖**: 测试不同的testType验证各种情况
3. **长期稳定性**: 进行长时间运行测试
4. **资源监控**: 实时监控系统资源使用情况

### 📈 性能调优
1. **线程数调优**: 根据CPU核数和系统负载调整
2. **批量大小**: 平衡吞吐量和延迟
3. **JVM参数**: 调整堆内存和GC参数
4. **Kafka配置**: 优化生产者和消费者配置

### ⚠️ 注意事项
1. **资源限制**: 确保系统有足够的CPU、内存和网络带宽
2. **Kafka容量**: 确保Kafka集群能处理高并发写入
3. **测试环境**: 建议在非生产环境进行大规模测试
4. **监控告警**: 设置适当的监控和告警机制

## 监控和分析

### 系统监控
- CPU使用率
- 内存使用率
- 网络带宽
- 磁盘I/O

### 应用监控
- JVM堆内存使用
- 线程数量
- GC频率和耗时
- 应用日志错误

### Kafka监控
- 生产者吞吐量
- 消费者延迟
- 分区负载均衡
- 集群健康状态

## 故障排查

### 常见问题
1. **连接超时**: 检查网络连接和Kafka配置
2. **内存不足**: 增加JVM堆内存或减少并发数
3. **消息积压**: 检查消费者处理能力
4. **性能下降**: 分析系统瓶颈和资源使用

### 日志分析
- 查看应用日志中的ERROR和WARN信息
- 监控Kafka客户端日志
- 分析GC日志识别内存问题

## 扩展功能

### 未来改进
- [ ] 支持自定义消息模板
- [ ] 添加实时性能图表
- [ ] 集成APM监控工具
- [ ] 支持分布式压力测试
- [ ] 添加自动化性能回归测试

### 集成建议
- 与CI/CD流水线集成
- 添加性能基准测试
- 集成监控告警系统
- 支持测试报告自动生成

## 联系方式

如有问题或建议，请联系开发团队或提交Issue。

---

**版本**: 1.0.0  
**更新时间**: 2025-07-09  
**作者**: AI Assistant
