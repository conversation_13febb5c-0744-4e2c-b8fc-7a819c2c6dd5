# Kafka消费者配置优化指南

## 概述

本文档介绍了优化后的Kafka消费者配置，支持通过YAML文件动态配置消费者数量和性能参数。

## 配置结构

### 主要配置参数

```yaml
spring:
  kafka:
    consumer:
      # 基础配置
      group-id: submerge-tracking-water-group
      auto-offset-reset: latest
      enable-auto-commit: false
      
      # 并发配置
      concurrency: 30                    # 主消费者并发数
      retry-concurrency: 10              # 重试消费者并发数
      max-poll-records: 500              # 每次拉取的最大记录数
      
      # 性能优化配置
      session-timeout-ms: 30000          # 会话超时时间
      heartbeat-interval-ms: 3000        # 心跳间隔
      max-poll-interval-ms: 300000       # 最大轮询间隔
      fetch-min-size: 1                  # 最小拉取字节数
      fetch-max-wait: 500                # 最大等待时间
```

## 环境配置建议

### 开发环境 (application-dev.yml)
- **concurrency**: 20 - 适中的并发数，便于调试
- **retry-concurrency**: 5 - 较低的重试并发
- **max-poll-records**: 300 - 适中的批处理大小

### 测试环境 (application-test.yml)
- **concurrency**: 30 - 模拟生产环境的中等负载
- **retry-concurrency**: 10 - 中等重试并发
- **max-poll-records**: 500 - 标准批处理大小

### 生产环境 (application-prod.yml)
- **concurrency**: 50 - 高并发处理能力
- **retry-concurrency**: 15 - 较高的重试处理能力
- **max-poll-records**: 1000 - 大批处理提高吞吐量

### VPN环境 (application-vpn.yml)
- **concurrency**: 25 - 中等并发数
- **retry-concurrency**: 8 - 适中的重试并发
- **max-poll-records**: 500 - 标准批处理大小

## 配置参数详解

### 并发配置
- **concurrency**: 控制主要业务消息的消费者线程数量
- **retry-concurrency**: 控制重试消息的消费者线程数量，通常设置为主并发数的1/3

### 性能配置
- **max-poll-records**: 每次从Kafka拉取的最大消息数，影响批处理效率
- **session-timeout-ms**: 消费者会话超时时间，影响故障检测速度
- **heartbeat-interval-ms**: 心跳间隔，应设置为session-timeout的1/3
- **max-poll-interval-ms**: 两次poll调用的最大间隔，防止消费者被踢出组
- **fetch-min-size**: 最小拉取字节数，影响网络效率
- **fetch-max-wait**: 等待数据的最大时间

## 配置验证

系统启动时会自动验证配置的合理性：
- 并发数必须大于0
- 心跳间隔必须小于会话超时时间的1/3
- 会话超时时间必须合理设置

## 监控和调优

### 关键指标
1. **消费延迟**: 监控消息处理延迟
2. **吞吐量**: 每秒处理的消息数量
3. **错误率**: 消息处理失败的比例
4. **资源使用**: CPU和内存使用情况

### 调优建议
1. **高吞吐量场景**: 增加concurrency和max-poll-records
2. **低延迟场景**: 减少max-poll-records，增加并发数
3. **资源受限**: 适当降低并发数和批处理大小
4. **网络不稳定**: 增加超时时间和重试配置

## 使用示例

### 动态调整并发数
只需修改对应环境的YAML文件中的concurrency值，重启应用即可生效。

### 添加新的消费者
1. 在KafkaConfig中添加新的容器工厂Bean
2. 在YAML中添加对应的配置参数
3. 在消费者类中使用新的容器工厂

## 注意事项

1. **并发数设置**: 不要超过Topic的分区数
2. **内存使用**: 高并发会增加内存使用，注意监控
3. **数据库连接**: 确保数据库连接池能支撑消费者并发数
4. **重试机制**: 重试消费者的并发数应适当控制，避免雪崩效应

## 故障排查

### 常见问题
1. **消费延迟高**: 检查并发数和批处理大小
2. **频繁重平衡**: 检查会话超时和心跳配置
3. **内存溢出**: 降低并发数或批处理大小
4. **连接超时**: 增加超时时间配置

### 日志监控
系统启动时会输出配置信息，便于确认配置是否正确加载。
