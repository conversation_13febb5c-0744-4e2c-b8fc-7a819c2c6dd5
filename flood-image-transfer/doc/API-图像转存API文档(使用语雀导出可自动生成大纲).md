**请求类型**: `application/json`

**响应格式**: 所有响应均使用标准化的 `ResVoT` 格式包装

## 接口列表
### 1. 同步上传 (Synchronous Upload)
**接口地址**: `/api/openAPI/sync/upload`

**请求类型**: `POST`

**接口描述**: 同步处理最多10条事件消息并返回即时结果。

#### 请求参数 （具体格式可参考topic：common_event_alarm 消息体结构）
| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| messageList | Array | 否 | 要处理的事件消息列表（最多10条） |
| --camera_index_code | string | 否 | 摄像头内码，如："CAM001" |
| --camera_channel_code | string | 否 | 摄像头通道码，如："CH001" |
| --camera_foreign_code | string | 是 | 摄像头外码，如："EXT001" |
| --event_time | string | 是 | 事件时间戳，格式：ISO8601，如："2025-07-17T10:30:00.000+08:00" |
| --source_system | string | 是 | 来源系统标识，如："flood_monitor" |
| --source_module | string | 否 | 来源模块标识，如："detection" |
| --info | string | 否 | 附加信息，如："Flood detected in area A" |
| --event_type | string | 是 | 事件类型，如："flood_alert" |
| --event_id | string | 是 | 唯一事件标识符，如："evt_20250717_001" |
| --image_url | string | 是 | 要下载的图片URL，如："[https://example.com/image1.jpg](https://example.com/image1.jpg)" |
| --video_url | string | 否 | 视频URL，不传或为空(本接口不对视频URL进行转存) |
| --image_base64 | string | 否 | Base64编码的图片数据，不传或为空(本接口不对base64类型图片进行转存) |
| --only_picture | integer | 否 | 仅处理图片标志，0或1，默认为1 |


#### 请求示例
```json
[
  {
    "camera_index_code": "",
    "camera_channel_code": "",
    "camera_foreign_code": "37017724001310204835",
    "event_time": "2025-07-16T09:27:30.000+08:00",
    "source_system": "flood_prevention",
    "source_module": "cv_person",
    "info": "",
    "event_type": "图片特征提取",
    "event_id": "QLrmh8GErkEejyQMpHIQ0XXIIwRIvich",
    "image_url": "https://100.192.5.35:18001/data/snapshot/20250716/09/37017724001310204835/f110b25618de83d08aa56d1dbaf156d0.jpg",
    "video_url": null,
    "image_base64": null,
    "only_picture": 1
  }
]
```

#### 响应参数
| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| flag | boolean | 请求是否成功，true为成功，false为失败 |
| message | string | 响应消息，成功时为"success" |
| code | string | 响应码，成功时为"200" |
| time | string | 响应时间戳，格式：yyyy-MM-dd HH:mm:ss |
| t | Array | 上传结果列表 |
| --eventId | string | 事件ID |
| --minioImageUrl | string | MinIO存储的图片URL |
| --disposeType | string | 处理类型，成功："success"，失败："error" |
| --retryCount | integer | 重试次数 |
| --createTime | string | 创建时间，格式：yyyy-MM-dd HH:mm:ss |
| --updateTime | string | 更新时间，格式：yyyy-MM-dd HH:mm:ss |
| --eventTime | string | 事件时间，格式：yyyy-MM-dd HH:mm:ss |
| --taskId | string | 任务ID |
| --cameraIndexCode | string | 摄像头内码 |
| --cameraChannelCode | string | 摄像头通道码 |
| --cameraForeignCode | string | 摄像头外码 |
| --originImageUrl | string | 原始图片URL |


#### 响应示例
**成功响应 (200)**:

```json
{
  "flag": true,
  "message": "success",
  "code": "200",
  "time": "2025-07-17 10:30:15",
  "t": [
    {
      "eventId": "evt_20250717_001",
      "minioImageUrl": "http://minio.example.com/bucket/image1.jpg",
      "disposeType": "success",
      "retryCount": 0,
      "createTime": "2025-07-17 10:30:10",
      "updateTime": "2025-07-17 10:30:15",
      "eventTime": "2025-07-17 10:30:00",
      "taskId": null,
      "cameraIndexCode": "CAM001",
      "cameraChannelCode": "CH001",
      "cameraForeignCode": "EXT001",
      "originImageUrl": "https://example.com/image1.jpg"
    }
  ]
}
```

### 2. 异步上传 (Asynchronous Upload)
**接口地址**: `/api/openAPI/async/upload`

**请求类型**: `POST`

**接口描述**: 异步处理最多1000条事件消息并返回任务ID用于状态跟踪。

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| messageList | Array | 是 | 要处理的事件消息列表（最多1000条） |
| --camera_index_code | string | 是 | 摄像头内码，如："CAM001" |
| --camera_channel_code | string | 是 | 摄像头通道码，如："CH001" |
| --camera_foreign_code | string | 是 | 摄像头外码，如："EXT001" |
| --event_time | string | 是 | 事件时间戳，格式：ISO8601，如："2025-07-17T10:30:00.000+08:00" |
| --source_system | string | 是 | 来源系统标识，如："flood_monitor" |
| --source_module | string | 否 | 来源模块标识，如："detection" |
| --info | string | 否 | 附加信息，如："Flood detected in area A" |
| --event_type | string | 是 | 事件类型，如："flood_alert" |
| --event_id | string | 是 | 唯一事件标识符，如："evt_20250717_001" |
| --image_url | string | 是 | 要下载的图片URL，如："[https://example.com/image1.jpg](https://example.com/image1.jpg)" |
| --video_url | string | 否 | 视频URL，不传或为空(本接口不对视频URL进行转存) |
| --image_base64 | string | 否 | Base64编码的图片数据 不传或为空(本接口不对Base64编码的图片数据进行转存) |
| --only_picture | integer | 否 | 仅处理图片标志，0或1，默认为1 |


#### 请求示例
```json
[
  {
    "camera_index_code": "",
    "camera_channel_code": "",
    "camera_foreign_code": "37017724001310204835",
    "event_time": "2025-07-16T09:27:30.000+08:00",
    "source_system": "flood_prevention",
    "source_module": "cv_person",
    "info": "",
    "event_type": "图片特征提取",
    "event_id": "QLrmh8GErkEejyQMpHIQ0XXIIwRIvich",
    "image_url": "https://100.192.5.35:18001/data/snapshot/20250716/09/37017724001310204835/f110b25618de83d08aa56d1dbaf156d0.jpg",
    "video_url": null,
    "image_base64": null,
    "only_picture": 1
  }
]
```

#### 响应参数
| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| flag | boolean | 请求是否成功，true为成功，false为失败 |
| message | string | 响应消息，成功时为"success" |
| code | string | 响应码，成功时为"200" |
| time | string | 响应时间戳，格式：yyyy-MM-dd HH:mm:ss |
| t | string | 异步任务ID，用于后续查询任务状态 |


#### 响应示例
**成功响应 (200)**:

```json
{
  "flag": true,
  "message": "success",
  "code": "200",
  "time": "2025-07-17 10:30:15",
  "t": "task_20250717_103015_abc123"
}
```

### 3. 异步任务状态查询 (Async Task Status Query)
**接口地址**: `/api/openAPI/async/status/{taskId}`

**请求类型**: `GET`

**接口描述**: 查询异步任务的处理状态和结果。

#### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| taskId | string | 是 | 异步上传返回的任务ID，如："task_20250717_103015_abc123" |


#### 请求示例
```plain
GET /api/openAPI/async/status/task_20250717_103015_abc123
```

#### 响应参数
| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| flag | boolean | 请求是否成功，true为成功，false为失败 |
| message | string | 响应消息，成功时为"success" |
| code | string | 响应码，成功时为"200" |
| time | string | 响应时间戳，格式：yyyy-MM-dd HH:mm:ss |
| t | Array | 任务处理结果列表 |
| --eventId | string | 事件ID |
| --minioImageUrl | string | MinIO存储的图片URL |
| --disposeType | string | 处理类型，成功："success"，失败："error" |
| --retryCount | integer | 重试次数 |
| --createTime | string | 创建时间，格式：yyyy-MM-dd HH:mm:ss |
| --updateTime | string | 更新时间，格式：yyyy-MM-dd HH:mm:ss |
| --eventTime | string | 事件时间，格式：yyyy-MM-dd HH:mm:ss |
| --taskId | string | 任务ID |
| --cameraIndexCode | string | 摄像头内码 |
| --cameraChannelCode | string | 摄像头通道码 |
| --cameraForeignCode | string | 摄像头外码 |
| --originImageUrl | string | 原始图片URL |


#### 响应示例
**成功响应 (200)**:

```json
{
  "flag": true,
  "message": "success",
  "code": "200",
  "time": "2025-07-17 10:35:20",
  "t": [
    {
      "eventId": "evt_20250717_001",
      "minioImageUrl": "http://minio.example.com/bucket/image1.jpg",
      "disposeType": "success",
      "retryCount": 0,
      "createTime": "2025-07-17 10:30:10",
      "updateTime": "2025-07-17 10:35:15",
      "eventTime": "2025-07-17 10:30:00",
      "taskId": "task_20250717_103015_abc123",
      "cameraIndexCode": "CAM001",
      "cameraChannelCode": "CH001",
      "cameraForeignCode": "EXT001",
      "originImageUrl": "https://example.com/image1.jpg"
    }
  ]
}
```

### 4. 根据事件ID查询结果 (Query Result by Event ID)
**接口地址**: `/api/openAPI/queryResult/{eventId}`

**请求类型**: `GET`

**接口描述**: 查询指定事件ID的详细结果信息。

#### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| eventId | string | 是 | 唯一事件标识符，如："evt_20250717_001" |


#### 请求示例
```plain
GET /api/openAPI/queryResult/evt_20250717_001
```

#### 响应参数
| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| flag | boolean | 请求是否成功，true为成功，false为失败 |
| message | string | 响应消息，成功时为"success" |
| code | string | 响应码，成功时为"200" |
| time | string | 响应时间戳，格式：yyyy-MM-dd HH:mm:ss |
| t | CommonEventResourceLast | 事件资源详细信息对象 |
| --camera_foreign_code | string | 摄像头外码 |
| --event_time | string | 事件时间，格式：yyyy-MM-dd HH:mm:ss |
| --start_time | string | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| --end_time | string | 结束时间，格式：yyyy-MM-dd HH:mm:ss |
| --event_id | string | 事件ID |
| --camera_index_code | string | 摄像头内码 |
| --camera_channel_code | string | 摄像头通道码 |
| --image_msg | string | 图片处理消息 |
| --video_msg | string | 视频处理消息 |
| --image_retry | integer | 图片重试次数 |
| --video_retry | integer | 视频重试次数 |
| --status | string | 处理状态，如："completed"、"processing"、"failed" |
| --duration | integer | 处理持续时间（秒） |
| --info | string | 事件信息描述 |
| --source_system | string | 来源系统 |
| --system_module | string | 系统模块 |
| --minio_video_url | string | MinIO存储的视频URL |
| --minio_image_url | string | MinIO存储的图片URL |
| --accept_time | string | 接收时间，格式：yyyy-MM-dd HH:mm:ss |
| --partition | integer | Kafka分区号 |
| --offset | integer | Kafka偏移量 |
| --sink_time | string | 落库时间，格式：yyyy-MM-dd HH:mm:ss |


#### 响应示例
**成功响应 (200)**:

```json
{
  "flag": true,
  "message": "success",
  "code": "200",
  "time": "2025-07-17 10:40:25",
  "t": {
    "camera_foreign_code": "EXT001",
    "event_time": "2025-07-17 10:30:00",
    "start_time": "2025-07-17 10:30:00",
    "end_time": "2025-07-17 10:35:00",
    "event_id": "evt_20250717_001",
    "camera_index_code": "CAM001",
    "camera_channel_code": "CH001",
    "image_msg": "Image processed successfully",
    "video_msg": "Video processed successfully",
    "image_retry": 0,
    "video_retry": 0,
    "status": "completed",
    "duration": 300,
    "info": "Flood detected in area A",
    "source_system": "flood_monitor",
    "system_module": "detection",
    "minio_video_url": "http://minio.example.com/bucket/video1.mp4",
    "minio_image_url": "http://minio.example.com/bucket/image1.jpg",
    "accept_time": "2025-07-17 10:30:05",
    "partition": 1,
    "offset": 12345,
    "sink_time": "2025-07-17 18:04:26"
  }
}
```

### 5. 分页查询结果 (Paginated Query Results)
**接口地址**: `/api/openAPI/queryResultList`

**请求类型**: `GET`

**接口描述**: 分页查询结果，支持按摄像头编码和时间范围过滤。

#### 查询参数
| 参数名 | 类型 | 必填 | 验证规则 | 说明 |
| --- | --- | --- | --- | --- |
| page | integer | 否（默认：1） | 最小值：1，最大值：10000 | 页码（从1开始），如：1 |
| pageSize | integer | 否（默认：10） | 最小值：1，最大值：1000 | 每页记录数，如：20 |
| cameraForeignCode | string | 否 | 最大长度：100 | 摄像头外码（精确匹配），如："EXT001" |
| cameraIndexCode | string | 否 | 最大长度：100 | 摄像头内码（精确匹配），如："CAM001" |
| startTime | string | 否 | 格式：yyyy-MM-dd HH:mm:ss | 查询开始时间，如："2025-07-17 09:00:00" |
| endTime | string | 否 | 格式：yyyy-MM-dd HH:mm:ss | 查询结束时间，如："2025-07-17 18:00:00" |


#### 验证规则
+ **时间格式**: `startTime` 和 `endTime` 都必须遵循 `yyyy-MM-dd HH:mm:ss` 格式
+ **时间逻辑**: 如果同时提供两个时间参数，`startTime` 必须早于 `endTime`
+ **分页限制**: 页码不能超过10,000，页大小不能超过1,000

#### 请求示例
**基础分页**:

```plain
GET /api/openAPI/queryResultList?page=1&pageSize=20
```

**按摄像头编码过滤**:

```plain
GET /api/openAPI/queryResultList?cameraForeignCode=EXT001&cameraIndexCode=CAM001&page=1&pageSize=10
```

**按时间范围过滤**:

```plain
GET /api/openAPI/queryResultList?startTime=2025-07-17 09:00:00&endTime=2025-07-17 18:00:00&page=1&pageSize=50
```

**组合过滤条件**:

```plain
GET /api/openAPI/queryResultList?cameraForeignCode=outdoor&startTime=2025-07-17 10:00:00&endTime=2025-07-17 18:00:00&page=1&pageSize=20
```

#### 响应参数
| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| flag | boolean | 请求是否成功，true为成功，false为失败 |
| message | string | 响应消息，成功时为"查询成功" |
| code | string | 响应码，成功时为"200" |
| time | string | 响应时间戳，格式：yyyy-MM-dd HH:mm:ss |
| t | IPage | 分页结果对象 |
| --records | Array | 当前页的记录列表 |
| ----camera_foreign_code | string | 摄像头外码 |
| ----event_time | string | 事件时间，格式：yyyy-MM-dd HH:mm:ss |
| ----start_time | string | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| ----end_time | string | 结束时间，格式：yyyy-MM-dd HH:mm:ss |
| ----event_id | string | 事件ID |
| ----camera_index_code | string | 摄像头内码 |
| ----camera_channel_code | string | 摄像头通道码 |
| ----image_msg | string | 图片处理消息 |
| ----video_msg | string | 视频处理消息 |
| ----image_retry | integer | 图片重试次数 |
| ----video_retry | integer | 视频重试次数 |
| ----status | string | 处理状态，如："completed"、"processing"、"failed" |
| ----duration | integer | 处理持续时间（秒） |
| ----info | string | 事件信息描述 |
| ----source_system | string | 来源系统 |
| ----system_module | string | 系统模块 |
| ----minio_video_url | string | MinIO存储的视频URL |
| ----minio_image_url | string | MinIO存储的图片URL |
| ----accept_time | string | 接收时间，格式：yyyy-MM-dd HH:mm:ss |
| ----partition | integer | Kafka分区号 |
| ----offset | integer | Kafka偏移量 |
| ----sink_time | string | 落库时间，格式：yyyy-MM-dd HH:mm:ss |
| --total | integer | 总记录数 |
| --size | integer | 每页大小 |
| --current | integer | 当前页码 |
| --pages | integer | 总页数 |


#### 响应示例
**成功响应 (200)**:

```json
{
  "flag": true,
  "message": "查询成功",
  "code": "200",
  "time": "2025-07-17 10:45:30",
  "t": {
    "records": [
      {
        "camera_foreign_code": "EXT001",
        "event_time": "2025-07-17 10:30:00",
        "start_time": "2025-07-17 10:30:00",
        "end_time": "2025-07-17 10:35:00",
        "event_id": "evt_20250717_001",
        "camera_index_code": "CAM001",
        "camera_channel_code": "CH001",
        "image_msg": "Image processed successfully",
        "video_msg": "Video processed successfully",
        "image_retry": 0,
        "video_retry": 0,
        "status": "completed",
        "duration": 300,
        "info": "Flood detected in area A",
        "source_system": "flood_monitor",
        "system_module": "detection",
        "minio_video_url": "http://minio.example.com/bucket/video1.mp4",
        "minio_image_url": "http://minio.example.com/bucket/image1.jpg",
        "accept_time": "2025-07-17 10:30:05",
        "partition": 1,
        "offset": 12345,
        "sink_time": "2025-07-17 18:04:26"
      }
    ],
    "total": 1,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

## 通用响应格式
所有API接口都返回使用标准化 `ResVoT` 格式包装的响应：

### 成功响应结构
```json
{
  "flag": true,           // Boolean: 成功时为true，错误时为false
  "message": "string",    // String: 响应消息
  "code": "200",         // String: 响应码（成功时为"200"）
  "time": "2025-07-17 10:30:15",  // String: 响应时间戳（GMT+8）
  "t": {}                // Generic: 响应数据（根据接口而异）
}
```

### 错误响应结构
```json
{
  "flag": false,         // Boolean: 错误时为false
  "message": "string",   // String: 错误消息
  "code": "500",        // String: 错误码
  "time": "2025-07-17 10:30:15",  // String: 响应时间戳（GMT+8）
  "t": null             // Generic: 错误时通常为null，可能包含上下文信息
}
```

## HTTP状态码
| 状态码 | 描述 | 使用场景 |
| --- | --- | --- |
| 200 | 成功 | 所有成功的请求（即使业务逻辑错误也返回200，但flag=false） |
| 400 | 请求错误 | 无效的请求参数或验证失败 |
| 500 | 内部服务器错误 | 系统错误、数据库故障或意外异常 |


## 错误处理
API使用全局异常处理器来捕获和标准化所有错误：

### 错误响应示例
**验证错误**:

```json
{
  "flag": false,
  "message": "参数验证失败",
  "code": "400",
  "time": "2025-07-17 10:30:15",
  "t": {
    "startTime": "开始时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式",
    "endTime": "结束时间必须晚于开始时间"
  }
}
```

**业务逻辑错误**:

```json
{
  "flag": false,
  "message": "业务处理失败",
  "code": "500",
  "time": "2025-07-17 10:30:15",
  "t": null
}
```

**系统错误**:

```json
{
  "flag": false,
  "message": "系统运行时异常: Database connection failed",
  "code": "500",
  "time": "2025-07-17 10:30:15",
  "t": null
}
```

## 速率限制
API为图片下载操作实现了基于令牌桶的速率限制：

+ 速率限制按图片服务器端点应用
+ 速率限制检查失败会导致自动重试和指数退避

## 性能考虑
+ **同步上传**: 推荐用于≤10条消息且需要立即结果的场景
+ **异步上传**: 推荐用于大批量处理（最多1000条消息）
+ **查询操作**: 对于大结果集使用分页
+ **时间范围查询**: 较窄的时间范围可提高查询性能

